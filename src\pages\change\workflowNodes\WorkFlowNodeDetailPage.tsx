import {
  AdvancedFilterMappingType,
  KanbanButton,
  getDefaultTableAffected,
  KanbanIconButton,
  KanbanSelect,
  KanbanTextarea,
  KanbanTitle,
  TableAffactedSafeType,
  KanbanInput,
} from 'kanban-design-system';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { useDisclosure } from '@mantine/hooks';
import { Box, Flex, SimpleGrid } from '@mantine/core';
import { zodResolver } from '@hookform/resolvers/zod';
import customStyled from '@resources/styles/Common.module.scss';
import { IconArrowLeft } from '@tabler/icons-react';
import { Controller, useForm, UseFormReturn } from 'react-hook-form';
import UnsaveConfirmModal from '@components/utils/ConfirmMessageUtils';
import { buildUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import { COMMON_DESCRIPTION_MAX_LENGTH, COMMON_MAX_LENGTH } from '@common/constants/ValidationConstant';
import equal from 'fast-deep-equal';
import { buildNavigateState } from '@core/hooks/useSavedFilterCycle';
import { ChangeWorkflowNodeApi } from '@api/ChangeWorkflowNodeApi';
import { ChangeWorkflowNodeModel, ChangeWorkflowNodeModelSchema } from '@models/ChangeWorkflowNodeModel';
import { ChangeApplicationTypeEnum } from '@common/constants/ChangeApplicationConstants';
import JenkinsAuthentication from './JenkinsAuthentication';
import WLAAuthentication from './WLAAuthentication';
import { ChangeWorkflowNodePageFilter } from './WorkFlowNodePage';
import { ChangeWorkflowNode } from '@core/schema/ChangeWorkflowNode';
import { useBreadcrumbEntityName } from '@core/hooks/useBreadcrumbEntityName';
import { CIConfigMapperHandle } from './CIConfigMapper';

const DEFAULT_FORM_VALUE: ChangeWorkflowNodeModel = {
  nodeName: '',
  description: '',
};

type DetailPageProps = {
  idProp?: number;
};
type SaveButtonProps = {
  form: UseFormReturn<ChangeWorkflowNodeModel>;
  hasAuthenticationChanged: boolean;
  ciConfigRef: React.RefObject<CIConfigMapperHandle>;
};
const SaveButton = ({ ciConfigRef, form, hasAuthenticationChanged }: SaveButtonProps) => {
  const navigate = useNavigate();
  const { mutate: saveUserMutate } = useMutate(ChangeWorkflowNodeApi.save, {
    successNotification: 'Node saved successfully',
    onSuccess: () => {
      navigate(buildUrl(ROUTE_PATH.CONFIGURATION_CHANGE_WORKFLOW_NODE), { state: buildNavigateState({ fromDetail: true }) });
    },
  });

  const onSubmit = useCallback(() => {
    const isConfigValid = ciConfigRef.current?.validate();
    if (!isConfigValid) {
      return;
    }
    form.trigger();
    const parsedData = ChangeWorkflowNodeModelSchema.safeParse(form.getValues());
    if (parsedData.success) {
      const trimmed = parsedData.data;
      if (trimmed.id && !hasAuthenticationChanged) {
        trimmed.authentication = undefined;
      }
      trimmed.isChange = hasAuthenticationChanged;
      saveUserMutate(trimmed);
    }
  }, [ciConfigRef, form, hasAuthenticationChanged, saveUserMutate]);

  return (
    <KanbanButton size='xs' onClick={onSubmit}>
      Save
    </KanbanButton>
  );
};

const initOrUpdatedFilterPayloads = (prev?: TableAffactedSafeType<ChangeWorkflowNode>, initFilters?: ChangeWorkflowNodePageFilter) => {
  const prevFilter = prev || getDefaultTableAffected();
  if (initFilters) {
    const filterObj: AdvancedFilterMappingType<ChangeWorkflowNode> = {
      ['nodeName']: {
        filterOption: 'equals',
        value: {
          fromValue: initFilters.nodeName,
        },
      },
      ['application']: {
        filterOption: 'equals',
        value: {
          fromValue: initFilters.application,
        },
      },
    };
    const advancedFilterMapping = { ...prevFilter.advancedFilterMapping, ...filterObj };
    return { ...prevFilter, advancedFilterMapping: { ...advancedFilterMapping } };
  }
  return prevFilter;
};

export const WorkFlowNodeDetailPage: React.FC<DetailPageProps> = () => {
  const id = Number(useParams().id);
  const { data: dataNode } = useFetch(ChangeWorkflowNodeApi.findById(id), { enabled: !!id });
  const [changeWorkflowNode, setChangeWorkflowNode] = useState<ChangeWorkflowNodeModel>();
  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);
  const [hasAuthenticationChanged, setHasAuthenticationChanged] = useState(false);
  useBreadcrumbEntityName(changeWorkflowNode?.nodeName);

  const navigate = useNavigate();
  const [inputFilters, setInputFilters] = useState<ChangeWorkflowNodePageFilter>({});
  const [tableAffectedChange, setTableAffectedChange] = useState<TableAffactedSafeType>();
  const form = useForm<ChangeWorkflowNodeModel>({
    defaultValues: DEFAULT_FORM_VALUE,
    resolver: zodResolver(ChangeWorkflowNodeModelSchema),
    mode: 'onChange',
  });
  const ciConfigRef = useRef<CIConfigMapperHandle>(null);

  const [errorName, setErrorName] = useState<boolean>(false);

  const { data: dataSearch } = useFetch(ChangeWorkflowNodeApi.findAllWithPage(initOrUpdatedFilterPayloads(tableAffectedChange, inputFilters)), {
    enabled: !!tableAffectedChange,
  });
  const { control } = form;
  const application = form.watch('application');

  useEffect(() => {
    const nameSearchExists: boolean = dataSearch?.data?.content.some((obj) => obj.nodeName === inputFilters.nodeName) || false;
    setErrorName(nameSearchExists);
  }, [dataSearch?.data?.content, inputFilters.nodeName]);

  useEffect(() => {
    if (id && dataNode?.data) {
      setChangeWorkflowNode(dataNode.data);
      form.reset(dataNode.data);
      setHasAuthenticationChanged(false);
    }
  }, [dataNode?.data, form, id]);

  const handleApplicationChange = (value: ChangeApplicationTypeEnum) => {
    if (value !== null) {
      form.setValue('application', value);
      form.setValue('authentication', '');
      form.setValue('config', '');
      setHasAuthenticationChanged(true);
      form.setValue('isChange', true);
      form.trigger('application');
    }
  };

  const handleExistWithoutSave = () => {
    const currentValues = form.getValues();
    if (!equal(currentValues, changeWorkflowNode)) {
      openModal();
    } else {
      navigate(buildUrl(ROUTE_PATH.CONFIGURATION_CHANGE_WORKFLOW_NODE), { state: buildNavigateState({ fromDetail: true }) });
    }
  };

  const handleSearch = useCallback(
    (filters?: ChangeWorkflowNodePageFilter) => {
      const appliedFilters = filters ?? inputFilters;
      setTableAffectedChange((prev) => {
        return initOrUpdatedFilterPayloads(prev, appliedFilters);
      });
    },
    [inputFilters],
  );

  return (
    <Box>
      <HeaderTitleComponent
        title=''
        rightSection={
          <Flex direction='row' align='center' gap='xs'>
            <KanbanButton size='xs' variant='light' onClick={handleExistWithoutSave}>
              Cancel
            </KanbanButton>
            <SaveButton form={form} hasAuthenticationChanged={hasAuthenticationChanged} ciConfigRef={ciConfigRef} />
          </Flex>
        }
        leftSection={
          <Flex direction='row' align='center' gap='xs'>
            <KanbanIconButton size='sm' variant='subtle' onClick={handleExistWithoutSave}>
              <IconArrowLeft />
            </KanbanIconButton>
            <KanbanTitle fz='h4'>{id ? 'Edit Node' : 'Add Node'}</KanbanTitle>
          </Flex>
        }
      />
      <Box className={customStyled.elementBorder}>
        <SimpleGrid cols={2} spacing='md'>
          <Controller
            name='nodeName'
            control={control}
            render={({ field, fieldState }) => (
              <KanbanInput
                label='Node Name'
                {...field}
                value={field.value || ''}
                withAsterisk
                error={errorName ? 'This Node Name is exist' : fieldState.error?.message}
                maxLength={COMMON_MAX_LENGTH}
                onBlur={(data) => {
                  if (dataNode?.data?.nodeName !== data.target.value.trim()) {
                    setInputFilters({ nodeName: data.target.value.trim() });
                    handleSearch();
                  }
                }}
              />
            )}
          />
          <Controller
            name='description'
            control={control}
            render={({ field, fieldState }) => (
              <KanbanTextarea
                minRows={4}
                maxLength={COMMON_DESCRIPTION_MAX_LENGTH}
                label='Description'
                {...field}
                value={field.value || ''}
                error={fieldState?.error?.message}
              />
            )}
          />
        </SimpleGrid>
        <SimpleGrid cols={2} spacing='md'>
          <Controller
            name='application'
            control={control}
            render={({ field, fieldState }) => (
              <KanbanSelect
                label='Application'
                allowDeselect={false}
                required
                data={Object.values(ChangeApplicationTypeEnum).map((enumValue) => ({
                  value: `${enumValue}`,
                  label: `${enumValue}`,
                }))}
                value={field.value}
                onChange={(value) => handleApplicationChange(value as ChangeApplicationTypeEnum)}
                onBlur={field.onBlur}
                error={fieldState?.error?.message}
              />
            )}
          />
        </SimpleGrid>
        {ChangeApplicationTypeEnum.JENKINS === application && (
          <JenkinsAuthentication form={form} onChangeDetected={() => setHasAuthenticationChanged(true)} ciConfigRef={ciConfigRef} />
        )}
        {ChangeApplicationTypeEnum.WLA === application && (
          <WLAAuthentication form={form} onChangeDetected={() => setHasAuthenticationChanged(true)} ciConfigRef={ciConfigRef} />
        )}
      </Box>

      <UnsaveConfirmModal
        opened={openedModal}
        onClose={closeModal}
        onConfirm={() => navigate(buildUrl(ROUTE_PATH.CONFIGURATION_CHANGE_WORKFLOW_NODE), { state: buildNavigateState({ fromDetail: true }) })}
      />
    </Box>
  );
};

export default WorkFlowNodeDetailPage;
