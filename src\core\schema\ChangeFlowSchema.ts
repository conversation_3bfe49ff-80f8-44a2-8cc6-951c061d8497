import { string, z } from 'zod';
import { COMMON_DESCRIPTION_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { INPUT_REQUIRE } from '@core/message/MesageConstant';
import { ChangeFlowNodeTypeEnum } from '@models/ChangeFlowModel';
import { AuditSchema } from './Common';

export const ChangeFlowNodeGroupSchema = z.object({
  id: z.string(),
  name: z.string(),
});
export type ChangeFlowNodeGroup = z.infer<typeof ChangeFlowNodeGroupSchema>;

export const ChangeFlowNodeSchema = z.object({
  id: z.number().nullish(),
  nodeId: z.string(),
  name: z.string().trim().min(1, { message: INPUT_REQUIRE }),
  groups: z.array(ChangeFlowNodeGroupSchema),
  nodeLevel: z.number(),
  type: ChangeFlowNodeTypeEnum,
});
export type ChangeFlowNode = z.infer<typeof ChangeFlowNodeSchema>;

export const ChangeFlowNotificationSchema = z.object({
  emailTemplateId: z.number().nullable(),
  notifyTo: z.array(string()).optional(),
  notifyNode: z.array(string()).optional(),
  statusId: z.string().optional(),
});

export const ChangeFlowSchema = z
  .object({
    id: z.number(),
    name: z.string().trim().min(1, { message: INPUT_REQUIRE }),
    description: z.string().max(COMMON_DESCRIPTION_MAX_LENGTH).nullish(),
    flowNodes: z.string(),
    flowEdges: z.string(),
    nodes: z.array(ChangeFlowNodeSchema).nullish(),
    notifications: z.array(ChangeFlowNotificationSchema).nullish(),
  })
  .merge(AuditSchema);
export type ChangeFlow = z.infer<typeof ChangeFlowSchema>;
