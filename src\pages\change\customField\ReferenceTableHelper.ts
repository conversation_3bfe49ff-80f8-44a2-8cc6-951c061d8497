import { QueryBuilderOperatorEnum } from '@components/queryBuilder/QueryBuilderOperatorEnum';
import { QueryBuilderField } from '@components/queryBuilder';

export enum ValueType {
  VALUE = 'value',
  REFERENCE = 'reference',
}

export const mockReferenceTables = new Map<
  string,
  {
    referenceField: string;
    fields: { fieldName: string; fieldType: 'string' | 'number' | 'boolean' }[];
  }
>([
  [
    'SYS_USER',
    {
      referenceField: 'USERNAME',
      fields: [
        { fieldName: 'CENTER', fieldType: 'string' },
        { fieldName: 'DEPARTMENT', fieldType: 'string' },
        { fieldName: 'USERNAME', fieldType: 'string' },
      ],
    },
  ],
  [
    'SYS_GROUP',
    {
      referenceField: 'NAME',
      fields: [
        { fieldName: 'NAME', fieldType: 'string' },
        { fieldName: 'DESCRIPTION', fieldType: 'string' },
      ],
    },
  ],
  [
    'SYS_ROLE',
    {
      referenceField: 'NAME',
      fields: [
        { fieldName: 'NAME', fieldType: 'string' },
        { fieldName: 'DESCRIPTION', fieldType: 'string' },
      ],
    },
  ],
]);

export const getQueryFieldsFromReference = (tableName?: string): QueryBuilderField[] => {
  const table = mockReferenceTables.get(tableName || '');

  if (!table) {
    return [];
  }

  return table.fields.map((field) => ({
    name: field.fieldName,
    label: field.fieldName,
    placeholder: `Please enter ${field.fieldName}`,
    inputType: getInputType(field.fieldType),
    operators: Object.values(getOperators(field.fieldType)) as QueryBuilderOperatorEnum[],
  }));
};

const getInputType = (fieldType: 'string' | 'number' | 'boolean') => {
  switch (fieldType) {
    case 'number':
      return 'number';
    case 'boolean':
      return 'checkbox';
    default:
      return 'text';
  }
};

const getOperators = (fieldType: 'string' | 'number' | 'boolean'): QueryBuilderOperatorEnum[] => {
  switch (fieldType) {
    case 'string':
      return [
        QueryBuilderOperatorEnum.IS,
        QueryBuilderOperatorEnum.IS_NOT,
        QueryBuilderOperatorEnum.CONTAINS,
        QueryBuilderOperatorEnum.DOES_NOT_CONTAIN,
        QueryBuilderOperatorEnum.BEGINS_WITH,
        QueryBuilderOperatorEnum.ENDS_WITH,
        QueryBuilderOperatorEnum.IS_NULL_OR_EMPTY,
        QueryBuilderOperatorEnum.IS_NOT_NULL_OR_NOT_EMPTY,
      ];
    case 'number':
      return [
        QueryBuilderOperatorEnum.IS,
        QueryBuilderOperatorEnum.IS_NOT,
        QueryBuilderOperatorEnum.GREATER_THAN,
        QueryBuilderOperatorEnum.GREATER_THAN_OR_EQUAL,
        QueryBuilderOperatorEnum.LESS_THAN,
        QueryBuilderOperatorEnum.LESS_THAN_OR_EQUAL,
        QueryBuilderOperatorEnum.IS_NULL,
        QueryBuilderOperatorEnum.IS_NOT_NULL,
      ];
    default:
      return [];
  }
};

export const getReferenceFieldFromTable = (tableName: string): string | undefined => {
  const table = mockReferenceTables.get(tableName);
  return table?.referenceField;
};
