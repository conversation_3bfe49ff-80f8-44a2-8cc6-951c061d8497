import { BaseURL } from '@common/constants/BaseUrl';
import { RequestConfig } from '@core/api';
import { PaginationRequest } from './Type';
import { createRequest } from './Utils';
import { tableAffectedToMultiColumnFilterPaginationRequestModel } from '@common/utils/TableUtils';
import { TableAffactedSafeType } from 'kanban-design-system';
import { ResponseData, createResponseSchema, Page, createPageSchema } from '@core/schema/Common';
import { CustomField, CustomFieldMentionListSchema, CustomFieldSchema, PicklistOptionSchema } from '@core/schema/CustomField';
import { CustomFieldConditionModel, CustomFieldModel } from '@models/CustomField';
import { z } from 'zod';

export class CustomFieldApi {
  static findAll(pagination: TableAffactedSafeType): RequestConfig<ResponseData<Page<CustomField>>, PaginationRequest> {
    return {
      url: `${BaseURL.customField}/all`,
      method: 'POST',
      schema: createResponseSchema(createPageSchema(CustomFieldSchema)),
      data: tableAffectedToMultiColumnFilterPaginationRequestModel(pagination),
    };
  }

  static findById(id: number) {
    return createRequest({
      url: `${BaseURL.customField}/:id`,
      method: 'GET',
      schema: createResponseSchema(CustomFieldSchema),
      pathVariable: {
        id,
      },
    });
  }

  static save(user: CustomFieldModel): RequestConfig<ResponseData<CustomField>> {
    return {
      url: `${BaseURL.customField}`,
      method: 'POST',
      schema: createResponseSchema(CustomFieldSchema),
      data: user,
    };
  }

  static deleteById(id: number): RequestConfig<ResponseData<void>> {
    return {
      url: `${BaseURL.customField}/${id}`,
      method: 'DELETE',
    };
  }

  static existsByName(id: number | undefined, name: string) {
    return createRequest({
      url: `${BaseURL.customField}/${id}/exists?name=${name}`,
      method: 'GET',
      schema: createResponseSchema(z.boolean()),
    });
  }

  static condition({ pagination, payload }: { payload: CustomFieldConditionModel; pagination: TableAffactedSafeType }) {
    return createRequest({
      url: `${BaseURL.customField}/preview-picklist`,
      method: 'POST',
      schema: createResponseSchema(createPageSchema(PicklistOptionSchema)),
      data: payload,
      params: tableAffectedToMultiColumnFilterPaginationRequestModel(pagination),
    });
  }

  static findAllAvaiableFields(pagination: TableAffactedSafeType) {
    return createRequest({
      url: `${BaseURL.customField}/all/detail`,
      method: 'POST',
      schema: createResponseSchema(createPageSchema(CustomFieldSchema)),
      data: tableAffectedToMultiColumnFilterPaginationRequestModel(pagination),
    });
  }

  static findPickListOptions(id: number, isReference: boolean, pagination: TableAffactedSafeType) {
    return createRequest({
      url: `${BaseURL.customField}/${id}/picklist-options?isReference=${isReference}`,
      method: 'POST',
      schema: createResponseSchema(createPageSchema(PicklistOptionSchema)),
      data: tableAffectedToMultiColumnFilterPaginationRequestModel(pagination),
    });
  }
  static findAllAvailableFieldsByName(search: string) {
    return createRequest({
      url: `${BaseURL.customField}/availables`,
      method: 'GET',
      params: { name: search },
      schema: createResponseSchema(CustomFieldMentionListSchema),
    });
  }
}
