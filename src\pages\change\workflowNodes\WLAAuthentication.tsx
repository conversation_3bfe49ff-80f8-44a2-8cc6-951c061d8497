import { KanbanInput } from 'kanban-design-system';
import React, { useState } from 'react';
import { SimpleGrid } from '@mantine/core';
import { Controller, UseFormReturn } from 'react-hook-form';
import { ChangeWorkflowNodeModel, WLAAuthenticationModel } from '@models/ChangeWorkflowNodeModel';
import { COMMON_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { IconEye, IconEyeOff } from '@tabler/icons-react';
import { INPUT_REQUIRE } from '@core/message/MesageConstant';
import CIConfigMapper, { CIConfigMapperHandle } from './CIConfigMapper';

type WLAAuthenticationProps = {
  form: UseFormReturn<ChangeWorkflowNodeModel>;
  onChangeDetected?: () => void;
  ciConfigRef: React.RefObject<CIConfigMapperHandle>;
};

export const WLAAuthentication: React.FC<WLAAuthenticationProps> = (props) => {
  const { control, setValue } = props.form;
  const { onChangeDetected } = props;
  const id = props.form.watch('id');
  const [hasChanged, setHasChanged] = useState(false);
  const [requireValidation, setRequireValidation] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [hasErrorUrl, setHasErrorUrl] = useState(false);
  const [hasErrorApiKey, setHasErrorApiKey] = useState(false);
  return (
    <Controller
      name='authentication'
      control={control}
      rules={{
        validate: (value) => {
          if (!hasChanged || !requireValidation) {
            return true;
          }
          if (!value) {
            return 'Authentication data is required';
          }
          try {
            const auth: WLAAuthenticationModel = JSON.parse(value);
            if (!auth.url?.trim()) {
              return 'Url is required';
            }
            if (!auth.apiKey?.trim()) {
              return 'API Key is required';
            }
            return true;
          } catch {
            return 'Invalid authentication JSON data';
          }
        },
      }}
      render={({ field }) => {
        let auth: WLAAuthenticationModel = { url: '', apiKey: '' };
        try {
          auth = field.value ? JSON.parse(field.value) : auth;
          const errorMessage = props.form.formState.errors.authentication?.message;
          if (errorMessage) {
            setHasErrorUrl(!auth.url);
            setHasErrorApiKey(!auth.apiKey);
          }
        } catch (e) {
          console.error('Invalid authentication JSON:', field.value);
        }
        const handleChange = (key: keyof WLAAuthenticationModel, value: string) => {
          const trimmedValue = value.replace(/\s+/g, '');
          setValue('isChange', true);
          setHasChanged(true);
          if (key === 'url') {
            setRequireValidation(true); // Kích hoạt xác thực khi sửa url
          }
          onChangeDetected?.();
          const updatedAuth = {
            ...auth,
            [key]: trimmedValue,
          };

          const isUrlEmpty = !updatedAuth.url?.trim();
          const isApiKeyEmpty = !updatedAuth.apiKey?.trim();
          setHasErrorUrl(isUrlEmpty);
          setHasErrorApiKey(isApiKeyEmpty);

          if (isUrlEmpty && isApiKeyEmpty) {
            field.onChange(null);
          } else {
            field.onChange(JSON.stringify(updatedAuth));
          }
        };

        return (
          <>
            <SimpleGrid cols={2} spacing='md'>
              <KanbanInput
                label='Url'
                type='url'
                value={auth.url || ''}
                onChange={(e) => handleChange('url', e.target.value.trim())}
                error={hasErrorUrl ? INPUT_REQUIRE : undefined}
                maxLength={COMMON_MAX_LENGTH}
                required
              />
            </SimpleGrid>

            <SimpleGrid cols={2} spacing='md'>
              <KanbanInput
                label='API Key'
                type={showPassword ? 'text' : 'password'}
                autoComplete='new-apiKey'
                placeholder={id ? '*********' : ''}
                value={auth.apiKey || ''}
                onChange={(e) => handleChange('apiKey', e.target.value.trim())}
                error={hasErrorApiKey ? INPUT_REQUIRE : undefined}
                required
                maxLength={COMMON_MAX_LENGTH}
                rightSection={
                  showPassword ? (
                    <IconEyeOff size={16} stroke={1.5} style={{ cursor: 'pointer' }} onClick={() => setShowPassword(!showPassword)} />
                  ) : (
                    <IconEye size={16} stroke={1.5} style={{ cursor: 'pointer' }} onClick={() => setShowPassword(!showPassword)} />
                  )
                }
              />
            </SimpleGrid>
            <CIConfigMapper form={props.form} ref={props.ciConfigRef} />
          </>
        );
      }}
    />
  );
};

export default WLAAuthentication;
