import { KanbanModal, KanbanModalProps } from 'kanban-design-system';
import React from 'react';

const AmtModal = (props: KanbanModalProps, ref: React.ForwardedRef<HTMLDivElement>) => {
  return <KanbanModal ref={ref} closeOnClickOutside={false} showFullScreenAction={false} closeOnEscape={false} showCloseIcon={false} {...props} />;
};

export default React.forwardRef(AmtModal) as typeof KanbanModal;
