import { COMMON_DESCRIPTION_MAX_LENGTH, COMMON_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { INPUT_REQUIRE, REGEX_INPUT_MESSAGE_ERROR } from '@core/message/MesageConstant';
import { GroupUserSchema } from '@core/schema/Group';
import { UserRoleSchema } from '@core/schema/Role';
import { z } from 'zod';
import { USERNAME_REGEX } from '@common/constants/RegexConstant';

export const UserModelSchema = z.object({
  id: z.number().nullish(),
  userName: z.string().trim().min(1, { message: INPUT_REQUIRE }).regex(USERNAME_REGEX, {
    message: REGEX_INPUT_MESSAGE_ERROR,
  }),
  name: z.string().trim().min(1, { message: INPUT_REQUIRE }),
  email: z.string().trim().min(1, { message: INPUT_REQUIRE }).max(COMMON_MAX_LENGTH),

  isActive: z.boolean(),
  isAdmin: z.boolean(),

  phone: z.string().max(COMMON_MAX_LENGTH).nullish(),
  title: z.string().max(COMMON_MAX_LENGTH).nullish(),
  description: z.string().max(COMMON_DESCRIPTION_MAX_LENGTH).nullish(),
  center: z.string().max(COMMON_MAX_LENGTH).nullish(),
  department: z.string().max(COMMON_MAX_LENGTH).nullish(),

  expired: z.string().nullish(),

  groupsToDelete: z.array(GroupUserSchema).optional(),
  groupsToInsert: z.array(GroupUserSchema).optional(),

  rolesToDelete: z.array(UserRoleSchema).optional(),
  rolesToInsert: z.array(UserRoleSchema).optional(),
});

export type UserModel = z.infer<typeof UserModelSchema>;

export type UserRoleModel = z.infer<typeof UserRoleSchema>;

export type UserSelectedModel = {
  id: string;
  name: string;
};
