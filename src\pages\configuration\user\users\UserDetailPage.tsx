import { KanbanButton, KanbanIconButton, KanbanInput, KanbanSwitch, KanbanText, KanbanTextarea, KanbanTitle } from 'kanban-design-system';

import React, { useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { useDisclosure } from '@mantine/hooks';
import { Box, Flex, SimpleGrid } from '@mantine/core';
import { zodResolver } from '@hookform/resolvers/zod';

import customStyled from '@resources/styles/Common.module.scss';
import { IconArrowLeft } from '@tabler/icons-react';
import { UserModel, UserModelSchema } from '@models/UserModel';
import { Controller, useForm, UseFormReturn } from 'react-hook-form';
import { UserApi } from '@api/UserApi';
import { DateTimePicker } from '@mantine/dates';
import UnsaveConfirmModal from '@components/utils/ConfirmMessageUtils';
import { UsernameValidationInput } from './UsernameValidationInput';
import { GroupListWithPagination } from './GroupList';
import { RoleListWithPagination } from './RoleList';
import { buildUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import { COMMON_DESCRIPTION_MAX_LENGTH, COMMON_MAX_LENGTH } from '@common/constants/ValidationConstant';
import dayjs from 'dayjs';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import { useAppSelector } from '@store';
import isEqual from 'fast-deep-equal';

import { trimStringFields } from '@common/utils/Helpers';
import { buildNavigateState } from '@core/hooks/useSavedFilterCycle';
import { useBreadcrumbEntityName } from '@core/hooks/useBreadcrumbEntityName';
type UserDetailPageProps = {
  idProp?: number;
};

const DEFAULT_FORM_VALUE: UserModel = {
  id: null,
  name: '',
  userName: '',
  isActive: true,
  email: '',
  phone: '',
  title: '',
  description: '',
  center: '',
  department: '',
  expired: null,
  isAdmin: false,
};

const SaveButton = ({ form }: { form: UseFormReturn<UserModel> }) => {
  const navigate = useNavigate();
  const { mutate: saveUserMutate } = useMutate(UserApi.save, {
    successNotification: 'User saved successfully',
    onSuccess: () => {
      navigate(buildUrl(ROUTE_PATH.CONFIGURATION_USER_USERS), { state: buildNavigateState({ fromDetail: true }) });
    },
  });

  const onSubmit = useCallback(() => {
    form.trigger();

    const parsedData = UserModelSchema.safeParse(form.getValues());
    if (parsedData.success) {
      const trimmed = trimStringFields(parsedData.data);
      trimmed.userName = trimmed.userName.toLowerCase();
      saveUserMutate(trimmed);
    }
  }, [form, saveUserMutate]);

  return (
    <KanbanButton size='xs' onMouseDown={onSubmit}>
      Save
    </KanbanButton>
  );
};

export const UserDetailPage: React.FC<UserDetailPageProps> = () => {
  const id = Number(useParams().id);
  const isUpdateMode = !!id;
  const { data: userResponse } = useFetch(UserApi.findById(id), { enabled: !!id });

  const [user, setUser] = useState<UserModel>();
  const [oldUser, setOldUser] = useState<UserModel>(DEFAULT_FORM_VALUE);
  useBreadcrumbEntityName(oldUser.userName);

  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);

  const username = useAppSelector(getCurrentUser).userInfo?.userName;

  const navigate = useNavigate();
  const form = useForm<UserModel>({
    defaultValues: DEFAULT_FORM_VALUE,
    resolver: zodResolver(UserModelSchema),
    mode: 'onChange',
  });

  useEffect(() => {
    if (id && userResponse?.data) {
      setUser(userResponse.data);
      setOldUser(userResponse.data);
      form.reset({ ...userResponse.data });
    }
  }, [form, id, userResponse]);

  const handleExistWithoutSave = () => {
    const currentValues = form.getValues();

    const normalizedCurrentValues = trimStringFields(currentValues);
    const normalizedOldUser = trimStringFields(oldUser);

    const hasFieldChanged = !isEqual(normalizedCurrentValues, normalizedOldUser);

    const hasDependencyChanged =
      !!currentValues.groupsToDelete?.length ||
      !!currentValues.groupsToInsert?.length ||
      !!currentValues.rolesToInsert?.length ||
      !!currentValues.rolesToDelete?.length;

    if (hasFieldChanged || hasDependencyChanged) {
      openModal();
    } else {
      navigate(buildUrl(ROUTE_PATH.CONFIGURATION_USER_USERS), { state: buildNavigateState({ fromDetail: true }) });
    }
  };

  return (
    <Box>
      <HeaderTitleComponent
        title=''
        rightSection={
          <Flex direction={'row'} align={'center'} gap={'xs'}>
            <KanbanButton size='xs' variant='light' onClick={handleExistWithoutSave}>
              Cancel
            </KanbanButton>
            <SaveButton form={form} />
          </Flex>
        }
        leftSection={
          <Flex direction={'row'} align={'center'} gap={'xs'}>
            <KanbanIconButton size='sm' variant='subtle' onClick={handleExistWithoutSave}>
              <IconArrowLeft />
            </KanbanIconButton>
            <KanbanTitle fz={'h4'}>{id ? 'Edit User' : 'Add User'}</KanbanTitle>
          </Flex>
        }
      />
      <Box className={customStyled.elementBorder}>
        <SimpleGrid cols={2} spacing='md'>
          <UsernameValidationInput form={form} id={id} />
          <KanbanTextarea maxLength={COMMON_DESCRIPTION_MAX_LENGTH} label='Description' {...form.register('description')} />
          <KanbanInput
            disabled={isUpdateMode}
            maxLength={COMMON_MAX_LENGTH}
            withAsterisk
            required
            label='Full Name'
            {...form.register('name')}
            error={form.formState.errors.name?.message}
          />

          <KanbanInput
            disabled={isUpdateMode}
            maxLength={COMMON_MAX_LENGTH}
            withAsterisk
            required
            label='Email'
            {...form.register('email')}
            error={form.formState.errors.email?.message}
          />
          <KanbanInput disabled={isUpdateMode} maxLength={COMMON_MAX_LENGTH} label='Title' {...form.register('title')} />
          <KanbanInput
            disabled={isUpdateMode}
            maxLength={COMMON_MAX_LENGTH}
            label='Phone'
            {...form.register('phone')}
            error={form.formState.errors.phone?.message}
          />
          <KanbanInput disabled={isUpdateMode} maxLength={COMMON_MAX_LENGTH} label='Department' {...form.register('department')} />
          <KanbanInput disabled={isUpdateMode} maxLength={COMMON_MAX_LENGTH} label='Center' {...form.register('center')} />

          <Box mb='md'>
            <KanbanText fw='500' mt={3}>
              Status
            </KanbanText>
            <Controller
              control={form.control}
              name='isActive'
              render={({ field }) => (
                <KanbanSwitch
                  mt='sm'
                  checked={field.value}
                  onChange={(e) => (username === user?.userName ? undefined : field.onChange(e.currentTarget.checked))}
                />
              )}
            />
          </Box>

          <Controller
            name='expired'
            control={form.control}
            render={({ field, fieldState }) => {
              return (
                <DateTimePicker
                  disabled={username === user?.userName}
                  label='Expired Date'
                  {...field}
                  error={fieldState.error?.message}
                  onChange={(val) => {
                    const stringValue = val ? dayjs(val).format() : null;
                    field.onChange(stringValue);
                  }}
                  value={field?.value ? new Date(field.value) : null}
                  minDate={new Date()}
                  clearable
                />
              );
            }}
          />
        </SimpleGrid>

        <Flex direction={'row'} align={'center'} gap={'xs'} mt='xs'>
          <GroupListWithPagination form={form} username={user?.userName || ''} />
        </Flex>

        <Flex direction={'row'} align={'center'} gap={'xs'} mt='xs'>
          <RoleListWithPagination form={form} username={user?.userName || ''} />
        </Flex>
      </Box>
      <UnsaveConfirmModal
        opened={openedModal}
        onClose={closeModal}
        onConfirm={() => navigate(buildUrl(ROUTE_PATH.CONFIGURATION_USER_USERS), { state: buildNavigateState({ fromDetail: true }) })}
      />
    </Box>
  );
};
export default UserDetailPageProps;
