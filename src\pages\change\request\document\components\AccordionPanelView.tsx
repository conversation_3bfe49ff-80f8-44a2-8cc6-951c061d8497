import React, { useCallback } from 'react';
import { Table, Group, Anchor } from '@mantine/core';
import { ChangeRequestDocumentGroupApi } from '@api/ChangeRequestDocumentGroupApi';
import useMutate from '@core/hooks/useMutate';
import {
  ChangeRequestDocumentEnums,
  ChangeRequestDocumentGroupModel,
  ChangeRequestDocumentModel,
  ChangeRequestSendToOwnerRequest,
} from '@models/ChangeRequestDocumentGroupModel';
import classes from './table/CommonTable.module.scss';
import { saveAs } from 'file-saver';
import { KanbanCheckbox } from 'kanban-design-system';
import { IconFile, IconLink } from '@tabler/icons-react';
import { renderStatusPill, renderUserPill } from '@pages/change/request/document/utils/TableUtils';
import { DocumentApproverLevels } from '@common/constants/ChangeDocumentConstants';

export const NON_BREAKING_SPACE = '\u00A0';

type DocumentViewTableProps = {
  changeRequestId: number;
  documentGroup: ChangeRequestDocumentGroupModel;
  selectedItems: ChangeRequestSendToOwnerRequest[];
  onToggle: (referenceId: number, username: string, checked: boolean) => void;
};

export function AccordionPanelView({ changeRequestId, documentGroup, onToggle, selectedItems }: DocumentViewTableProps) {
  const totalDocumentsInGroup = documentGroup.owners.reduce((sum, o) => sum + (o.documents?.length || 1), 0) || 1;
  const { mutate: downloadMutate } = useMutate<Blob, { changeRequestId: number; documentGroupId: number; documentName: string }>(
    ({ changeRequestId, documentGroupId }) => ChangeRequestDocumentGroupApi.downloadFile(changeRequestId, documentGroupId),
    {
      successNotification: 'Document downloaded successfully',
      errorNotification: (error) => ({ message: error.message }),
      onSuccess: (res, variables) => {
        if (res instanceof Blob) {
          const { documentName } = variables;
          saveAs(res, documentName);
        }
      },
    },
  );

  const handleDownloadFile = useCallback(
    (documentGroupId: number, documentName: string) => {
      downloadMutate({ changeRequestId, documentGroupId, documentName });
    },
    [changeRequestId, downloadMutate],
  );

  const renderDocumentCell = (document?: ChangeRequestDocumentModel) => {
    if (!document) {
      return NON_BREAKING_SPACE;
    }

    if (document.type === ChangeRequestDocumentEnums.DocumentType.Enum.FILE) {
      return (
        <Group gap={4}>
          <IconFile size={16} />
          <Anchor size='xs' component='button' onClick={() => handleDownloadFile(document.id, document.documentName)}>
            {document.documentName || 'Document File'}
          </Anchor>
        </Group>
      );
    }

    if (document.type === ChangeRequestDocumentEnums.DocumentType.Enum.URL && document.documentUrl) {
      return (
        <Group gap={4}>
          <IconLink size={16} />
          <Anchor href={document.documentUrl} target='_blank' rel='noopener noreferrer'>
            {document.documentName || 'Document Url'}
          </Anchor>
        </Group>
      );
    }

    return document.documentName || NON_BREAKING_SPACE;
  };

  return (
    <Table striped highlightOnHover withTableBorder withColumnBorders className={classes.table}>
      <Table.Thead>
        <Table.Tr>
          <Table.Th className={classes.headerCellCheckBox}></Table.Th>
          <Table.Th className={classes.headerCell}>Owner</Table.Th>
          <Table.Th className={classes.headerCell}>Document</Table.Th>
          <Table.Th className={classes.headerCell}>Leader level 1</Table.Th>
          <Table.Th className={classes.headerCell}>Leader level 2</Table.Th>
          <Table.Th className={classes.headerCell}>CAB</Table.Th>
          <Table.Th className={classes.headerCell}>Status</Table.Th>
          <Table.Th className={classes.headerCell}>Comment</Table.Th>
        </Table.Tr>
      </Table.Thead>
      <Table.Tbody>
        {documentGroup.owners.flatMap((owner, ownerIndex) => {
          const docs = owner.documents && owner.documents.length > 0 ? owner.documents : [undefined];
          const rowSpan = docs.length;

          return docs.map((doc, docIndex) => (
            <Table.Tr key={`${owner.username}-${docIndex}`}>
              {/* Checkbox + Owner */}
              {docIndex === 0 && (
                <>
                  <Table.Td className={classes.cell} rowSpan={rowSpan} style={{ verticalAlign: 'top' }}>
                    <KanbanCheckbox
                      onChange={(e) => onToggle(documentGroup.id, owner.username, e.target.checked)}
                      checked={selectedItems.some((item) => item.referenceId === documentGroup.id && item.usernames.includes(owner.username))}
                    />
                  </Table.Td>
                  <Table.Td className={classes.cell} rowSpan={rowSpan} style={{ verticalAlign: 'top' }}>
                    {renderUserPill(owner)}
                  </Table.Td>
                </>
              )}

              {/* Document cell */}
              <Table.Td className={classes.cell}>{renderDocumentCell(doc)}</Table.Td>

              {/* Approvers by level */}
              {DocumentApproverLevels.map((level) => (
                <Table.Td className={classes.cell} key={level}>
                  {doc
                    ? doc.approvers.filter((a) => a.documentApproverLevel === level).map((approver) => renderUserPill(approver.user))
                    : NON_BREAKING_SPACE}
                </Table.Td>
              ))}

              {/* CAB */}
              {ownerIndex === 0 && docIndex === 0 && (
                <Table.Td className={classes.cell} rowSpan={totalDocumentsInGroup} style={{ verticalAlign: 'top' }}>
                  {(documentGroup.approvers ?? []).map((approver) => renderUserPill(approver.user))}
                </Table.Td>
              )}

              {/* Status */}
              {docIndex === 0 && (
                <Table.Td className={classes.cell} rowSpan={rowSpan}>
                  {owner.status && renderStatusPill(owner.status)}
                </Table.Td>
              )}

              {/* Comment */}
              {docIndex === 0 && (
                <Table.Td className={classes.cell} rowSpan={rowSpan}>
                  {/* comment content */}
                </Table.Td>
              )}
            </Table.Tr>
          ));
        })}
      </Table.Tbody>
    </Table>
  );
}
