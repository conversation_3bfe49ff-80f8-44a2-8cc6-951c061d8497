import { KanbanButton, KanbanIconButton, KanbanMultiSelect, KanbanTextarea, KanbanTitle } from 'kanban-design-system';

import React, { useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { useDisclosure } from '@mantine/hooks';
import { Box, Flex, SimpleGrid } from '@mantine/core';
import { zodResolver } from '@hookform/resolvers/zod';

import customStyled from '@resources/styles/Common.module.scss';
import { IconArrowLeft } from '@tabler/icons-react';
import { Controller, useForm, UseFormReturn } from 'react-hook-form';
import UnsaveConfirmModal from '@components/utils/ConfirmMessageUtils';

import { buildUrlFromDetail, ROUTE_PATH } from '@common/utils/RouterUtils';
import { COMMON_DESCRIPTION_MAX_LENGTH } from '@common/constants/ValidationConstant';
import equal from 'fast-deep-equal';
import { trimStringFields } from '@common/utils/Helpers';
import { ChangeDocumentApi } from '@api/ChangeDocumentApi';
import { ChangeDocumentModel, ChangeDocumentModelSchema, DocumentChangeLevelEnum } from '@models/DocumentModel';
import { useBreadcrumbEntityName } from '@core/hooks/useBreadcrumbEntityName';
import { NameValidationInput } from './NameValidationInput';

type DetailPageProps = {
  idProp?: number;
};

const { path, state } = buildUrlFromDetail(ROUTE_PATH.CONFIGURATION_CHANGE_DOCUMENT);

const DEFAULT_FORM_VALUE: ChangeDocumentModel = {
  id: null,
  name: '',
};

const SaveButton = ({ form }: { form: UseFormReturn<ChangeDocumentModel> }) => {
  const navigate = useNavigate();
  const { mutate: saveDocumentMutate } = useMutate(ChangeDocumentApi.save, {
    successNotification: 'Document saved successfully',
    onSuccess: () => {
      navigate(path, { state });
    },
  });

  const onSubmit = useCallback(() => {
    form.trigger();

    const parsedData = ChangeDocumentModelSchema.safeParse(form.getValues());
    if (parsedData.success) {
      const trimmed = trimStringFields(parsedData.data, false);
      saveDocumentMutate(trimmed);
    }
  }, [form, saveDocumentMutate]);

  return (
    <KanbanButton size='xs' onClick={onSubmit}>
      Save
    </KanbanButton>
  );
};

export const DocumentDetailPage: React.FC<DetailPageProps> = () => {
  const id = Number(useParams().id);
  const { data } = useFetch(ChangeDocumentApi.findById(id), { enabled: !!id });
  const [document, setDocument] = useState<ChangeDocumentModel>(DEFAULT_FORM_VALUE);
  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);
  useBreadcrumbEntityName(document.name);

  const navigate = useNavigate();

  const form = useForm<ChangeDocumentModel>({
    defaultValues: DEFAULT_FORM_VALUE,
    resolver: zodResolver(ChangeDocumentModelSchema),
    mode: 'onChange',
  });

  const { control } = form;

  useEffect(() => {
    if (id && data?.data) {
      setDocument(data.data);
      form.reset({ ...data.data });
    }
  }, [form, id, data]);

  const handleExistWithoutSave = () => {
    const currentValues = form.getValues();

    const isInitialData = equal(currentValues, DEFAULT_FORM_VALUE);

    if (equal(currentValues, document) || isInitialData) {
      navigate(path, { state });
    } else {
      openModal();
    }
  };

  return (
    <Box>
      <HeaderTitleComponent
        title=''
        rightSection={
          <Flex direction={'row'} align={'center'} gap={'xs'}>
            <KanbanButton size='xs' variant='light' onClick={handleExistWithoutSave}>
              Cancel
            </KanbanButton>
            <SaveButton form={form} />
          </Flex>
        }
        leftSection={
          <Flex direction={'row'} align={'center'} gap={'xs'}>
            <KanbanIconButton size='sm' variant='subtle' onClick={handleExistWithoutSave}>
              <IconArrowLeft />
            </KanbanIconButton>
            <KanbanTitle fz={'h4'}>{id ? 'Edit Document' : 'Add Document'}</KanbanTitle>
          </Flex>
        }
      />
      <Box className={customStyled.elementBorder}>
        <SimpleGrid cols={2} spacing='md'>
          <NameValidationInput form={form} id={id} name={data?.data?.name} />
          <Controller
            name='changeLevels'
            control={control}
            render={({ field, fieldState }) => (
              <KanbanMultiSelect
                label='Change Level'
                required
                withAsterisk={false}
                data={DocumentChangeLevelEnum.options.map((key) => ({
                  value: key,
                  label: key,
                }))}
                value={field.value?.map(String) ?? []}
                onChange={(value: string[]) => field.onChange(value.map(Number))}
                error={fieldState.error?.message}
              />
            )}
          />
        </SimpleGrid>

        <SimpleGrid cols={1} spacing='md'>
          <KanbanTextarea minRows={3} maxLength={COMMON_DESCRIPTION_MAX_LENGTH} label='Description' {...form.register('description')} />
        </SimpleGrid>
      </Box>
      <UnsaveConfirmModal opened={openedModal} onClose={closeModal} onConfirm={() => navigate(path, { state })} />
    </Box>
  );
};

export default DocumentDetailPage;
