import React from 'react';
import { Tooltip, Checkbox, Box } from '@mantine/core';
import { Control, Controller } from 'react-hook-form';
import { ChangeTemplate } from '@core/schema/ChangeTemplate';

interface RequiredCheckboxProps {
  control: Control<ChangeTemplate>;
  name: `fields.${number}.required`;
}

const RequiredCheckbox: React.FC<RequiredCheckboxProps> = ({ control, name }) => (
  <Box>
    <Controller
      name={name}
      control={control}
      render={({ field: controllerField }) => (
        <Tooltip label='Required' position='top' withArrow>
          <Checkbox size='xs' checked={controllerField.value || false} onChange={(e) => controllerField.onChange(e.currentTarget.checked)} />
        </Tooltip>
      )}
    />
  </Box>
);

export default RequiredCheckbox;
