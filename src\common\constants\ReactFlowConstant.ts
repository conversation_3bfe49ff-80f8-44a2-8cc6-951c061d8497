export const DRAFT_NODE_POSITION = { x: 300, y: 100 };

export interface KeyboardShortcutEvent {
  code: string;
  ctrlKey: boolean;
  key: string;
}

export const KEYBOARD_SHORTCUTS = {
  SPACE: 'Space',
  DELETE: 'Delete',
  ZOOM_IN: ['+', '='],
  ZOOM_OUT: '-',
  ZOOM_FIT: 'x',
  SELECT_ALL: 'a',
  COPY: 'c',
  PASTE: 'v',
  DUPLICATE: 'd',
  UNDO: 'z',
  REDO: 'y',
} as const;

export const ZOOM_CONFIG = {
  duration: 300,
  maxZoom: 0.8,
  minZoom: 0.1,
  padding: 0.1,
} as const;

export const GRID_CONFIG = {
  snap: [20, 20] as [number, number],
  gap: 20,
  size: 1,
} as const;

export const HANDLE_TYPES = {
  INPUT: 'INPUT',
  OUTPUT: 'OUTPUT',
  START: 'START',
  END: 'END',
} as const;

export const HANDLE_POSITION = {
  LEFT: 'LEFT',
  RIGHT: 'RIGHT',
  TOP: 'TOP',
  BOTTOM: 'BOTTOM',
} as const;

export const FLOW_DEFAULTS = {
  START_NODE_ID: 'START',
  END_NODE_ID: 'END',
} as const;
