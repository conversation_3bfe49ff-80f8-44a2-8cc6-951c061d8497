import { AxiosError } from 'axios';
import { ZodError } from 'zod';
import { AppNotificationConfig, NotificationConfig } from './AppConfigTypes';
import { NotificationError, NotificationSuccess } from '@common/utils/NotificationUtils';
import { NO_NETWORK } from '@core/message/MesageConstant';

export function defaultErrorNotification(error: unknown): NotificationConfig {
  if (error instanceof AxiosError) {
    if (error.code === '000') {
      return {
        enable: true,
        title: `Error with Client Message ID: ${error?.response?.data.clientMessageId ?? ''}`,
        message: 'An unknown error occurred. Please contact support.',
      };
    } else {
      return {
        enable: true,
        title: `Request error ${error?.code ?? ''}`,
        message: error?.message,
      };
    }
  }
  if (error instanceof ZodError) {
    return {
      enable: true,
      title: `Request error invalid response`,
      message: 'Failed to parse reponse',
    };
  }
  if (error && typeof error === 'object' && 'message' in error && error.message === NO_NETWORK) {
    return {
      enable: true,
      title: 'No connection',
      message: error.message,
    };
  }
  console.error(error);
  return {
    enable: true,
    title: `Server error`,
    message: 'Something wrong!',
  };
}

export const getNotificationConfig = <TData>(notification: AppNotificationConfig<TData> | undefined, data: TData) => {
  if (!notification) {
    return null;
  }
  if (typeof notification === 'string') {
    return {
      enable: true,
      message: notification,
    };
  }
  if (typeof notification === 'function') {
    return notification(data);
  }
  return notification;
};

export const showErrorNotification = (notification: AppNotificationConfig<Error> | undefined, error: Error) => {
  const notificationConfig = getNotificationConfig(notification, error);
  if (notificationConfig && notificationConfig.enable !== false) {
    NotificationError(notificationConfig);
  }
};

export const showSuccessNotification = <TData>(notification: AppNotificationConfig<TData> | undefined, data: TData) => {
  const notificationConfig = getNotificationConfig(notification, data);
  if (notificationConfig && notificationConfig.enable !== false) {
    NotificationSuccess(notificationConfig);
  }
};
