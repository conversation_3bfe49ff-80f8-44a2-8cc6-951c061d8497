export const LocalStorageKey = {
  TIME_INTERVAL_REFRESH_KEY: 'TIME_INTERVAL_REFRESH',
  EVENT_FILTER_KEY: 'EVENT_FILTER',

  SEARCH_VALUE_USERS_PAGE: 'SEARCH_VALUE_USERS_PAGE',
  SEARCH_VALUE_GROUPS_PAGE: 'SEARCH_VALUE_GROUPS_PAGE',
  SEARCH_VALUE_USER_ROLES_POPUP: 'SEARCH_VALUE_USER_ROLES_POPUP',
  SEARCH_VALUE_USER_GROUPS_POPUP: 'SEARCH_VALUE_USER_GROUPS_POPUP',
  SEARCH_VALUE_ROLES_PAGE: 'SEARCH_VALUE_ROLES_PAGE',
  SEARCH_VALUE_CUSTOM_FIELD_PAGE: 'SEARCH_VALUE_CUSTOM_FIELD_PAGE',
  SEARCH_VALUE_CHANGE_TEMPLATES_PAGE: 'SEARCH_VALUE_CHANGE_TEMPLATES_PAGE',
  SEARCH_VALUE_CHANGE_STATUS_PAGE: 'SEARCH_VALUE_CHANGE_STATUS_PAGE',
  SEARCH_VALUE_CHANGE_DOCUMENT_PAGE: 'SEARCH_VALUE_CHANGE_DOCUMENT_PAGE',
  SEARCH_VALUE_CHANGE_WORKFLOW_NODE_PAGE: 'SEARCH_VALUE_CHANGE_WORKFLOW_NODE_PAGE',
  SEARCH_VALUE_CHANGE_REQUEST_PAGE: 'SEARCH_VALUE_CHANGE_REQUEST_PAGE',
  SEARCH_VALUE_EMAIL_TEMPLATE_PAGE: 'SEARCH_VALUE_EMAIL_TEMPLATE_PAGE',
  SEARCH_VALUE_CHANGE_FLOW_PAGE: 'SEARCH_VALUE_CHANGE_FLOW_PAGE',

  COLUMN_DISPLAY_USERS_PAGE: 'COLUMN_DISPLAY_USERS_PAGE',
  COLUMN_DISPLAY_ROLES_PAGE: 'COLUMN_DISPLAY_ROLES_PAGE',
  COLUMN_DISPLAY_GROUPS_PAGE: 'COLUMN_DISPLAY_GROUPS_PAGE',
  COLUMN_DISPLAY_USER_ROLES_POPUP: 'COLUMN_DISPLAY_USER_ROLES_POPUP',
  COLUMN_DISPLAY_USER_GROUPS_POPUP: 'COLUMN_DISPLAY_USER_GROUPS_POPUP',
  COLUMN_DISPLAY_CUSTOM_FIELD_PAGE: 'COLUMN_DISPLAY_CUSTOM_FIELD_PAGE',
  COLUMN_DISPLAY_CHANGE_TEMPLATES_PAGE: 'COLUMN_DISPLAY_CHANGE_TEMPLATES_PAGE',
  COLUMN_DISPLAY_CHANGE_STATUS_PAGE: 'COLUMN_DISPLAY_CHANGE_STATUS_PAGE',
  COLUMN_DISPLAY_CHANGE_DOCUMENT_PAGE: 'COLUMN_DISPLAY_CHANGE_DOCUMENT_PAGE',
  COLUMN_DISPLAY_CHANGE_WORKFLOW_NODE_PAGE: 'COLUMN_DISPLAY_CHANGE_WORKFLOW_NODE_PAGE',
  COLUMN_DISPLAY_NOTIFICATIONS_EMAIL_TEMPLATE: 'COLUMN_DISPLAY_NOTIFICATIONS_EMAIL_TEMPLATE',
  COLUMN_DISPLAY_CHANGE_FLOW_PAGE: 'COLUMN_DISPLAY_CHANGE_FLOW_PAGE',
  COLUMN_DISPLAY_NODE_CONFIG: 'COLUMN_DISPLAY_NODE_CONFIG',
} as const;

const SEARCH_KEYS = [
  LocalStorageKey.SEARCH_VALUE_USERS_PAGE,
  LocalStorageKey.SEARCH_VALUE_GROUPS_PAGE,
  LocalStorageKey.SEARCH_VALUE_USER_ROLES_POPUP,
  LocalStorageKey.SEARCH_VALUE_USER_GROUPS_POPUP,
  LocalStorageKey.SEARCH_VALUE_ROLES_PAGE,
  LocalStorageKey.SEARCH_VALUE_CUSTOM_FIELD_PAGE,
  LocalStorageKey.SEARCH_VALUE_CHANGE_TEMPLATES_PAGE,
  LocalStorageKey.SEARCH_VALUE_CHANGE_STATUS_PAGE,
  LocalStorageKey.SEARCH_VALUE_CHANGE_DOCUMENT_PAGE,
  LocalStorageKey.SEARCH_VALUE_CHANGE_WORKFLOW_NODE_PAGE,
  LocalStorageKey.SEARCH_VALUE_CHANGE_FLOW_PAGE,
  LocalStorageKey.SEARCH_VALUE_EMAIL_TEMPLATE_PAGE,
] as const;

const COLUMN_KEYS = [
  LocalStorageKey.COLUMN_DISPLAY_USERS_PAGE,
  LocalStorageKey.COLUMN_DISPLAY_ROLES_PAGE,
  LocalStorageKey.COLUMN_DISPLAY_GROUPS_PAGE,
  LocalStorageKey.COLUMN_DISPLAY_USER_ROLES_POPUP,
  LocalStorageKey.COLUMN_DISPLAY_USER_GROUPS_POPUP,
  LocalStorageKey.COLUMN_DISPLAY_CUSTOM_FIELD_PAGE,
  LocalStorageKey.COLUMN_DISPLAY_CHANGE_TEMPLATES_PAGE,
  LocalStorageKey.COLUMN_DISPLAY_CHANGE_STATUS_PAGE,
  LocalStorageKey.COLUMN_DISPLAY_CHANGE_DOCUMENT_PAGE,
  LocalStorageKey.COLUMN_DISPLAY_CHANGE_WORKFLOW_NODE_PAGE,
  LocalStorageKey.COLUMN_DISPLAY_CHANGE_FLOW_PAGE,
  LocalStorageKey.COLUMN_DISPLAY_NOTIFICATIONS_EMAIL_TEMPLATE,
] as const;

export const CLEAR_WHEN_LOGOUT_KEYS: (typeof LocalStorageKey)[keyof typeof LocalStorageKey][] = [
  LocalStorageKey.TIME_INTERVAL_REFRESH_KEY,
  LocalStorageKey.EVENT_FILTER_KEY,
  ...SEARCH_KEYS,
  ...COLUMN_KEYS,
];
