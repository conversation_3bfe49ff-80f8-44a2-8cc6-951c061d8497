import {
  getDefaultTableAffected,
  KanbanButton,
  KanbanIconButton,
  KanbanInput,
  KanbanModal,
  KanbanSelect,
  KanbanText,
  KanbanTextarea,
  KanbanTitle,
  TableAffactedSafeType,
} from 'kanban-design-system';

import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { GroupModel, GroupRoleModel, UserGroupModel } from '@models/GroupRoleUserModel';
import { GroupApi } from '@api/GroupApi';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import { GROUP_TYPE_OPTIONS as GROUP_TYPE_LABELS, GroupSchema, GroupTypeSchema } from '@core/schema/Group';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { useDisclosure } from '@mantine/hooks';
import { Box, Flex } from '@mantine/core';
import UserGroupTableComponent from './component/UserGroupTableComponent';

import customStyled from '@resources/styles/Common.module.scss';
import { IconArrowLeft } from '@tabler/icons-react';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import GroupRoleTableComponent, { getKeyRoleInGroup } from './component/GroupRoleTableComponent';
import { callRequest } from '@core/api';
import AmtPillsComponent from '@components/AmtPillsComponent';
import { buildUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import { DEFAULT_ITEM_CHECK, ItemCheck } from '@core/schema/Common';
import UnsaveConfirmModal from '@components/utils/ConfirmMessageUtils';
import { trimStringFields } from '@common/utils/Helpers';
import { buildNavigateState } from '@core/hooks/useSavedFilterCycle';
import { useBreadcrumbEntityName } from '@core/hooks/useBreadcrumbEntityName';
type GroupDetailPage = {
  idProp?: number;
};

const DEFAULT_GROUP: GroupModel = {
  id: 0,
  name: '',
  type: GroupTypeSchema.Enum.NORMAL,
};
const DEFAULT_PAGING_ITEM_IN_GROUP = 100;
export const GroupDetailPage: React.FC<GroupDetailPage> = () => {
  const id = Number(useParams().id);
  const { data: groupResponse } = useFetch(GroupApi.getDetail(id), { enabled: !!id });
  //users
  const [toInsertUsers, setToInsertUsers] = useState<ItemCheck<UserGroupModel>>(DEFAULT_ITEM_CHECK);
  const [toInsertTempUsers, setToInsertTempUsers] = useState<ItemCheck<UserGroupModel>>(DEFAULT_ITEM_CHECK);
  const [toDeleteUsers, setToDeleteUsers] = useState<ItemCheck<UserGroupModel>>(DEFAULT_ITEM_CHECK);
  const [toDeleteTempUsers, setToDeleteTempUsers] = useState<ItemCheck<UserGroupModel>>(DEFAULT_ITEM_CHECK);
  const [selectedUsers, setSelectedUsers] = useState<ItemCheck<UserGroupModel>>(DEFAULT_ITEM_CHECK);
  const [selectedTempUsers, setSelectedTempUsers] = useState<ItemCheck<UserGroupModel>>(selectedUsers);
  const [tableAffectedUser, setTableAffectedUser] = useState<TableAffactedSafeType>({
    ...getDefaultTableAffected(),
    rowsPerPage: DEFAULT_PAGING_ITEM_IN_GROUP,
  });
  const { data: onePageUserGroups } = useFetch(GroupApi.findPagingUsersByGroupId(id, tableAffectedUser), {
    enabled: !!tableAffectedUser && !!id,
  });
  const [oldDisplayUsers, setOldDisplayUsers] = useState<ItemCheck<UserGroupModel>>(DEFAULT_ITEM_CHECK);
  const [oldPagingUsers, setOldPagingUsers] = useState<ItemCheck<UserGroupModel>>(DEFAULT_ITEM_CHECK);

  //roles
  const [selectedRoles, setSelectedRoles] = useState<ItemCheck<GroupRoleModel>>(DEFAULT_ITEM_CHECK);
  const [toInsertRoles, setToInsertRoles] = useState<ItemCheck<GroupRoleModel>>(DEFAULT_ITEM_CHECK);
  const [toDeleteRoles, setToDeleteRoles] = useState<ItemCheck<GroupRoleModel>>(DEFAULT_ITEM_CHECK);
  const [oldPagingRoles, setOldPagingRoles] = useState<ItemCheck<GroupRoleModel>>(DEFAULT_ITEM_CHECK);

  const [selectedTempRoles, setSelectedTempRoles] = useState<ItemCheck<GroupRoleModel>>(selectedRoles);
  const [toInsertTempRoles, setToInsertTempRoles] = useState<ItemCheck<GroupRoleModel>>(DEFAULT_ITEM_CHECK);
  const [toDeleteTempRoles, setToDeleteTempRoles] = useState<ItemCheck<GroupRoleModel>>(DEFAULT_ITEM_CHECK);
  const [tableAffectedRole, setTableAffectedRole] = useState<TableAffactedSafeType>({
    ...getDefaultTableAffected(),
    rowsPerPage: DEFAULT_PAGING_ITEM_IN_GROUP,
  });

  const displayRoleQueryConfig = GroupApi.findPagingRolesByGroupId(id, tableAffectedRole);
  const { data: onePageGroupRoles } = useFetch(displayRoleQueryConfig, {
    enabled: !!tableAffectedRole && !!id,
  });
  const [oldDisplayRoles, setOldDisplayRoles] = useState<ItemCheck<GroupRoleModel>>(DEFAULT_ITEM_CHECK);

  //chip user fetch paging
  useEffect(() => {
    const result = onePageUserGroups?.data?.content;
    if (result) {
      setOldDisplayUsers((_) => {
        const updateMap = { ...DEFAULT_ITEM_CHECK };

        result.forEach((item) => {
          updateMap[item.userName] = item;
        });
        return updateMap;
      });
    }
  }, [onePageUserGroups?.data?.content]);
  //chip role fetch paging
  useEffect(() => {
    const result = onePageGroupRoles?.data?.content;
    if (result) {
      setOldDisplayRoles((_) => {
        const updateMap = { ...DEFAULT_ITEM_CHECK };
        result.forEach((item) => {
          updateMap[getKeyRoleInGroup(item.roleId)] = item;
        });
        return updateMap;
      });
    }
  }, [onePageGroupRoles, setOldDisplayRoles]);
  const displayUsers = useMemo(() => {
    const updateMap = { ...oldDisplayUsers };
    Object.keys(toInsertUsers).forEach((it) => (updateMap[it] = toInsertUsers[it]));
    Object.keys(toDeleteUsers).forEach((it) => delete updateMap[it]);
    return updateMap;
  }, [oldDisplayUsers, toDeleteUsers, toInsertUsers]);

  const displayRoles = useMemo(() => {
    const updateMap = { ...oldDisplayRoles };
    Object.keys(toInsertRoles).forEach((it) => (updateMap[it] = toInsertRoles[it]));
    Object.keys(toDeleteRoles).forEach((it) => delete updateMap[it]);

    return updateMap;
  }, [oldDisplayRoles, toDeleteRoles, toInsertRoles]);

  const [group, setGroup] = useState<GroupModel>();
  const [oldGroup, setOldGroup] = useState<GroupModel>(DEFAULT_GROUP);
  useBreadcrumbEntityName(oldGroup.name);

  const isUpdateMode = !!group;

  const [openedUsers, { close: closeUsers, open: openUsers }] = useDisclosure(false);
  const [openedRoles, { close: closeRoles, open: openRoles }] = useDisclosure(false);
  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);

  const navigate = useNavigate();

  useEffect(() => {
    if (groupResponse?.data) {
      setGroup(groupResponse.data);
      setOldGroup(groupResponse.data);
    }
  }, [groupResponse?.data]);
  const { mutate: saveMutate } = useMutate(GroupApi.saveOrUpdate, {
    successNotification: `Group saved successfully`,
    errorNotification: (data) => ({ message: data.message }),
    onSuccess: () => {
      handleNavigateToList();
    },
  });
  const handleNavigateToList = useCallback(() => {
    navigate(buildUrl(ROUTE_PATH.CONFIGURATION_USER_GROUPS), { state: buildNavigateState({ fromDetail: true }) });
  }, [navigate]);
  const { clearErrors, control, formState, getValues, handleSubmit, register, setError, setValue } = useForm<GroupModel>({
    defaultValues: DEFAULT_GROUP,
    resolver: zodResolver(GroupSchema),
    mode: 'onBlur',
  });

  const validateNameOnBlur = async (name: string) => {
    const isValidZod = !formState.errors.name;

    if (isValidZod && name) {
      const checkNameResponse = await callRequest(GroupApi.getSameNameDetail(id, name));
      if (checkNameResponse.data) {
        setError('name', { type: 'api', message: 'Group name existed' });
        return false;
      }
      clearErrors('name');
    }
    return true;
  };
  const handleSave = () => {
    const updateGr: GroupModel = {
      ...getValues(),
      toInsertUserGroups: Object.keys(toInsertUsers).map((it) => toInsertUsers[it]),
      toDeleteGroupRoles: Object.keys(toDeleteRoles).map((it) => toDeleteRoles[it]),
      toDeleteUserGroups: Object.keys(toDeleteUsers).map((it) => toDeleteUsers[it]),
      toInsertGroupRoles: Object.keys(toInsertRoles).map((it) => toInsertRoles[it]),
    };
    saveMutate(updateGr);
  };
  useEffect(() => {
    if (group) {
      setValue('id', group.id);
      setValue('name', group.name);
      setValue('description', group.description);
      setValue('userGroups', group.userGroups);
      setValue('groupRoles', group.groupRoles);
      setValue('type', group.type);
    }
  }, [group, setValue, onePageUserGroups?.data]);
  const handleCancelChangeUsers = () => {
    setSelectedTempUsers(selectedUsers);
    closeUsers();
  };

  const handleCancelChangeRoles = () => {
    setSelectedTempRoles(selectedRoles);
    closeRoles();
  };
  const handleExistWithoutSave = () => {
    const normalizedCurrrents = trimStringFields(getValues());
    const normalizedOlds = trimStringFields(oldGroup);
    if (
      normalizedCurrrents.name !== normalizedOlds.name ||
      normalizedCurrrents.description !== normalizedOlds.description ||
      Object.keys(toInsertUsers).length ||
      Object.keys(toDeleteUsers).length ||
      Object.keys(toDeleteRoles).length ||
      Object.keys(toInsertRoles).length
    ) {
      openModal();
    } else {
      handleNavigateToList();
    }
  };

  const handleGetPageUser = () => {
    setTableAffectedUser((prev) => ({ ...prev, page: prev.page + 1 }));
  };

  const handleGetPageRole = (value: number) => {
    setTableAffectedRole((prev) => ({ ...prev, page: value }));
  };

  const handleRemoveUser = (key: string) => {
    const isOldItem = key in oldDisplayUsers;
    //add /remove item in insert list
    if (!isOldItem) {
      setToInsertUsers((prev) => {
        const updateMap = { ...prev };
        delete updateMap[key];
        return updateMap;
      });
    } else {
      // add /remove item in delete list
      setToDeleteUsers((prev) => {
        const updateMap = { ...prev };
        updateMap[key] = oldDisplayUsers[key];
        return updateMap;
      });
    }
    setSelectedUsers((prev) => {
      const updateMap = { ...prev };
      delete updateMap[key];
      setSelectedTempUsers(updateMap);
      return updateMap;
    });
  };
  const handleRemoveRole = (key: string) => {
    const isOldItem = key in oldDisplayRoles;
    //add /remove item in insert list
    if (!isOldItem) {
      setToInsertRoles((prev) => {
        const updateMap = { ...prev };
        delete updateMap[key];
        return updateMap;
      });
    } else {
      // add /remove item in delete list
      setToDeleteRoles((prev) => {
        const updateMap = { ...prev };
        updateMap[key] = oldDisplayRoles[key];
        return updateMap;
      });
    }
    setSelectedRoles((prev) => {
      const updateMap = { ...prev };
      delete updateMap[key];
      setSelectedTempRoles(updateMap);
      return updateMap;
    });
  };

  const handleSaveTempUsers = useCallback(() => {
    setSelectedUsers((prev) => {
      const updatedPaging = { ...prev };
      Object.entries(selectedTempUsers).forEach((it) => {
        updatedPaging[it[0]] = it[1];
      });

      Object.entries(toInsertTempUsers).forEach((it) => {
        if (!(it[0] in oldPagingUsers)) {
          updatedPaging[it[0]] = it[1];
        }
      });

      Object.entries(toDeleteTempUsers).forEach((it) => {
        delete updatedPaging[it[0]];
      });

      setToInsertUsers(() => {
        const initInserts = { ...DEFAULT_ITEM_CHECK };
        Object.keys(updatedPaging)
          .filter((it) => !(it in oldPagingUsers))
          .forEach((it) => (initInserts[it] = updatedPaging[it]));

        return initInserts;
      });

      setToDeleteUsers(() => {
        const initDeletes = { ...DEFAULT_ITEM_CHECK };
        Object.keys(oldPagingUsers)
          .filter((it) => !(it in updatedPaging))
          .forEach((it) => (initDeletes[it] = oldPagingUsers[it]));
        return initDeletes;
      });

      return updatedPaging;
    });

    closeUsers();
  }, [closeUsers, oldPagingUsers, selectedTempUsers, toDeleteTempUsers, toInsertTempUsers]);
  const handleSaveTempRoles = useCallback(() => {
    setSelectedRoles((prev) => {
      const updatedPaging = { ...prev };
      Object.entries(selectedTempRoles).forEach((it) => {
        updatedPaging[it[0]] = it[1];
      });

      Object.entries(toInsertTempRoles).forEach((it) => {
        if (!(it[0] in oldPagingRoles)) {
          updatedPaging[it[0]] = it[1];
        }
      });

      Object.entries(toDeleteTempRoles).forEach((it) => {
        delete updatedPaging[it[0]];
      });

      setToInsertRoles(() => {
        const initInserts = { ...DEFAULT_ITEM_CHECK };
        Object.keys(updatedPaging)
          .filter((it) => !(it in oldPagingRoles))
          .forEach((it) => (initInserts[it] = updatedPaging[it]));

        return initInserts;
      });

      setToDeleteRoles(() => {
        const initDeletes = { ...DEFAULT_ITEM_CHECK };
        Object.keys(oldPagingRoles)
          .filter((it) => !(it in updatedPaging))
          .forEach((it) => (initDeletes[it] = oldPagingRoles[it]));
        return initDeletes;
      });

      return updatedPaging;
    });

    closeRoles();
  }, [closeRoles, oldPagingRoles, selectedTempRoles, toDeleteTempRoles, toInsertTempRoles]);

  return (
    <Box>
      <form onSubmit={handleSubmit(handleSave)}>
        <HeaderTitleComponent
          title=''
          rightSection={
            <Flex direction={'row'} align={'center'} gap={'xs'}>
              <KanbanButton size='xs' variant='subtle' onClick={handleExistWithoutSave}>
                Cancel
              </KanbanButton>
              <KanbanButton size='xs' variant='filled' type='submit'>
                Save
              </KanbanButton>
            </Flex>
          }
          leftSection={
            <Flex direction={'row'} align={'center'} gap={'xs'}>
              <KanbanIconButton size='sm' variant='subtle' onClick={handleExistWithoutSave}>
                <IconArrowLeft />
              </KanbanIconButton>
              <KanbanTitle fz={'h4'}>{isUpdateMode ? 'Edit Group' : 'Add Group'}</KanbanTitle>
            </Flex>
          }
        />

        <Box className={customStyled.elementBorder}>
          <KanbanInput
            label='Group Name'
            mb={'xs'}
            withAsterisk
            {...register('name', {
              onBlur: async (e) => {
                validateNameOnBlur(e.target.value);
              },
              onChange: () => {
                if (formState.errors.name) {
                  clearErrors('name');
                }
              },
            })}
            error={formState.errors['name']?.message}
            maxLength={255}
          />

          <Controller
            name='type'
            control={control}
            render={({ field }) => (
              <KanbanSelect
                label='Group type'
                allowDeselect={false}
                defaultValue={GroupTypeSchema.Enum.NORMAL}
                data={Object.entries(GROUP_TYPE_LABELS).map((entry) => ({ value: entry[0], label: entry[1] }))}
                {...field}
                onChange={(value) => {
                  field.onChange(value);
                }}
              />
            )}
          />
          <KanbanTextarea
            label='Group Description'
            mb={'lg'}
            {...register('description')}
            error={formState.errors['description']?.message}
            maxLength={1000}
          />
          <Flex direction={'row'} align={'center'} gap={'xs'} mt='xs' mb='xs'>
            <KanbanText fw={'500'}>User list</KanbanText>
            <KanbanButton
              size='compact-xs'
              variant='outline'
              onClick={() => {
                openUsers();
                setToInsertTempUsers({});
                setToDeleteTempUsers({});
              }}>
              Add +
            </KanbanButton>
          </Flex>
          <AmtPillsComponent
            values={Object.keys(displayUsers).reduce((acc: Record<string, string>, obj) => {
              acc[obj] = displayUsers[obj].userName;
              return acc;
            }, {})}
            onChangePage={handleGetPageUser}
            pageResonse={onePageUserGroups?.data}
            onRemoveItem={handleRemoveUser}
          />
          <KanbanModal
            title=''
            size={'80%'}
            opened={openedUsers}
            actions={<KanbanButton onClick={handleSaveTempUsers}>Save</KanbanButton>}
            onClose={handleCancelChangeUsers}>
            <UserGroupTableComponent
              parentId={id}
              selecteds={selectedTempUsers}
              setSelecteds={setSelectedTempUsers}
              setToInserts={setToInsertTempUsers}
              toDeletes={toDeleteUsers}
              setToDeletes={setToDeleteTempUsers}
              setOldPagings={setOldPagingUsers}
            />
          </KanbanModal>
          <Flex direction={'row'} align={'center'} gap={'xs'} mt='xl' mb='xs'>
            <KanbanText fw={'500'}>Role list</KanbanText>
            <KanbanButton
              size='compact-xs'
              variant='outline'
              onClick={() => {
                openRoles();
                setToInsertTempRoles({});
                setToDeleteTempRoles({});
              }}>
              Add +
            </KanbanButton>
          </Flex>
          <AmtPillsComponent
            values={Object.keys(displayRoles).reduce((acc: Record<string, string>, obj) => {
              acc[obj] = displayRoles[obj].roleName;
              return acc;
            }, {})}
            onChangePage={handleGetPageRole}
            onRemoveItem={handleRemoveRole}
            pageResonse={onePageGroupRoles?.data}
          />

          <KanbanModal
            title=''
            size={'80%'}
            opened={openedRoles}
            actions={
              <KanbanButton variant='filled' onClick={handleSaveTempRoles}>
                Save
              </KanbanButton>
            }
            onClose={handleCancelChangeRoles}>
            <GroupRoleTableComponent
              parentId={id}
              selecteds={selectedTempRoles}
              setSelecteds={setSelectedTempRoles}
              setToInserts={setToInsertTempRoles}
              setToDeletes={setToDeleteTempRoles}
              setOldPagings={setOldPagingRoles}
              toDeletes={toDeleteRoles}
            />
          </KanbanModal>
        </Box>

        <UnsaveConfirmModal opened={openedModal} onClose={closeModal} onConfirm={handleNavigateToList} />
      </form>
    </Box>
  );
};
export default GroupDetailPage;
