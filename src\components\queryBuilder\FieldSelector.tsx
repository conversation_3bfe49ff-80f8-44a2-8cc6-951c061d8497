import React, { FC, useMemo } from 'react';
import { FieldSelectorProps } from 'react-querybuilder';
import { KanbanSelect } from 'kanban-design-system';
import { ComboboxData } from '@mantine/core';

const FieldSelector: FC<FieldSelectorProps> = ({ handleOnChange, options, value }) => {
  const data: ComboboxData = useMemo(() => {
    return options.flatMap((opt) => {
      if ('name' in opt) {
        return [{ value: opt.name, label: opt.label }];
      } else if ('options' in opt) {
        return opt.options.map((subOpt) => ({
          value: subOpt.name,
          label: subOpt.label,
        }));
      }
      return [];
    });
  }, [options]);

  return <KanbanSelect allowDeselect={false} data={data} value={value} onChange={handleOnChange} placeholder='Select Field' mt='xs' />;
};

export default FieldSelector;
