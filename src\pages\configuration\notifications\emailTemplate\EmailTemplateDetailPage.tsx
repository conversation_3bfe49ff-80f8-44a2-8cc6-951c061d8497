import { KanbanButton, KanbanIconButton, KanbanSelect, KanbanTitle } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import { Box, Flex } from '@mantine/core';
import { IconArrowLeft } from '@tabler/icons-react';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import React, { useCallback, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { buildUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import UnsaveConfirmModal from '@components/utils/ConfirmMessageUtils';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import customStyled from '@resources/styles/Common.module.scss';

import { trimStringFields } from '@common/utils/Helpers';
import { buildNavigateState } from '@core/hooks/useSavedFilterCycle';
import { useBreadcrumbEntityName } from '@core/hooks/useBreadcrumbEntityName';
import { EmailTemplateDetailModel, EmailTemplateDetailSchema } from '@models/EmailTemplateModel';
import { NotificationsEmailTemplateApi } from '@api/NotificationsEmailTemplateApi';
import { CustomContentComponent } from '@components/customContent/CustomContentComponent';
import { NameValidationInput } from '@pages/configuration/notifications/emailTemplate/NameValidationInput';
import { TextLengthConstants } from '@common/constants/TextLengthConstants';
import { removeMentionLabelAndStringify } from '@common/utils/JsonTextUtils';
import { ControllerRenderProps } from 'react-hook-form/dist/types/controller';
import { EMAIL_TEMPLATE_TYPE_LABEL, EmailTemplateTypeEnum } from '@common/constants/EmailTemplateConstants';

const typeOptions = EmailTemplateTypeEnum.options.map((value) => ({
  value,
  label: EMAIL_TEMPLATE_TYPE_LABEL[value],
}));

type EmailTemplateDetailPageProps = {
  idProp?: number;
  disabled?: boolean;
};

export const DEFAULT_TEMPLATE_EMAIL: EmailTemplateDetailModel = {
  id: 0,
  name: '',
  subject: '',
  content: '',
  type: EmailTemplateTypeEnum.Enum.DEFAULT,
};

export const EmailTemplateDetailPage: React.FC<EmailTemplateDetailPageProps> = ({ disabled = false }) => {
  const id = Number(useParams().id);

  // Fetch template by id if edit mode
  const { data: emailTemplateResponse } = useFetch(NotificationsEmailTemplateApi.findById(id), { enabled: !!id });

  const form = useForm<EmailTemplateDetailModel>({
    defaultValues: DEFAULT_TEMPLATE_EMAIL,
    resolver: zodResolver(EmailTemplateDetailSchema),
    mode: 'onBlur',
    values: emailTemplateResponse?.data
      ? {
          ...emailTemplateResponse.data,
          subject: JSON.stringify(emailTemplateResponse.data.subject ?? ''),
          content: JSON.stringify(emailTemplateResponse.data.content ?? ''),
        }
      : DEFAULT_TEMPLATE_EMAIL,
  });
  const { clearErrors, control, formState, getValues, handleSubmit } = form;

  // Keep a copy of the initial values for dirty checking
  const oldTemplateEmail = useMemo(
    () => (emailTemplateResponse?.data ? trimStringFields(emailTemplateResponse.data) : DEFAULT_TEMPLATE_EMAIL),
    [emailTemplateResponse?.data],
  );

  // Breadcrumb entity update
  useBreadcrumbEntityName(oldTemplateEmail.name);

  const isUpdateMode = !!oldTemplateEmail.id && oldTemplateEmail.id !== 0;
  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);
  const navigate = useNavigate();

  const { mutate: saveMutate } = useMutate(NotificationsEmailTemplateApi.saveOrUpdate, {
    successNotification: `Email template saved successfully`,
    onSuccess: () => {
      handleNavigateToList();
    },
  });

  const handleNavigateToList = useCallback(() => {
    navigate(buildUrl(ROUTE_PATH.CONFIGURATION_NOTIFICATIONS_EMAIL_TEMPLATES), { state: buildNavigateState({ fromDetail: true }) });
  }, [navigate]);

  const handleSave = useCallback(() => {
    const { content, subject } = getValues();
    const cleanedSubject = removeMentionLabelAndStringify(subject);
    const cleanedContent = removeMentionLabelAndStringify(content);

    const payload = {
      ...getValues(),
      subject: cleanedSubject,
      content: cleanedContent,
    };

    saveMutate(trimStringFields(payload));
  }, [saveMutate, getValues]);

  const handleChangeField = useCallback(
    (field: keyof EmailTemplateDetailModel, value: string | null, fieldControl: ControllerRenderProps<EmailTemplateDetailModel>) => {
      if (formState.errors[field]) {
        clearErrors(field);
      }
      fieldControl.onChange(value);
    },
    [formState.errors, clearErrors],
  );

  const handleExistWithoutSave = useCallback(() => {
    const normalizedCurrent = trimStringFields(getValues());
    if (
      normalizedCurrent.name !== oldTemplateEmail.name ||
      normalizedCurrent.subject !== JSON.stringify(oldTemplateEmail.subject) ||
      normalizedCurrent.content !== JSON.stringify(oldTemplateEmail.content)
    ) {
      openModal();
    } else {
      handleNavigateToList();
    }
  }, [getValues, oldTemplateEmail, openModal, handleNavigateToList]);

  return (
    <Box>
      <form onSubmit={handleSubmit(handleSave)}>
        <HeaderTitleComponent
          title=''
          rightSection={
            <Flex direction='row' align='center' gap='xs'>
              <KanbanButton size='xs' variant='subtle' onClick={handleExistWithoutSave} type='button'>
                Cancel
              </KanbanButton>
              <KanbanButton size='xs' variant='filled' type='submit'>
                Save
              </KanbanButton>
            </Flex>
          }
          leftSection={
            <Flex direction='row' align='center' gap='xs'>
              <KanbanIconButton size='sm' variant='subtle' onClick={handleExistWithoutSave} type='button'>
                <IconArrowLeft />
              </KanbanIconButton>
              <KanbanTitle fz='h4'>{isUpdateMode ? 'Edit Template Email' : 'Add Template Email'}</KanbanTitle>
            </Flex>
          }
        />

        <Box className={customStyled.elementBorder}>
          <Flex>
            <Controller
              control={control}
              render={({ field }) => {
                return (
                  <KanbanSelect
                    required
                    value={field.value}
                    defaultValue={EmailTemplateTypeEnum.Enum.DEFAULT}
                    onChange={(val) => {
                      if (val) {
                        handleChangeField('type', val, field);
                      }
                    }}
                    data={typeOptions}
                    placeholder='Email Template Type'
                    label='Type'
                  />
                );
              }}
              name={'type'}></Controller>
          </Flex>
          <NameValidationInput form={form} name={emailTemplateResponse?.data?.name} id={id} />

          <Controller
            control={control}
            name={'subject'}
            render={({ field }) => {
              return (
                <CustomContentComponent
                  disabled={disabled}
                  label='Subject'
                  withAsterisk
                  value={field.value}
                  onChange={(val) => handleChangeField('subject', val, field)}
                  contentMaxLength={TextLengthConstants.EMAIL_SUBJECT_MAX_LENGTH}
                  error={formState.errors['subject']?.message}
                  singleLineMode={true}
                />
              );
            }}></Controller>

          <Controller
            control={control}
            name={'content'}
            render={({ field }) => (
              <CustomContentComponent
                disabled={disabled}
                label='Content'
                withAsterisk
                value={field.value}
                onChange={(val) => handleChangeField('content', val, field)}
                contentMaxLength={TextLengthConstants.EMAIL_CONTENT_MAX_LENGTH}
                docsToolbar={true}
                error={formState.errors['content']?.message}
                minHeightRte={300}
              />
            )}></Controller>
        </Box>
        <UnsaveConfirmModal opened={openedModal} onClose={closeModal} onConfirm={handleNavigateToList} />
      </form>
    </Box>
  );
};

export default EmailTemplateDetailPage;
