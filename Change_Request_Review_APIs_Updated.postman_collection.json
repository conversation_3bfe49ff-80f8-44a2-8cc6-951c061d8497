{"info": {"name": "Change Request Review APIs - Comprehensive Test Suite", "description": "Comprehensive collection for testing Change Request Review Controller APIs with success and error scenarios", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "12345678"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}, {"key": "changeRequestId", "value": "511", "type": "string"}, {"key": "authToken", "value": "your-bearer-token-here", "type": "string"}], "item": [{"name": "SUCCESS SCENARIOS", "item": [{"name": "1. Find Change Request Review - Success", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has valid structure', function () {", "    const responseJson = pm.response.json();", "    pm.expect(response<PERSON>son).to.have.property('owner');", "    pm.expect(responseJson).to.have.property('status');", "    pm.expect(responseJson).to.have.property('note');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/v1/change-requests/{{changeRequestId}}/review", "host": ["{{baseUrl}}"], "path": ["api", "v1", "change-requests", "{{changeRequestId}}", "review"]}, "description": "Successfully retrieves change request review details"}}, {"name": "2. Save or Update for Coordinator - Success", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200 or 201', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", "", "pm.test('Response time is less than 2000ms', function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"owner\": \"thenx.os\",\n  \"note\": \"Coordinator has reviewed and assigned this change request to the designated owner for further processing.\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/change-requests/{{changeRequestId}}/review/saveOrUpdateForCoordinator", "host": ["{{baseUrl}}"], "path": ["api", "v1", "change-requests", "{{changeRequestId}}", "review", "saveOrUpdateForCoordinator"]}, "description": "Coordinator successfully creates or updates review with owner assignment"}}, {"name": "3. Save or Update for Owner with File - Success", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200 or 201', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", "", "pm.test('File upload successful', function () {", "    pm.response.to.not.have.jsonBody('error');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "request", "value": "{\n  \"approvers\": [\"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\"],\n  \"documentName\": \"change-request-review-document.pdf\",\n  \"note\": \"Owner has completed the review with supporting documentation. All technical requirements have been analyzed and documented.\"\n}", "type": "text", "contentType": "application/json"}, {"key": "file", "type": "file", "src": [], "description": "Upload a valid Word, PDF, or Excel file (max 20MB)"}]}, "url": {"raw": "{{baseUrl}}/api/v1/change-requests/{{changeRequestId}}/review/saveOrUpdateForOwner", "host": ["{{baseUrl}}"], "path": ["api", "v1", "change-requests", "{{changeRequestId}}", "review", "saveOrUpdateForOwner"]}, "description": "Owner successfully updates review with file upload and approver assignment"}}, {"name": "4. Save or Update for Owner without File - Success", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200 or 201', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", "", "pm.test('Update without file successful', function () {", "    pm.response.to.not.have.jsonBody('error');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "request", "value": "{\n  \"approvers\": [\"<EMAIL>\", \"<EMAIL>\"],\n  \"documentName\": \"review-summary.docx\",\n  \"note\": \"Owner review completed without file upload. Text-based review with detailed analysis and recommendations.\"\n}", "type": "text", "contentType": "application/json"}]}, "url": {"raw": "{{baseUrl}}/api/v1/change-requests/{{changeRequestId}}/review/saveOrUpdateForOwner", "host": ["{{baseUrl}}"], "path": ["api", "v1", "change-requests", "{{changeRequestId}}", "review", "saveOrUpdateForOwner"]}, "description": "Owner successfully updates review without file upload"}}, {"name": "5. Update Review Status to SENT_TO_OWNER - Success", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Status transition successful', function () {", "    pm.response.to.not.have.jsonBody('error');", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/v1/change-requests/{{changeRequestId}}/review/updateReviewStatus?status=SENT_TO_OWNER", "host": ["{{baseUrl}}"], "path": ["api", "v1", "change-requests", "{{changeRequestId}}", "review", "updateReviewStatus"], "query": [{"key": "status", "value": "SENT_TO_OWNER", "description": "Coordinator sends review to owner"}]}, "description": "Successfully transitions status from IN_PROGRESS to SENT_TO_OWNER"}}, {"name": "6. Update Review Status to SENT_TO_APPROVER - Success", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Status transition successful', function () {", "    pm.response.to.not.have.jsonBody('error');", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/v1/change-requests/{{changeRequestId}}/review/updateReviewStatus?status=SENT_TO_APPROVER", "host": ["{{baseUrl}}"], "path": ["api", "v1", "change-requests", "{{changeRequestId}}", "review", "updateReviewStatus"], "query": [{"key": "status", "value": "SENT_TO_APPROVER", "description": "Owner sends review to approvers"}]}, "description": "Successfully transitions status from SENT_TO_OWNER to SENT_TO_APPROVER"}}, {"name": "7. Update Approver Status - ACCEPT", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Approval successful', function () {", "    pm.response.to.not.have.jsonBody('error');", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"approvalStatus\": \"ACCEPT\",\n  \"approver\": \"<EMAIL>\",\n  \"comment\": \"I approve this change request after thorough review. All technical requirements are met, documentation is comprehensive, and the implementation approach is sound.\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/change-requests/{{changeRequestId}}/review/updateApproverStatus", "host": ["{{baseUrl}}"], "path": ["api", "v1", "change-requests", "{{changeRequestId}}", "review", "updateApproverStatus"]}, "description": "Approver successfully accepts the change request review"}}, {"name": "8. Update Approver Status - REJECT", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Rejection successful', function () {", "    pm.response.to.not.have.jsonBody('error');", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"approvalStatus\": \"REJECT\",\n  \"approver\": \"<EMAIL>\",\n  \"comment\": \"I cannot approve this change request due to insufficient technical documentation and potential security concerns that need to be addressed before proceeding.\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/change-requests/{{changeRequestId}}/review/updateApproverStatus", "host": ["{{baseUrl}}"], "path": ["api", "v1", "change-requests", "{{changeRequestId}}", "review", "updateApproverStatus"]}, "description": "Approver successfully rejects the change request review"}}, {"name": "9. Download Review Document - Success", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response is a valid file', function () {", "    pm.response.to.have.header('Content-Type', 'application/pdf');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/v1/change-requests/{{changeRequestId}}/review/downloads", "host": ["{{baseUrl}}"], "path": ["api", "v1", "change-requests", "{{changeRequestId}}", "review", "downloads"]}, "description": "Successfully downloads the change request review document"}}]}, {"name": "ERROR SCENARIOS", "item": [{"name": "1. Find Change Request Review - Invalid ID", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 404', function () {", "    pm.response.to.have.status(404);", "});", "", "pm.test('Response has error message', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.equal('Change request review not found');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/v1/change-requests/999999/review", "host": ["{{baseUrl}}"], "path": ["api", "v1", "change-requests", "999999", "review"]}, "description": "Attempt to retrieve review for non-existent change request ID"}}, {"name": "2. Save or Update for Coordinator - Missing Owner", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 400', function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test('Response has error message', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.equal('Owner is required for coordinator review');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"note\": \"This is a coordinator note for the review process. The coordinator has reviewed the change request and assigned it to the appropriate owner.\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/change-requests/{{changeRequestId}}/review/saveOrUpdateForCoordinator", "host": ["{{baseUrl}}"], "path": ["api", "v1", "change-requests", "{{changeRequestId}}", "review", "saveOrUpdateForCoordinator"]}, "description": "Attempt to save/update review without specifying owner"}}, {"name": "3. Save or Update for Owner with File - Invalid File Type", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 400', function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test('Response has error message', function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('message');", "    pm.expect(responseJson.message).to.equal('Invalid file type. Only Word, PDF, and Excel files are allowed.');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "request", "value": "{\n  \"approvers\": [\"<EMAIL>\", \"<EMAIL>\", \"<EMAIL>\"],\n  \"documentName\": \"change-request-review-document.pdf\",\n  \"note\": \"Owner has completed the review with supporting documentation. All technical requirements have been analyzed and documented.\"\n}", "type": "text", "contentType": "application/json"}, {"key": "file", "type": "file", "src": [], "description": "Upload a valid Word, PDF, or Excel file (max 20MB)"}]}, "url": {"raw": "{{baseUrl}}/api/v1/change-requests/{{changeRequestId}}/review/saveOrUpdateForOwner", "host": ["{{baseUrl}}"], "path": ["api", "v1", "change-requests", "{{changeRequestId}}", "review", "saveOrUpdateForOwner"]}, "description": "Owner successfully updates review with file upload and approver assignment"}}, {"name": "4. Save or Update for Owner without File - Success", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200 or 201', function () {", "    pm.expect(pm.response.code).to.be.oneOf([200, 201]);", "});", "", "pm.test('Update without file successful', function () {", "    pm.response.to.not.have.jsonBody('error');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "body": {"mode": "formdata", "formdata": [{"key": "request", "value": "{\n  \"approvers\": [\"<EMAIL>\", \"<EMAIL>\"],\n  \"documentName\": \"review-summary.docx\",\n  \"note\": \"Owner review completed without file upload. Text-based review with detailed analysis and recommendations.\"\n}", "type": "text", "contentType": "application/json"}]}, "url": {"raw": "{{baseUrl}}/api/v1/change-requests/{{changeRequestId}}/review/saveOrUpdateForOwner", "host": ["{{baseUrl}}"], "path": ["api", "v1", "change-requests", "{{changeRequestId}}", "review", "saveOrUpdateForOwner"]}, "description": "Owner successfully updates review without file upload"}}, {"name": "5. Update Review Status to SENT_TO_OWNER - Success", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Status transition successful', function () {", "    pm.response.to.not.have.jsonBody('error');", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/v1/change-requests/{{changeRequestId}}/review/updateReviewStatus?status=SENT_TO_OWNER", "host": ["{{baseUrl}}"], "path": ["api", "v1", "change-requests", "{{changeRequestId}}", "review", "updateReviewStatus"], "query": [{"key": "status", "value": "SENT_TO_OWNER", "description": "Coordinator sends review to owner"}]}, "description": "Successfully transitions status from IN_PROGRESS to SENT_TO_OWNER"}}, {"name": "6. Update Review Status to SENT_TO_APPROVER - Success", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Status transition successful', function () {", "    pm.response.to.not.have.jsonBody('error');", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/v1/change-requests/{{changeRequestId}}/review/updateReviewStatus?status=SENT_TO_APPROVER", "host": ["{{baseUrl}}"], "path": ["api", "v1", "change-requests", "{{changeRequestId}}", "review", "updateReviewStatus"], "query": [{"key": "status", "value": "SENT_TO_APPROVER", "description": "Owner sends review to approvers"}]}, "description": "Successfully transitions status from SENT_TO_OWNER to SENT_TO_APPROVER"}}, {"name": "7. Update Approver Status - ACCEPT", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Approval successful', function () {", "    pm.response.to.not.have.jsonBody('error');", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"approvalStatus\": \"ACCEPT\",\n  \"approver\": \"<EMAIL>\",\n  \"comment\": \"I approve this change request after thorough review. All technical requirements are met, documentation is comprehensive, and the implementation approach is sound.\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/change-requests/{{changeRequestId}}/review/updateApproverStatus", "host": ["{{baseUrl}}"], "path": ["api", "v1", "change-requests", "{{changeRequestId}}", "review", "updateApproverStatus"]}, "description": "Approver successfully accepts the change request review"}}, {"name": "8. Update Approver Status - REJECT", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Rejection successful', function () {", "    pm.response.to.not.have.jsonBody('error');", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"approvalStatus\": \"REJECT\",\n  \"approver\": \"<EMAIL>\",\n  \"comment\": \"I cannot approve this change request due to insufficient technical documentation and potential security concerns that need to be addressed before proceeding.\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/v1/change-requests/{{changeRequestId}}/review/updateApproverStatus", "host": ["{{baseUrl}}"], "path": ["api", "v1", "change-requests", "{{changeRequestId}}", "review", "updateApproverStatus"]}, "description": "Approver successfully rejects the change request review"}}, {"name": "9. Download Review Document - Success", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response is a valid file', function () {", "    pm.response.to.have.header('Content-Type', 'application/pdf');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/v1/change-requests/{{changeRequestId}}/review/downloads", "host": ["{{baseUrl}}"], "path": ["api", "v1", "change-requests", "{{changeRequestId}}", "review", "downloads"]}, "description": "Successfully downloads the change request review document"}}]}]}