import { callRequest, RequestConfig } from '../api/BaseApi';
import {
  GetNextPageParamFunction,
  GetPreviousPageParamFunction,
  InfiniteData,
  QueryKey,
  useInfiniteQuery,
  UseInfiniteQueryOptions,
} from '@tanstack/react-query';
import { getQueryKey } from '@common/utils/QueryUtils';
import { AppConfig } from './AppConfigTypes';
import useConfigNotification from './useConfigNotification';

type InfiniteFetchAppConfig<TQueryFnData> = Omit<AppConfig<TQueryFnData>, 'successNotification'> &
  Omit<
    UseInfiniteQueryOptions<TQueryFnData, Error, InfiniteData<TQueryFnData, number>, TQueryFnData, QueryKey, number>,
    'queryKey' | 'queryFn' | 'getNextPageParam' | 'getPreviousPageParam' | 'initialPageParam'
  >;

type InfiniteConfig<Response, SearchParam, Data> = {
  getPageParam: (requestConfig: RequestConfig<Response, SearchParam, Data>) => number;
  getNextPageParam: GetNextPageParamFunction<number, Response>;
  getPreviousPageParam?: GetPreviousPageParamFunction<number, Response>;
};

function useInfiniteFetchCore<Response, SearchParam, Data = unknown>(
  requestConfig: RequestConfig<Response, SearchParam, Data>,
  infiniteConfig: InfiniteConfig<Response, SearchParam, Data>,
  appConfig?: InfiniteFetchAppConfig<Response>,
) {
  const { errorNotification, showLoading, throwParsedError = true, withSignal = true, ...otherConfig } = appConfig || {};
  const { getNextPageParam, getPageParam, getPreviousPageParam } = infiniteConfig;
  const queryResult = useInfiniteQuery<Response, Error, InfiniteData<Response, number>, QueryKey, number>({
    queryKey: getQueryKey(requestConfig),
    queryFn: ({ pageParam, signal }) =>
      callRequest(
        {
          ...requestConfig,
          data: { ...requestConfig.data, page: pageParam },
          params: { ...requestConfig.params, page: pageParam },
          signal: withSignal ? signal : undefined,
        },

        { showLoading, throwParsedError },
      ),
    initialPageParam: getPageParam(requestConfig),
    getNextPageParam: getNextPageParam,
    getPreviousPageParam: getPreviousPageParam,
    ...otherConfig,
  });
  useConfigNotification(queryResult, { errorNotification });
  return queryResult;
}

export default useInfiniteFetchCore;
