#Chi sua gia tri, khong thay doi key
# So luong pod duoc tao
replicaCount: 1

# Khai bao service
# Type = NodePort cap nhat cac truong (nodePort,port,targetPort)
# Type = LoadBalancer cap nhat cac truong (port,targetPort,loadBalancerIP)
# Type = ClusterIP cap nhat cac truong (port,targetPort)
service:
  type: ClusterIP
  nodePort: 8001
  port: 8001
  targetPort: 8001

# Khai bao gRPC service
gRPCEnable: false
gRPCService:
  nodePortgrpc: 10780
  portgrpc: 10780


# Khai bao hostnames
hostAliases:
   
# Khai bao su dung configmap
configMaps:
  - nginx.conf

# Khai bao su dung PVC
pvc: false
namePVC: 
  - pvc-cmv
  - pvc-crm-next-gen
   
# Khai bao volumeMount
volumeMountsMap:
  mountPathconfig: /deployment/config
  mountPathpodinfo: /deployment/podinfo   
  mountPathPVC:
    pvc-cmv: /data
    pvc-crm-next-gen: /crmdata
  
#Khai bao tai nguyen su dung cho pod
resources:
  requests:
    memory: 128Mi

# <PERSON><PERSON> bao label cho istio
labels: false
templateLabels:
  version: old
  sidecar.istio.io/inject: "true"
  
# Khai bao promethus de gia tri promethus true,nguoc lai de false
promethus: false
podAnnotations:
  prometheus.io/scrape: "true"
  prometheus.io/path: /actuator/prometheus
  prometheus.io/port: "8001"

# Khai bao HealthCheck de gia tri healthCheck true,nguoc lai de false
healthCheck: false
startupProbe:
  failureThreshold: 5
  httpGet:
    path: /actuator/health
    port: 8001
    scheme: HTTP
  initialDelaySeconds: 60 #depends on time app need to start
  periodSeconds: 15
  successThreshold: 1
  timeoutSeconds: 2
livenessProbe:
  failureThreshold: 3
  httpGet:
    path: /actuator/health
    port: 8001
    scheme: HTTP
  periodSeconds: 20
  successThreshold: 1
  timeoutSeconds: 2
readinessProbe:
  failureThreshold: 3
  httpGet:
    path: /actuator/health
    port: 8001
    scheme: HTTP
  periodSeconds: 20
  successThreshold: 1
  timeoutSeconds: 2

Command:
  # -- Options: [java, golang, dotnet, python-uvicorn, python-gunicorn, python, angular, manual]
  option: angular

# Khai bao bien moi truong 
ENV_PROFILE: tanzu
JVM_OPTS: -Xms1024m -Xmx2048m -Dspring.config.location=/deployment/config/application.properties -Dsun.net.http.retryPost=false -Dhttp.retryPost=false
TZ: Asia/Ho_Chi_Minh
  
# Chi dinh pod chay tren node co dinh(chi dung tren live: default hoac ecm)
NodeSelectorEnable: false
nodeName: default

# Khai bao hpa thi de gia tri hpa true,,nguoc lai de false
autoscaling:
  enabled: false
  minPodsHPA: 2
  maxPodsHPA: 4
  cpuHPA: 80
  memoryHPA: 80

##################Khong duoc chinh ben duoi#########################
# So lan deploy Helm
numberHelm: H1

# Thong tin image
image:
  repository: image_version
  pullPolicy: Always
  tag: "image_tag"

# Khai bao secret-registry harbor
imagePullSecrets:
 - name: harbor-secret-registry

## Tao Service Account
serviceAccount:
  create: false
  # name: k8s_sa

### Configure kind
#statefulset: true
#canary: true
#Ten project thuc hien canary va role opa luu tren keycloak
canary: false
projectName: ms-card-partner-new

# OpaEnvoy
### Dung thi copy noi dung duoi vao file values
opaEnvoy:
  enabled : false
  OPA_ENV: prod ### images base opa to env
  label: default 
  opalServer: http://opal-server-service.opa-istio.svc.cluster.local:8003

### Cau hinh dung vault tap chung
VaultInject: false

# Khai bao su dung configmap tap chung
# ConfigMapsCommonName: Ten Configmap-Common.
# ConfigMapsCommonData: Doi voi Configmap-Common co nhieu files,  ConfigMapsCommonData chi ra se su dung file nao trong Confimap-Common tren.
ConfigCommon: false
ConfigMapsCommonName: esb-common-config
ConfigMapsCommonData:
  - application-common.properties
# Trong Dockerfile se khai bao them CONFIGMAP_COMMON trong ENTRYPOINT de su dung configmap-common.
# Vi Du: ENTRYPOINT exec java $JVM_OPTS -jar -Doracle.jdbc.timezoneAsRegion=false interest-fee-management.jar --spring.config.location=$CONFIGMAP_COMMON,file:/deployment/config/application.properties 
CONFIGMAP_COMMON: file:/deployment/common/application-common.properties

# Khai bao user
## User appadmin, ID 10000
## User nginx, ID 101
### Configure Pods Security Context
podSecurityContext:
  enabled: true
  fsGroup: 101
  
### Configure Container Security Context
containerSecurityContext:
  enabled: true
  runAsUser: 101
  runAsNonRoot: true
  readOnlyRootFilesystem: false

# Thoi gian gia han truc khi vung chua bi cham dut #
TerminationGracePeriodSeconds:
  enabled: false
  graceperiodseconds: 1800