import React, { useMemo, useState, useEffect } from 'react';
import { ChangeRequestReviewStatusEnum } from '@common/constants/ChangeRequestReviewStatusConstants';
import { Group, Paper, Stack, Table, Modal } from '@mantine/core';
import { KanbanButton, KanbanTextarea, KanbanTitle } from 'kanban-design-system';
import { SelectWithPage } from '@components/SelectWithPage';
import ComboboxLoadMore from '@components/ComboboxLoadMore';
import { getDefaultTableAffected, TableAffactedSafeType } from 'kanban-design-system';
import useInfiniteFetch from '@core/hooks/useInfiniteFetch';
import useFetch from '@core/hooks/useFetch';
import { UserApi } from '@api/UserApi';
import { initOrUpdatedFilterPayloads, UserPageFilter } from '@pages/configuration/user/users';
import { ChangeRequestReviewApi } from '@api/ChangeRequestReviewApi';
import { ReviewFormValues } from './ReviewFormValues';
import useMutate from '@core/hooks/useMutate';
import { ACCEPTED_FILE_TYPES } from '@common/constants/ChangeRequestConstants';
import classes from '@pages/change/request/document/components/table/CommonTable.module.scss';
import reviewClasses from './ReviewTab.module.scss';
import { useSelector } from 'react-redux';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import { ChangeRequestReviewApprovalStatusEnum } from '@common/constants/ChangeRequestReviewApprovalStatusConstants';
import { ChangeRequestApi } from '@api/ChangeRequestApi';

export const ReviewTab: React.FC<{ changeRequestId: number; isViewMode: boolean }> = ({ changeRequestId, isViewMode }) => {
  // Get current user information for role-based visibility
  const currentUserState = useSelector(getCurrentUser);
  const currentUser = currentUserState.userInfo;
  const currentUsername = currentUser?.userName || '';

  const [ownerFilters, setOwnerFilters] = useState<UserPageFilter>({});
  const [approverFilters, setApproverFilters] = useState<UserPageFilter>({});
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [skipFileUpload, setSkipFileUpload] = useState<boolean>(false);
  const [review, setReview] = useState<ReviewFormValues>({
    owner: '',
    approvers: [],
    status: ChangeRequestReviewStatusEnum.Enum.IN_PROGRESS,
    lastestComment: '',
    note: '',
    documentName: '',
    action: null,
  });

  // Modal states for approval/rejection
  const [isApprovalModalOpen, setIsApprovalModalOpen] = useState(false);
  const [approvalAction, setApprovalAction] = useState<typeof ChangeRequestReviewApprovalStatusEnum._type | null>(null);
  const [approvalComment, setApprovalComment] = useState('');
  const [showCommentError, setShowCommentError] = useState(false);

  // Fetch change request data to get coordinator info
  const { data: changeRequestData } = useFetch(ChangeRequestApi.findWithId(changeRequestId), {
    showLoading: false,
  });

  const makeParams = (filters: UserPageFilter): TableAffactedSafeType => {
    const baseParams = getDefaultTableAffected();
    // Reset to first page when searching to ensure we get fresh results
    const paramsWithSearch = initOrUpdatedFilterPayloads(baseParams, filters);
    if (filters.filterUsername) {
      paramsWithSearch.page = 0; // Reset to first page when searching
    }
    return paramsWithSearch;
  };

  const ownerParams = useMemo(() => makeParams(ownerFilters), [ownerFilters]);
  const approverParams = useMemo(() => makeParams(approverFilters), [approverFilters]);

  const {
    fetchNextPage: ownerFetchMore,
    flatData: ownerUsers,
    isFetching: isOwnerLoading,
  } = useInfiniteFetch(UserApi.findAll(ownerParams), { showLoading: false });

  const {
    fetchNextPage: approverFetchMore,
    flatData: approverUsers,
    isFetching: isApproverLoading,
  } = useInfiniteFetch(UserApi.findAll(approverParams), { showLoading: false });

  const ownerOptions = useMemo(() => ownerUsers.map((u) => ({ value: u.userName, label: `${u.name} (${u.userName})` })), [ownerUsers]);

  // Options for ComboboxLoadMore (approvers)
  const approverComboboxOptions = useMemo(
    () =>
      approverUsers.map((u) => ({
        id: u.userName,
        name: u.name ? `${u.name} (${u.userName})` : u.userName, // Full format for dropdown display
        userName: u.userName,
        fullName: u.name, // Keep full name for search functionality
      })),
    [approverUsers],
  );

  const statusOptions = [
    { label: 'In Progress', value: ChangeRequestReviewStatusEnum.Enum.IN_PROGRESS },
    { label: 'Sent to Owner', value: ChangeRequestReviewStatusEnum.Enum.SENT_TO_OWNER },
    { label: 'Sent to Approver', value: ChangeRequestReviewStatusEnum.Enum.SENT_TO_APPROVER },
    { label: 'Approved', value: ChangeRequestReviewStatusEnum.Enum.APPROVED },
    { label: 'Rejected', value: ChangeRequestReviewStatusEnum.Enum.REJECTED },
  ];

  const updateReview = (field: keyof ReviewFormValues, value: any) => {
    setReview((prev) => ({ ...prev, [field]: value }));
  };

  const removeOwner = () => {
    updateReview('owner', '');
  };

  // Conditional field editing based on review status
  const isStatusInProgress = review.status === ChangeRequestReviewStatusEnum.Enum.IN_PROGRESS;

  // Role-based visibility logic
  const isChangeCoordinator = useMemo(() => {
    // Check if current user is the coordinator of this change request
    if (!currentUser || !changeRequestData?.data) {
      return false;
    }

    // Compare current user's username with the coordinator field from change request
    return currentUsername === changeRequestData.data.coordinator;
  }, [currentUser, currentUsername, changeRequestData]);

  const isOwner = useMemo(() => {
    return currentUsername === review.owner;
  }, [currentUsername, review.owner]);

  const isApprover = useMemo(() => {
    return review.approvers?.some((a) => a.approver === currentUsername) ?? false;
  }, [review.approvers, currentUsername]);

  // Field editability logic with role-based restrictions
  const isOwnerFieldEditable = isEditMode && !isViewMode && isChangeCoordinator; // Only Coordinator can edit Owner field
  const isApproverFieldEditable = isEditMode && !isViewMode && isOwner && !isStatusInProgress; // Only Owner can edit Approvers field (not when IN_PROGRESS)
  const isDocumentFieldEditable = isEditMode && !isViewMode && isOwner && !isStatusInProgress; // Only Owner can edit Document field (not when IN_PROGRESS)
  const isNoteFieldEditable = isEditMode && !isViewMode && (isChangeCoordinator || isOwner); // Only Coordinator can edit Note field

  // Button visibility logic
  const showEditButton = useMemo(() => {
    return isChangeCoordinator || isOwner;
  }, [isChangeCoordinator, isOwner]);

  const showSendToOwnerButton = useMemo(() => {
    return isChangeCoordinator;
  }, [isChangeCoordinator]);

  const showSendToApproverButton = useMemo(() => {
    return isOwner;
  }, [isOwner]);

  const showAcceptRejectButtons = useMemo(() => {
    return isApprover && review.status === ChangeRequestReviewStatusEnum.Enum.SENT_TO_APPROVER;
  }, [isApprover, review.status]);

  // Check if current user has already provided approval status
  const currentUserApprovalStatus = useMemo(() => {
    const currentApprover = review.approvers?.find((a) => a.approver === currentUsername);
    return currentApprover?.approvalStatus;
  }, [review.approvers, currentUsername]);

  // Determine if ACCEPT/REJECT buttons should be disabled
  const areApprovalButtonsDisabled = useMemo(() => {
    return currentUserApprovalStatus !== null && currentUserApprovalStatus !== undefined;
  }, [currentUserApprovalStatus]);

  // Get display text for current user's approval status
  const currentUserApprovalStatusText = useMemo(() => {
    switch (currentUserApprovalStatus) {
      case 'ACCEPT':
        return 'You have already approved this review';
      case 'REJECT':
        return 'You have already rejected this review';
      default:
        return null;
    }
  }, [currentUserApprovalStatus]);

  // Action button handlers
  const handleEdit = () => {
    setIsEditMode(true);
  };

  // Mutation for saving review data as owner (with file upload support)
  const { mutate: saveAsOwner } = useMutate(
    () => {
      // Validate required fields before making API call
      const approvers = review.approvers?.map((a) => a.approver?.trim()).filter((a): a is string => Boolean(a)) ?? [];

      const note = review.note?.trim() || '';
      const owner = review.owner?.trim() || '';

      if (approvers.length === 0) {
        throw new Error('At least one approver is required');
      }
      if (!note) {
        throw new Error('Note is required');
      }
      if (!owner) {
        throw new Error('Owner is required');
      }

      // File handling logic:
      // - If selectedFile exists: upload new file (replaces existing)
      // - If selectedFile is null/undefined: preserve existing document (no file change)
      // - skipFileUpload flag informs backend about file upload behavior
      return ChangeRequestReviewApi.saveOrUpdateForOwner(
        changeRequestId,
        {
          approvers: approvers as [string, ...string[]], // Type assertion since we validated length > 0
          owner,
          note,
          skipFileUpload, // Include skipFileUpload in the request data structure
        },
        selectedFile || undefined,
      );
    },
    {
      successNotification: 'Review saved successfully',
      errorNotification: (error) => ({ message: error.message }),
      onSuccess: () => {
        setIsEditMode(false);
        setSelectedFile(null);
        setSkipFileUpload(false);
        refetchReviewData();
      },
    },
  );

  // Mutation for saving review data as coordinator
  const { mutate: saveAsCoordinator } = useMutate(
    () => {
      const owner = review.owner?.trim() || '';
      const note = review.note?.trim() || '';

      if (!owner) {
        throw new Error('Owner is required');
      }
      if (!note) {
        throw new Error('Note is required');
      }

      return ChangeRequestReviewApi.saveOrUpdateForCoordinator(changeRequestId, {
        owner,
        note,
      });
    },
    {
      successNotification: 'Review saved successfully',
      errorNotification: (error) => ({ message: error.message }),
      onSuccess: () => {
        setIsEditMode(false);
        refetchReviewData();
      },
    },
  );

  const handleSave = () => {
    // Determine which API to use based on user role or review state
    // For now, use owner API if approvers are set, coordinator API if owner is set
    if (review.approvers && review.approvers.length > 0) {
      saveAsOwner(undefined);
    } else if (review.owner) {
      saveAsCoordinator(undefined);
    } else {
      setIsEditMode(false);
    }
  };

  const handleCancel = () => {
    // Reset form to original values
    if (reviewData?.data) {
      const data = reviewData.data;
      setReview({
        owner: data.owner || '',
        approvers: data.approvers || [],
        status: data.status,
        lastestComment: data.comment || '',
        note: data.note || '',
        documentName: data.documentName || '',
        action: null,
      });
    }
    setIsEditMode(false);
  };

  // Mutation for updating review status
  const { mutate: updateReviewStatus } = useMutate((status: string) => ChangeRequestReviewApi.updateReviewStatus(changeRequestId, status), {
    successNotification: 'Review status updated successfully',
    errorNotification: (error) => ({ message: error.message }),
    onSuccess: () => {
      // Refetch review data after successful update
      refetchReviewData();
    },
  });

  // Mutation for approver actions
  const { mutate: updateApproverStatus } = useMutate(
    (data: { approvalStatus: 'ACCEPT' | 'REJECT'; comment: string }) => ChangeRequestReviewApi.updateApproverStatus(changeRequestId, data),
    {
      successNotification: 'Approver status updated successfully',
      errorNotification: (error) => ({ message: error.message }),
      onSuccess: () => {
        refetchReviewData();
      },
    },
  );

  // Mutation for downloading review document
  const { mutate: downloadDocument } = useMutate<Blob, void>(() => ChangeRequestReviewApi.downloadReviewDocument(changeRequestId), {
    successNotification: 'Document downloaded successfully',
    errorNotification: (error) => ({ message: error.message }),
    onSuccess: (blob) => {
      if (blob instanceof Blob) {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', review.documentName || 'review-document');
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url);
      }
    },
  });

  const handleDownloadDocument = () => {
    downloadDocument();
  };

  const handleSendToOwner = () => {
    updateReviewStatus(ChangeRequestReviewStatusEnum.Enum.SENT_TO_OWNER);
  };

  const handleSendToApprover = () => {
    if (review.approvers && review.approvers.length > 0) {
      updateReviewStatus(ChangeRequestReviewStatusEnum.Enum.SENT_TO_APPROVER);
    }
  };

  const handleAccept = () => {
    setApprovalAction(ChangeRequestReviewApprovalStatusEnum.Enum.ACCEPT);
    setApprovalComment('');
    setShowCommentError(false);
    setIsApprovalModalOpen(true);
  };

  const handleReject = () => {
    setApprovalAction(ChangeRequestReviewApprovalStatusEnum.Enum.REJECT);
    setApprovalComment('');
    setShowCommentError(false);
    setIsApprovalModalOpen(true);
  };

  const handleApprovalSubmit = () => {
    if (!approvalComment.trim()) {
      setShowCommentError(true);
      return; // Comment is required
    }

    if (approvalAction) {
      updateApproverStatus({
        approvalStatus: approvalAction,
        comment: approvalComment.trim(),
      });
    }

    // Close modal and reset states
    setIsApprovalModalOpen(false);
    setApprovalAction(null);
    setApprovalComment('');
    setShowCommentError(false);
  };

  const handleApprovalCancel = () => {
    setIsApprovalModalOpen(false);
    setApprovalAction(null);
    setApprovalComment('');
    setShowCommentError(false);
  };

  // Fetch review data from API
  const { data: reviewData, refetch: refetchReviewData } = useFetch(ChangeRequestReviewApi.findChangeRequestReview(changeRequestId), {
    showLoading: false,
  });

  // Update review when data is loaded
  useEffect(() => {
    if (reviewData?.data) {
      const data = reviewData.data;
      setReview({
        owner: data.owner || '',
        approvers: data.approvers || [],
        status: data.status,
        lastestComment: data.comment || '',
        note: data.note || '',
        documentName: data.documentName || '',
        action: null,
      });
      // Initialize skipFileUpload state from API response
      setSkipFileUpload(data.skipFileUpload || false);
    }
  }, [reviewData]);

  return (
    <Stack gap='md'>
      <Paper withBorder p='md'>
        <Group justify='space-between' align='center' mb='sm'>
          <KanbanTitle fz='h6'>Review</KanbanTitle>
          <Group gap='sm'>
            {/* Edit/Save/Cancel buttons */}
            {!isViewMode && !isEditMode && showEditButton && (
              <KanbanButton variant='outline' color='gray' onClick={handleEdit}>
                Edit
              </KanbanButton>
            )}
            {isEditMode && (
              <>
                <KanbanButton variant='outline' color='gray' onClick={handleCancel}>
                  Cancel
                </KanbanButton>
                <KanbanButton variant='filled' color='blue' onClick={handleSave}>
                  Save
                </KanbanButton>
              </>
            )}

            {/* Status action buttons */}
            {!isEditMode && (
              <>
                {showSendToOwnerButton && (
                  <KanbanButton variant='filled' color='orange' onClick={handleSendToOwner}>
                    Send to Owner
                  </KanbanButton>
                )}

                {showSendToApproverButton && review.approvers && review.approvers.length > 0 && (
                  <KanbanButton variant='filled' color='blue' onClick={handleSendToApprover}>
                    Send to Approver
                  </KanbanButton>
                )}

                {showAcceptRejectButtons && (
                  <>
                    {currentUserApprovalStatusText && (
                      <KanbanTitle order={6} style={{ color: currentUserApprovalStatus === 'ACCEPT' ? 'green' : 'red', fontWeight: 500 }}>
                        {currentUserApprovalStatusText}
                      </KanbanTitle>
                    )}
                    <KanbanButton variant='filled' color='green' onClick={handleAccept} disabled={areApprovalButtonsDisabled}>
                      Accept
                    </KanbanButton>
                    <KanbanButton variant='filled' color='red' onClick={handleReject} disabled={areApprovalButtonsDisabled}>
                      Reject
                    </KanbanButton>
                  </>
                )}
              </>
            )}
          </Group>
        </Group>

        <Table striped highlightOnHover withTableBorder withColumnBorders className={classes.table}>
          <Table.Thead>
            <Table.Tr>
              <Table.Th className={`${classes.headerCell} ${classes.ownerColumn}`}>Owner</Table.Th>
              <Table.Th className={`${classes.headerCell} ${classes.documentColumn}`}>Document</Table.Th>
              <Table.Th className={`${classes.headerCell} ${classes.approverColumn}`}>Approver</Table.Th>
              <Table.Th className={`${classes.headerCell} ${classes.statusColumn}`}>Status</Table.Th>
              <Table.Th className={`${classes.headerCell} ${classes.commentColumn}`}>Comment</Table.Th>
            </Table.Tr>
          </Table.Thead>

          <Table.Tbody>
            <Table.Tr>
              {/* Owner */}
              <Table.Td className={`${classes.cell} ${classes.ownerColumn}`}>
                <div className={reviewClasses.ownerFieldContainer}>
                  {review.owner ? (
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <span>{ownerOptions.find((o) => o.value === review.owner)?.label || review.owner}</span>
                      {isOwnerFieldEditable && (
                        <button
                          onClick={() => removeOwner()}
                          style={{
                            background: 'none',
                            border: 'none',
                            color: '#999',
                            cursor: 'pointer',
                            fontSize: '16px',
                            padding: '0',
                            lineHeight: '1',
                          }}
                          title='Remove owner'>
                          ×
                        </button>
                      )}
                    </div>
                  ) : (
                    <SelectWithPage
                      options={ownerOptions}
                      onChange={(val) => updateReview('owner', val || '')}
                      onSearch={(val) => setOwnerFilters((p: UserPageFilter) => ({ ...p, filterUsername: val }))}
                      onBlur={() => setOwnerFilters((p: UserPageFilter) => ({ ...p, filterUsername: '' }))}
                      handleScrollToBottom={ownerFetchMore}
                      isLoading={isOwnerLoading}
                      readOnly={!isOwnerFieldEditable}
                    />
                  )}
                </div>
              </Table.Td>

              {/* Document */}
              <Table.Td className={`${classes.cell} ${classes.documentColumn}`}>
                <div className={reviewClasses.documentFieldContainer}>
                  <Group gap={4}>
                    <KanbanButton
                      variant='outline'
                      size='xs'
                      disabled={!isDocumentFieldEditable}
                      className={`${reviewClasses.documentButton} ${!isDocumentFieldEditable ? reviewClasses.disabled : reviewClasses.enabled}`}
                      onClick={() => {
                        const input = document.createElement('input');
                        input.type = 'file';
                        input.accept = ACCEPTED_FILE_TYPES;
                        input.onchange = async (e: any) => {
                          const file = e.target.files[0];
                          if (file) {
                            updateReview('documentName', file.name);
                            // Store the file for later upload and set skipFileUpload to false since we have a new file
                            setSelectedFile(file);
                            setSkipFileUpload(false);
                          }
                        };
                        input.click();
                      }}>
                      {selectedFile ? selectedFile.name : review.documentName || '+ Add a document'}
                    </KanbanButton>

                    {review.documentName && !isEditMode && (
                      <KanbanButton
                        variant='outline'
                        size='xs'
                        color='blue'
                        className={`${reviewClasses.downloadButton} ${!isDocumentFieldEditable ? reviewClasses.disabled : reviewClasses.enabled}`}
                        onClick={() => handleDownloadDocument()}>
                        Download
                      </KanbanButton>
                    )}
                  </Group>
                </div>
              </Table.Td>

              {/* Approvers */}
              <Table.Td className={`${classes.cell} ${classes.approverColumn}`}>
                <div className={reviewClasses.approverFieldContainer}>
                  {isApproverFieldEditable ? (
                    // Edit mode: Show all approvers with infinite scroll support
                    <div className={reviewClasses.approverComboboxLoadMore}>
                      <ComboboxLoadMore
                        options={approverComboboxOptions}
                        values={
                          review.approvers
                            ?.map((a) => a.approver?.trim())
                            .filter((username): username is string => Boolean(username))
                            .map((username) => ({
                              id: username,
                              name: username, // Không dùng cho pills
                              userName: username, // renderPillLabel hiển thị username
                              fullName: username, // Đơn giản hóa: chỉ dùng username
                            })) || []
                        }
                        onChange={(selectedUsers) => {
                          // Convert ComboboxLoadMore values back to approver object structure
                          // Preserve existing approval status for existing approvers
                          const approverObjects = selectedUsers.map((user) => {
                            const existingApprover = review.approvers?.find((a) => a.approver === user.userName);
                            return {
                              approver: user.userName,
                              approvalStatus: existingApprover?.approvalStatus ?? null, // Preserve existing status or null for new approvers
                            };
                          });
                          updateReview('approvers', approverObjects);
                        }}
                        onSearch={(searchValue) => {
                          // Update search filter and reset pagination
                          setApproverFilters((p: UserPageFilter) => ({
                            ...p,
                            filterUsername: searchValue,
                          }));
                        }}
                        onBlur={() => {
                          // Clear search filter when losing focus to show all users again
                          setApproverFilters((p: UserPageFilter) => ({
                            ...p,
                            filterUsername: '',
                          }));
                        }}
                        onScroll={approverFetchMore}
                        renderPillLabel={(user) => user.userName} // Show only username in input pills
                        renderOptionLabel={(user) => user.name} // Show full format in dropdown options
                        filter={(searchValue, item) => {
                          // Allow search by both username and full name, but display only username
                          const searchLower = searchValue.toLowerCase();
                          return (
                            item.userName.toLowerCase().includes(searchLower) ||
                            (item.fullName ? item.fullName.toLowerCase().includes(searchLower) : false)
                          );
                        }}
                        placeholder='Select approvers'
                        clearable
                        isLoading={isApproverLoading}
                        scrollableForValue
                      />
                    </div>
                  ) : (
                    // Read-only mode: Show approvers as text with status-based styling
                    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                      {review.approvers
                        ?.filter((a) => Boolean(a.approver?.trim()))
                        .map((approverObj) => (
                          <span
                            key={approverObj.approver}
                            style={{
                              color:
                                approverObj.approvalStatus === 'ACCEPT' ? '#2e7d32' : approverObj.approvalStatus === 'REJECT' ? '#d32f2f' : '#1976d2',
                              fontWeight: approverObj.approvalStatus ? '500' : 'normal',
                            }}>
                            {approverObj.approver}
                            {approverObj.approvalStatus === 'ACCEPT' && ' ✓'}
                            {approverObj.approvalStatus === 'REJECT' && ' ✗'}
                          </span>
                        ))}

                      {(!review.approvers || review.approvers.length === 0) && (
                        <span style={{ color: '#999', fontStyle: 'italic' }}>No approvers selected</span>
                      )}
                    </div>
                  )}
                </div>
              </Table.Td>

              {/* Status */}
              <Table.Td className={`${classes.cell} ${classes.statusColumn}`}>
                <div className={reviewClasses.tableInputField}>
                  <span
                    style={{
                      display: 'inline-block',
                      padding: '4px 12px',
                      borderRadius: '12px',
                      fontSize: '12px',
                      fontWeight: '500',
                      backgroundColor:
                        review.status === 'IN_PROGRESS'
                          ? '#e3f2fd'
                          : review.status === 'SENT_TO_OWNER'
                            ? '#f3e5f5'
                            : review.status === 'SENT_TO_APPROVER'
                              ? '#fff3e0'
                              : review.status === 'APPROVED'
                                ? '#e8f5e8'
                                : review.status === 'REJECTED'
                                  ? '#ffebee'
                                  : '#f5f5f5',
                      color:
                        review.status === 'IN_PROGRESS'
                          ? '#1976d2'
                          : review.status === 'SENT_TO_OWNER'
                            ? '#7b1fa2'
                            : review.status === 'SENT_TO_APPROVER'
                              ? '#f57c00'
                              : review.status === 'APPROVED'
                                ? '#388e3c'
                                : review.status === 'REJECTED'
                                  ? '#d32f2f'
                                  : '#666',
                      border: '1px solid',
                      borderColor:
                        review.status === 'IN_PROGRESS'
                          ? '#bbdefb'
                          : review.status === 'SENT_TO_OWNER'
                            ? '#e1bee7'
                            : review.status === 'SENT_TO_APPROVER'
                              ? '#ffcc02'
                              : review.status === 'APPROVED'
                                ? '#c8e6c9'
                                : review.status === 'REJECTED'
                                  ? '#ffcdd2'
                                  : '#e0e0e0',
                    }}>
                    {statusOptions.find((option) => option.value === review.status)?.label || review.status}
                  </span>
                </div>
              </Table.Td>

              {/* Comment */}
              <Table.Td className={`${classes.cell} ${classes.commentColumn}`}>
                <div className={reviewClasses.noteFieldContainer}>
                  <KanbanTextarea
                    value={review.lastestComment || ''}
                    onChange={(e) => updateReview('lastestComment', e.target.value)}
                    placeholder='Comment...'
                    disabled={true}
                    maxLength={1000}
                    minRows={2}
                  />
                </div>
              </Table.Td>
            </Table.Tr>
          </Table.Tbody>
        </Table>

        {/* Note Section */}
        <Paper withBorder radius='md' p='md' mt='md' className={reviewClasses.noteSection}>
          <Stack gap='sm'>
            <KanbanTitle order={4} className={reviewClasses.noteTitle}>
              Note
            </KanbanTitle>
            <div className={reviewClasses.noteFieldContainer}>
              <KanbanTextarea
                value={review.note || ''}
                onChange={(e) => updateReview('note', e.target.value)}
                placeholder='Add your notes here...'
                disabled={!isNoteFieldEditable}
                maxLength={2000}
                minRows={3}
                className={reviewClasses.noteTextarea}
              />
            </div>
          </Stack>
        </Paper>
      </Paper>

      {/* Approval/Rejection Modal */}
      <Modal
        opened={isApprovalModalOpen}
        onClose={handleApprovalCancel}
        title={approvalAction === 'ACCEPT' ? 'Accept Review' : 'Reject Review'}
        size='md'
        centered>
        <Stack gap='md'>
          <KanbanTitle order={5} className={reviewClasses.modalTitle}>
            {approvalAction === 'ACCEPT'
              ? 'Please provide a comment for accepting this review:'
              : 'Please provide a reason for rejecting this review:'}
          </KanbanTitle>

          <KanbanTextarea
            value={approvalComment}
            onChange={(e) => {
              setApprovalComment(e.target.value);
              // Hide error when user starts typing
              if (showCommentError && e.target.value.trim()) {
                setShowCommentError(false);
              }
            }}
            placeholder={approvalAction === 'ACCEPT' ? 'Enter your approval comment...' : 'Enter your rejection reason...'}
            required
            maxLength={1000}
            minRows={3}
            error={showCommentError ? 'Comment is required' : null}
            className={reviewClasses.modalTextarea}
          />

          <Group justify='flex-end' gap='sm'>
            <KanbanButton variant='outline' color='gray' onClick={handleApprovalCancel}>
              Cancel
            </KanbanButton>
            <KanbanButton
              variant='filled'
              color={approvalAction === 'ACCEPT' ? 'green' : 'red'}
              onClick={handleApprovalSubmit}
              disabled={!approvalComment.trim()}>
              {approvalAction === 'ACCEPT' ? 'Accept' : 'Reject'}
            </KanbanButton>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  );
};
