import { AuditSchema } from '@core/schema/Common';
import { z } from 'zod';
import { INPUT_REQUIRE } from '@core/message/MesageConstant';
import { AclPermissionSchema } from '@core/schema/AclPermission';

export const RoleDetailSchema = z
  .object({
    id: z.number(),
    name: z.string().trim().nonempty(INPUT_REQUIRE),
    description: z.string().nullish(),
    toInsertPermissions: z.array(AclPermissionSchema).nullish(),
    toDeletePermissions: z.array(AclPermissionSchema).nullish(),
  })
  .merge(AuditSchema);

export type RoleDetailModel = z.infer<typeof RoleDetailSchema>;
