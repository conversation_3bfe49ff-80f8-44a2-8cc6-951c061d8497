import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Layout } from 'react-grid-layout';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { Grid } from '@mantine/core';

import { ChangeTemplate, TemplateCustomFieldTypeEnum, TemplateFieldItem } from '@core/schema/ChangeTemplate';
import {
  BREAK_FIELD_MIN_HEIGHT,
  DEFAULT_START_BREAK_FIELD_ID,
  DROP_ITEM_PREFIX,
  FIELD_MAX_HEIGHT,
  GRID_COLS,
} from '@common/constants/ChangeTemplateConstants';
import { SourceItemsPanel } from './SourceItemsPanel';
import { getDefaultTableAffected, SortOrder, useDebounceCallback, useSafetyRenderStateRef } from 'kanban-design-system';
import { CustomFieldApi } from '@api/CustomFieldApi';
import useInfiniteFetchCore from '@core/hooks/useInfiniteFetchCore';
import { NoticeChangeTemplate } from './NoticeChangeTemplate';
import { FetchNextPageOptions, InfiniteData, InfiniteQueryObserverResult } from '@tanstack/react-query';
import { Page, ResponseData } from '@core/schema/Common';
import { CustomField } from '@core/schema/CustomField';
import TemplateGrid from './TemplateGrid';
import { CustomFieldTypeEnum } from '@common/constants/CustomFieldType';

type ChangeTemplateBuilderProps = {
  layouts: Layout[];
  setLayouts: React.Dispatch<React.SetStateAction<Layout[]>>;
};

interface DroppingItem {
  i: string;
  w: number;
  h: number;
  x: number;
  y: number;
}
const getFieldDimensions = (item: TemplateFieldItem) => {
  const w =
    item.customFieldType === TemplateCustomFieldTypeEnum.enum.BREAK || item.name === 'Title' || item.name === 'Description'
      ? GRID_COLS
      : GRID_COLS / 2;
  const h = (() => {
    const height = (item as TemplateFieldItem).height;
    if (height !== undefined && height !== null) {
      return height;
    }

    switch (item.customFieldType) {
      case TemplateCustomFieldTypeEnum.enum.BREAK:
      case TemplateCustomFieldTypeEnum.enum.RICH_TEXT:
        return 3;
      default:
        return 1;
    }
  })();
  return { w, h: h as number };
};
export const isFieldResizable = (item: TemplateFieldItem) => {
  return item.customFieldType === TemplateCustomFieldTypeEnum.enum.BREAK || item.customFieldType === TemplateCustomFieldTypeEnum.enum.MULTI_LINE;
};

const getInitialFieldLayout = (newField: TemplateFieldItem, currentLayouts: Layout[], fieldMaxHeight: number): Layout => {
  const initialX = 0;
  let initialY = 0;

  if (currentLayouts.length > 0) {
    const bottomMostLayout = currentLayouts.reduce((prev, current) => {
      const prevBottom = (prev.y ?? 0) + (prev.h ?? 0);
      const currentBottom = (current.y ?? 0) + (current.h ?? 0);
      return prevBottom > currentBottom ? prev : current;
    });
    initialY = (bottomMostLayout.y ?? 0) + (bottomMostLayout.h ?? 0);
  }

  const { h, w } = getFieldDimensions(newField);

  const newLayout = {
    i: newField.customFieldId.toString(),
    x: initialX,
    y: initialY,
    w: w,
    h: h,
    minW: w,
    maxW: w,
    minH: newField.customFieldType !== TemplateCustomFieldTypeEnum.Enum.BREAK ? 1 : BREAK_FIELD_MIN_HEIGHT,
    maxH: fieldMaxHeight,
    isDraggable: true,
    isResizable: isFieldResizable(newField),
  };
  return newLayout;
};
const ChangeTemplateBuilder: React.FC<ChangeTemplateBuilderProps> = ({ layouts, setLayouts }) => {
  const [sourceSearchTerm, setSourceSearchTerm] = useState('');
  const [debouncedSourceSearchTerm, setDebouncedSourceSearchTerm] = useState(sourceSearchTerm);

  const [sourceItems, setSourceItems] = useState<TemplateFieldItem[]>([]);

  const [isDraggingSourceItem, setIsDraggingSourceItem] = useState(false);
  const [draggedSourceItem, setDraggedSourceItem] = useState<TemplateFieldItem | null>(null);
  const [droppableHighlight, setDroppableHighlight] = useState(false);
  const { control, getValues } = useFormContext<ChangeTemplate>();
  const { append, fields, remove, update } = useFieldArray({
    control,
    name: 'fields',
  });

  const fieldIdSet = useMemo(() => new Set(fields?.map((it) => it.customFieldId)), [fields]);
  const fieldBreakCount = useMemo(() => fields?.map((it) => it.customFieldType === TemplateCustomFieldTypeEnum.enum.BREAK).length || 0, [fields]);
  const breakId = useSafetyRenderStateRef(DEFAULT_START_BREAK_FIELD_ID - fieldBreakCount);
  const pageNum = useSafetyRenderStateRef(1);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSourceSearchTerm(sourceSearchTerm);
    }, 500);

    return () => {
      clearTimeout(handler);
    };
  }, [sourceSearchTerm]);

  const {
    data: sourceFieldResponse,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteFetchCore(
    CustomFieldApi.findAllAvaiableFields({
      ...getDefaultTableAffected(),
      page: pageNum.current,
      advancedFilterMapping: {
        ['name']: {
          filterOption: 'contains',
          value: {
            fromValue: debouncedSourceSearchTerm,
          },
        },
      },
      sortedBy: 'name',
      sortOrder: SortOrder.ASC,
      rowsPerPage: 100,
    }),
    {
      getPageParam: (requestConfig) => requestConfig.data?.page ?? 0,
      getNextPageParam: (lastPage) => {
        if (lastPage?.data?.number !== undefined && lastPage?.data?.totalPages !== undefined) {
          const nextPage = lastPage.data.number + 1;
          return nextPage < lastPage.data.totalPages ? nextPage : undefined;
        }
        return undefined;
      },
    },
    {
      enabled: true,
      refetchOnWindowFocus: false,
      showLoading: false,
    },
  );

  const handleFetchNextPage = useDebounceCallback(
    (opts?: FetchNextPageOptions): Promise<InfiniteQueryObserverResult<InfiniteData<ResponseData<Page<CustomField>>>>> => {
      return fetchNextPage(opts);
    },
    { delay: 500, flushOnUnmount: true },
  );

  useEffect(() => {
    if (sourceFieldResponse?.pages) {
      setSourceItems(() => {
        const items =
          sourceFieldResponse.pages
            .flatMap(
              (page) =>
                page.data?.content?.map(
                  (item) =>
                    ({
                      ...item,
                      customFieldType: item.type,
                      customFieldId: item.id,
                      type: item.type,
                      id: item.id,
                      isNew: true,
                    }) as TemplateFieldItem,
                ) || [],
            )
            .filter((it) => !fieldIdSet?.has(it.customFieldId)) || [];
        return [
          {
            customFieldId: breakId.current,
            customFieldType: TemplateCustomFieldTypeEnum.enum.BREAK,
            name: 'Page break',
            type: CustomFieldTypeEnum.Enum.RICH_TEXT,
          },
          ...items,
        ];
      });
    }
  }, [breakId, fieldIdSet, sourceFieldResponse?.pages]);

  useEffect(() => {
    if (sourceItems.length < 20 && hasNextPage) {
      handleFetchNextPage();
    }
  }, [handleFetchNextPage, hasNextPage, sourceItems.length]);
  const addNewFieldFromSourceItem = useCallback(
    (sourceItem: TemplateFieldItem) => {
      if (!sourceItem) {
        console.error('Source item is undefined or null.');
        return;
      }

      const { h, w } = getFieldDimensions(sourceItem);

      const newField: TemplateFieldItem = {
        ...sourceItem,
        customFieldId: sourceItem.customFieldId,
        customFieldType: sourceItem.customFieldType,
        iconName: sourceItem.iconName || 'pencil',
        required: false,
        height: h,
        width: w,
        isNew: true,
      };

      const currentLayouts = layouts || [];

      const newFieldLayout = getInitialFieldLayout(newField, currentLayouts, FIELD_MAX_HEIGHT);
      append(newField, { shouldFocus: false });

      setLayouts((prev) => {
        const res = [...prev, newFieldLayout];
        return res;
      });

      if (TemplateCustomFieldTypeEnum.Enum.BREAK !== sourceItem.customFieldType) {
        setSourceItems((prev) => [...prev].filter((item) => item.customFieldId !== newField.customFieldId));
      }
    },
    [append, layouts, setLayouts],
  );

  const handleDoubleClickSourceItem = useCallback(
    (sourceItem: TemplateFieldItem) => {
      addNewFieldFromSourceItem(sourceItem);
    },
    [addNewFieldFromSourceItem],
  );

  const handleLayoutChange = useCallback(
    (newLayout: Layout[]) => {
      setLayouts(newLayout);
      newLayout.forEach((layout) => {
        const fieldIndex = fields.findIndex((f) => f.customFieldId.toString() === layout.i);
        if (fields && fieldIndex !== -1) {
          const currentField = getValues(`fields.${fieldIndex}`);
          const updatedField: TemplateFieldItem = {
            ...currentField,
            verticalCoordinate: layout.y,
            horizontalCoordinate: layout.x ?? 0,
            width: layout.w ?? 1,
            height: layout.h ?? 1,
          };

          update(fieldIndex, updatedField);
        }
      });
    },
    [fields, getValues, setLayouts, update],
  );

  const handleDragSourceItemStart = (sourceItem: TemplateFieldItem) => {
    setIsDraggingSourceItem(true);
    setDraggedSourceItem(sourceItem);
    setDroppableHighlight(true);
  };

  const handleDragSourceItemEnd = useCallback(() => {
    setIsDraggingSourceItem(false);
    setDraggedSourceItem(null);
    setDroppableHighlight(false);
  }, []);

  const handleDropSourceItemOnGrid = useCallback(
    (event: React.DragEvent, _: Layout[], droppingItem: DroppingItem) => {
      event.preventDefault();

      if (draggedSourceItem) {
        const { x, y } = droppingItem;
        let defaultWidth = GRID_COLS / 2;
        let defaultHeight = 1;

        if (draggedSourceItem.customFieldType === TemplateCustomFieldTypeEnum.Enum.BREAK) {
          defaultWidth = GRID_COLS;
          defaultHeight = 3;
        } else if (draggedSourceItem.customFieldType === TemplateCustomFieldTypeEnum.Enum.RICH_TEXT) {
          defaultHeight = 3;
        }

        const newField: TemplateFieldItem = {
          ...draggedSourceItem,
          horizontalCoordinate: x,
          verticalCoordinate: y,
          width: defaultWidth,
          height: defaultHeight,
        };

        append(newField, { shouldFocus: false });

        const newFieldLayout: Layout = {
          i: newField.customFieldId.toString(),
          x: x,
          y: y,
          w: defaultWidth,
          h: defaultHeight,
          minW: defaultWidth,
          maxW: defaultWidth,
          minH: 1,
          maxH: FIELD_MAX_HEIGHT,
          isDraggable: true,
          isResizable: isFieldResizable(newField),
        };

        setLayouts((prevLayouts) => {
          const filteredLayouts = prevLayouts.filter((l) => !l.i.startsWith(DROP_ITEM_PREFIX));
          return [...filteredLayouts, newFieldLayout];
        });

        handleDragSourceItemEnd();

        setSourceItems((prev) => [...prev].filter((item) => item.customFieldId !== newField.customFieldId));
      }
    },
    [draggedSourceItem, append, setLayouts, handleDragSourceItemEnd],
  );

  const handleDragSourceItemOver = useCallback(
    (event: React.DragEvent) => {
      if (isDraggingSourceItem) {
        event.preventDefault();
      }
    },
    [isDraggingSourceItem],
  );

  const removeField = useCallback(
    (fieldId: string) => {
      const fieldIndex = fields?.findIndex((f) => f.customFieldId.toString() === fieldId);
      if (fieldIndex !== -1) {
        remove(fieldIndex);

        setLayouts((prev) => prev.filter((l) => l.i !== fieldId));
      }
    },
    [fields, remove, setLayouts],
  );

  return (
    <Grid>
      <Grid.Col span={{ base: 12, md: 2 }}>
        <SourceItemsPanel
          filteredSourceItems={sourceItems}
          searchTerm={sourceSearchTerm}
          onSearchChange={setSourceSearchTerm}
          onDoubleClickItem={handleDoubleClickSourceItem}
          onDragStart={handleDragSourceItemStart}
          onDragEnd={handleDragSourceItemEnd}
          fetchNextPage={handleFetchNextPage}
          hasNextPage={hasNextPage}
          isFetchingNextPage={isFetchingNextPage}
        />
      </Grid.Col>

      <Grid.Col span={{ base: 12, md: 10 }}>
        <TemplateGrid
          layouts={layouts}
          fields={fields || []}
          handleLayoutChange={handleLayoutChange}
          handleDropSourceItemOnGrid={handleDropSourceItemOnGrid}
          handleDragSourceItemOver={handleDragSourceItemOver}
          isDraggingSourceItem={isDraggingSourceItem}
          draggedSourceItem={draggedSourceItem}
          droppableHighlight={droppableHighlight}
          removeField={removeField}
        />
        <NoticeChangeTemplate control={control} />
      </Grid.Col>
    </Grid>
  );
};

export default ChangeTemplateBuilder;
