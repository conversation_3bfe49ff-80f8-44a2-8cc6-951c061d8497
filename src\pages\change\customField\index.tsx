import { AdvancedFilterMappingType, ColumnType, getDefaultTableAffected, KanbanIconButton, KanbanTitle, renderDateTime } from 'kanban-design-system';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanButton } from 'kanban-design-system';
import { KanbanTable, KanbanTableProps, TableAffactedSafeType } from 'kanban-design-system';
import { Box, Flex, Tooltip } from '@mantine/core';
import { IconEdit, IconPlus, IconTrash } from '@tabler/icons-react';
import equal from 'fast-deep-equal';
import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import { DateRangeFilter, dateRangeKeys } from '@components/AmtDateRangeInput';
import { LocalStorageKey } from '@common/constants/LocalStorageKeyConstants';
import { buildDetailUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import { EntityAction } from '@common/constants/EntityActionConstants';
import { FilterTextInput } from '@components/AmtFilterTextInput';
import { deleteConfirm } from '@components/utils/ConfirmMessageUtils';
import { parseFilterWithDate } from '@common/utils/DateUtils';
import { CustomField } from '@core/schema/CustomField';
import { CustomFieldApi } from '@api/CustomFieldApi';
import useSavedFilterCycle from '@core/hooks/useSavedFilterCycle';
import { useSavedColumns } from '@core/hooks/useSavedColumns';
import { CUSTOM_FIELD_TYPE_LABEL } from '@common/constants/CustomFieldType';
import { AmtShortTextContent, AmtShortTextLink } from '@components/AmtShortTextContent';

export type CustomFieldPageFilter = DateRangeFilter & {
  name?: string;
};

const initOrUpdatedFilterPayloads = (prev?: TableAffactedSafeType<CustomField>, initFilters?: CustomFieldPageFilter) => {
  const prevFilter = prev || getDefaultTableAffected();
  if (initFilters) {
    const filterObj: AdvancedFilterMappingType<CustomField> = {
      ['name']: {
        filterOption: 'contains',
        value: {
          fromValue: initFilters.name,
        },
      },
    };
    const advancedFilterMapping = { ...prevFilter.advancedFilterMapping, ...filterObj };
    return { ...prevFilter, advancedFilterMapping: { ...advancedFilterMapping } };
  }
  return prevFilter;
};

const columns: ColumnType<CustomField>[] = [
  {
    name: 'name',
    title: 'Field Name',
    customRender: (_data, rowData) => {
      return (
        <AmtShortTextLink
          routePath={ROUTE_PATH.CONFIGURATION_CHANGE_CUSTOM_FIELD_DETAIL}
          entityId={rowData.id}
          data={rowData.name}
          disableShorten
          fw={500}
        />
      );
    },
  },
  {
    name: 'description',
    title: 'Description',
    customRender: (_data, rowData) => <AmtShortTextContent data={rowData.description || ''} />,
  },
  {
    name: 'type',
    title: 'Type',
    customRender: (_data, rowData) => {
      return CUSTOM_FIELD_TYPE_LABEL[rowData.type];
    },
  },
  {
    name: 'createdDate',
    title: 'Created Date',
    hidden: true,
    customRender: renderDateTime,
  },
  {
    name: 'createdBy',
    title: 'Created By',
    hidden: true,
  },
  {
    name: 'modifiedDate',
    title: 'Modified Date',
    hidden: true,
    customRender: renderDateTime,
  },
  {
    name: 'modifiedBy',
    title: 'Modified By',
    hidden: true,
  },
];
export const CustomFieldManagementPage = () => {
  const [totalRecords, setTotalRecords] = useState(0);

  const navigate = useNavigate();

  const [inputFilters, setInputFilters] = useState<CustomFieldPageFilter>({} as CustomFieldPageFilter);

  const [savedFilters, setSavedFilters] = useSavedFilterCycle({
    key: LocalStorageKey.SEARCH_VALUE_CUSTOM_FIELD_PAGE,
    deserialize: (str) => parseFilterWithDate<CustomFieldPageFilter>(str, dateRangeKeys),
    setInputFilters,
  });

  const [tableAffectedChange, setTableAffectedChange] = useState<TableAffactedSafeType>();

  const [columnOrders, setColumnOrders] = useState<string[]>();

  const savedColumns = useSavedColumns<CustomField>(LocalStorageKey.COLUMN_DISPLAY_CUSTOM_FIELD_PAGE, columns, columnOrders);

  const { data: customFieldsResponse, refetch: refetchList } = useFetch(
    CustomFieldApi.findAll(initOrUpdatedFilterPayloads(tableAffectedChange, savedFilters)),
    {
      enabled: !!tableAffectedChange,
    },
  );

  useEffect(() => {
    if (customFieldsResponse?.data?.content) {
      setTotalRecords(customFieldsResponse.data.totalElements);
    }
  }, [customFieldsResponse?.data]);

  const handleSearch = useCallback(
    (filters?: CustomFieldPageFilter) => {
      const appliedFilters = filters ?? inputFilters;
      setSavedFilters((prev) => ({ ...prev, ...appliedFilters }));
      setTableAffectedChange((prev) => {
        return initOrUpdatedFilterPayloads(prev, appliedFilters);
      });
    },
    [inputFilters, setSavedFilters],
  );

  const handleClearNameFilter = () => {
    const updatedFilters = { ...inputFilters, name: '' };
    handleSearch(updatedFilters);
    setInputFilters(updatedFilters);
  };

  const tableProps: KanbanTableProps<CustomField> = useMemo(() => {
    const tblProps: KanbanTableProps<CustomField> = {
      columns: savedColumns,
      data: customFieldsResponse?.data?.content ?? [],
      customRowProps: (data) => ({
        onDoubleClick: () => {
          navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_CHANGE_CUSTOM_FIELD_DETAIL, data.id, EntityAction.EDIT));
        },
      }),

      serverside: {
        totalRows: totalRecords,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffectedChange, dataSet)) {
            setTableAffectedChange(dataSet);
          }
        },
      },
      columnOrderable: {
        onOrder: (data) => {
          setColumnOrders(data.map((it) => it.name));
        },
      },
      actions: {
        customAction: (data) => {
          return <ActionColumn customField={data} refetch={refetchList} />;
        },
      },
      pagination: {
        enable: true,
      },
      showNumericalOrderColumn: true,
    };
    return tblProps;
  }, [savedColumns, customFieldsResponse?.data?.content, totalRecords, navigate, tableAffectedChange, refetchList]);

  return (
    <Box>
      <HeaderTitleComponent
        title=''
        rightSection={
          <Flex>
            <KanbanButton
              size='xs'
              leftSection={<IconPlus />}
              onClick={() => {
                navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_CHANGE_CUSTOM_FIELD_DETAIL, 0, EntityAction.CREATE));
              }}>
              Add Field
            </KanbanButton>
          </Flex>
        }
        leftSection={<KanbanTitle fz={'h4'}>Custom Fields</KanbanTitle>}
      />
      <Flex dir='row' gap='xs' align={'center'}>
        <FilterTextInput
          onClear={handleClearNameFilter}
          placeholder='Field Name'
          value={inputFilters.name ?? ''}
          onChange={(val) => {
            setInputFilters({ ...inputFilters, name: val.target.value });
          }}
          onBlur={() => {
            handleSearch(inputFilters);
          }}
        />
      </Flex>
      <KanbanTable {...tableProps} title='' />
    </Box>
  );
};

const ActionColumn = ({ customField, refetch }: { customField: CustomField; refetch: () => void }) => {
  const navigate = useNavigate();

  const { mutate: deleteMutate } = useMutate(CustomFieldApi.deleteById, {
    successNotification: 'Field deleted successfully',
    confirm: deleteConfirm(),
    onSuccess: () => refetch(),
  });

  return (
    <Flex gap='xs' align='center'>
      <Tooltip label='Edit'>
        <KanbanIconButton
          variant='transparent'
          size={'sm'}
          onClick={() => {
            navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_CHANGE_CUSTOM_FIELD_DETAIL, customField.id, EntityAction.EDIT));
          }}>
          <IconEdit />
        </KanbanIconButton>
      </Tooltip>

      <Tooltip label='Delete'>
        <KanbanIconButton
          variant='transparent'
          color='red'
          size={'sm'}
          onClick={() =>
            deleteMutate(customField.id, {
              confirm: deleteConfirm([customField.name]),
            })
          }>
          <IconTrash />
        </KanbanIconButton>
      </Tooltip>
    </Flex>
  );
};

export default CustomFieldManagementPage;
