import { useLocalStorage } from '@mantine/hooks';
import { StorageProperties } from '@mantine/hooks/lib/use-local-storage/create-storage';
import React, { useMemo } from 'react';
import { SetStateAction, useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import type { Location } from 'react-router-dom';

export type Props<T extends object> = StorageProperties<Partial<T>> & {
  setInputFilters: React.Dispatch<SetStateAction<T>>;
};

export function buildNavigateState(overrides: NavigationState & Record<string, unknown> = {}): NavigationState {
  return {
    ...overrides,
  };
}

export type NavigationState = {
  fromDetail?: boolean;
};

/**
 * <PERSON>le saved filter value when navigate from any fromDetail page and return  state and dispatch;
 * @param props localstorage props
 * @returns state and dispactch
 */
function useSavedFilterCycle<KeyDataType extends object>(props: Props<KeyDataType>) {
  const stateResolves = useLocalStorage<Partial<KeyDataType>>({
    ...props,
  });
  const [savedFilters, setSavedFilters] = stateResolves;
  const didHandleLocalStorage = useRef(false);
  const location = useLocation() as Location<NavigationState>;

  const shouldResetFilters = useMemo(() => {
    const isFromDetail = location.state?.fromDetail;
    if (!isFromDetail) {
      return true;
    }
    const navEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    return navEntry?.type === 'reload';
  }, [location.state?.fromDetail]);

  useEffect(() => {
    if (didHandleLocalStorage.current) {
      return;
    }

    if (shouldResetFilters) {
      localStorage.removeItem(props.key);
      const defaultFilter = {} as KeyDataType;
      props.setInputFilters(defaultFilter);
      setSavedFilters(defaultFilter);
    } else {
      const saved = localStorage.getItem(props.key);
      const parsed = saved ? JSON.parse(saved) : savedFilters;
      props.setInputFilters(parsed);
    }

    didHandleLocalStorage.current = true;
  }, [shouldResetFilters, props, savedFilters, setSavedFilters]);

  return stateResolves;
}

export default useSavedFilterCycle;
