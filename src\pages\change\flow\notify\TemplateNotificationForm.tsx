import { Box, Combobox, useCombobox, PillsInput, Pill, ComboboxItem, Stack } from '@mantine/core';
import { CustomContentComponent } from '@components/customContent/CustomContentComponent';
import { SelectWithPage } from '@components/SelectWithPage';
import useInfiniteFetch from '@core/hooks/useInfiniteFetch';
import useMutate from '@core/hooks/useMutate';
import { ChangeFlowNotificationModel } from '@models/ChangeFlowModel';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { NotificationsEmailTemplateApi } from '@api/NotificationsEmailTemplateApi';
import { getDefaultTableAffected, TableAffactedSafeType } from 'kanban-design-system';
import { EntityAction } from '@common/constants/EntityActionConstants';
import { INVALID_VALUE } from '@core/message/MesageConstant';
import { VARIABLE_PREFIX } from './useNotificationForm';
import * as EmailValidator from 'email-validator';

export interface TemplateNotificationFormProps {
  selectedTemplate: number | null;
  setSelectedTemplate: (val: number | null) => void;
  notifyItems: string[];
  setNotifyItems: React.Dispatch<React.SetStateAction<string[]>>;
  notifyError: string | null;
  setNotifyError: (err: string | null) => void;
  variableOptions?: ComboboxItem[];
  notification?: ChangeFlowNotificationModel;
  setMode: (mode: EntityAction) => void;
}

const builtInVariable: ComboboxItem = {
  value: '$ChangeCoordinatorEmail',
  label: 'Change Coordinator',
};

const MAX_NOTIFY_LENGTH = 1000;

export const TemplateNotificationForm: React.FC<TemplateNotificationFormProps> = ({
  notification,
  notifyError,
  notifyItems,
  selectedTemplate,
  setMode,
  setNotifyError,
  setNotifyItems,
  setSelectedTemplate,
  variableOptions = [],
}) => {
  const [notifyInput, setNotifyInput] = useState('');
  const combobox = useCombobox();
  const skipNextSubmit = useRef(false);

  const [searchParams, setSearchParams] = useState<TableAffactedSafeType>({
    ...getDefaultTableAffected(),
    sortedBy: 'name',
  });

  const { fetchNextPage: fetchNextPageApplication, flatData: applicationData } = useInfiniteFetch(
    NotificationsEmailTemplateApi.findAll({ ...searchParams }),
    { showLoading: false },
  );

  const { data, mutate, reset } = useMutate(NotificationsEmailTemplateApi.findById, {
    successNotification: { enable: false },
    onError: () => {
      setSelectedTemplate(null);
      setMode(EntityAction.CREATE);
    },
  });

  const mergedOptions: ComboboxItem[] = useMemo(() => [builtInVariable, ...variableOptions], [variableOptions]);

  useEffect(() => {
    if (selectedTemplate && selectedTemplate > 0) {
      mutate(selectedTemplate);
    } else if (selectedTemplate === null) {
      reset();
    }
  }, [mutate, reset, selectedTemplate]);

  useEffect(() => {
    if (notification) {
      const validNotifyItems = [...(notification.notifyNode ?? [])].filter((item) => mergedOptions.some((opt) => opt.value === item));
      const newNotifyItems = [...validNotifyItems, ...(notification.notifyTo ?? [])];

      setNotifyItems((prev) => {
        if (prev.length !== newNotifyItems.length || !prev.every((item, index) => item === newNotifyItems[index])) {
          return newNotifyItems;
        }
        return prev;
      });
    }
  }, [notification, mergedOptions, setNotifyItems]);

  useEffect(() => {
    const hasInvalid = notifyItems.some((item) => {
      const isVariable = mergedOptions.some((opt) => opt.value === item);
      return !isVariable && !EmailValidator.validate(item);
    });

    if (hasInvalid) {
      setNotifyError(INVALID_VALUE);
    } else {
      setNotifyError(null);
    }
  }, [notifyItems, mergedOptions, setNotifyError]);

  const handleNotifySubmit = (val: string) => {
    const trimmed = val.trim();
    if (!trimmed || trimmed === VARIABLE_PREFIX) {
      return;
    }

    const newTotalLength = notifyItems.reduce((acc, item) => acc + item.length, 0) + trimmed.length;
    if (newTotalLength > MAX_NOTIFY_LENGTH) {
      return;
    }

    setNotifyItems([...notifyItems, trimmed]);
    setNotifyInput('');
    setNotifyError(null);
    combobox.closeDropdown();
  };

  const handleNotifyKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (['Enter', 'Tab'].includes(e.key)) {
      e.preventDefault();
      if (skipNextSubmit.current) {
        skipNextSubmit.current = false;
        return;
      }
      handleNotifySubmit(notifyInput);
    }

    if (e.key === 'Backspace' && notifyInput === '' && notifyItems.length > 0) {
      setNotifyItems((prev) => prev.slice(0, -1));
    }
  };

  useEffect(() => {
    const lastToken =
      notifyInput
        .trim()
        .split(/[\s,;]/)
        .pop() ?? '';
    if (lastToken === VARIABLE_PREFIX && notifyInput.endsWith(VARIABLE_PREFIX)) {
      combobox.openDropdown();
    } else {
      combobox.closeDropdown();
    }
  }, [combobox, notifyInput]);

  const handleSelectVariable = (val: string) => {
    handleNotifySubmit(val);
    skipNextSubmit.current = true;
  };

  const handleRemove = (indexToRemove: number) => {
    setNotifyItems((prev) => prev.filter((_, index) => index !== indexToRemove));
  };

  const selectedTemplateOption = useMemo(() => {
    if (!selectedTemplate || selectedTemplate <= 0) {
      return undefined;
    }
    const found = applicationData.find((i) => i.id === selectedTemplate);
    return found ? { value: String(found.id), label: found.name } : undefined;
  }, [selectedTemplate, applicationData]);

  return (
    <Stack>
      <SelectWithPage
        options={applicationData.map((item) => ({
          value: String(item.id),
          label: item.name,
        }))}
        onChange={(val) => {
          const templateId = val ? parseInt(val, 10) : null;

          if (!templateId) {
            setNotifyItems([]);
            setNotifyError(null);
            reset();
          }
          setSelectedTemplate(templateId);
        }}
        clearable
        label='Email Template'
        handleScrollToBottom={fetchNextPageApplication}
        onSearch={(val) => setSearchParams((prev) => ({ ...(prev ?? {}), name: val }))}
        value={selectedTemplateOption}
      />

      <CustomContentComponent
        key={`subject-${selectedTemplate || 'empty'}`}
        disabled
        label='Subject'
        value={selectedTemplate && data?.data?.subject ? JSON.stringify(data.data.subject) : ''}
        docsToolbar={false}
        contentMaxLength={0}
      />

      <CustomContentComponent
        key={`content-${selectedTemplate || 'empty'}`}
        disabled
        label='Content'
        value={selectedTemplate && data?.data?.content ? JSON.stringify(data.data.content) : ''}
        docsToolbar={false}
        contentMaxLength={0}
      />

      {selectedTemplate && data?.data?.id && (
        <Box>
          <Combobox store={combobox} onOptionSubmit={handleSelectVariable} withinPortal>
            <Combobox.Target>
              <PillsInput withAsterisk required label='Notify To' error={notifyError}>
                {notifyItems.map((val, index) => {
                  const matched = mergedOptions.find((opt) => opt.value === val);
                  const label = matched ? `${VARIABLE_PREFIX}${matched.label}` : val;

                  return (
                    <Pill withRemoveButton key={`${val}-${index}`} onRemove={() => handleRemove(index)}>
                      {label}
                    </Pill>
                  );
                })}

                <PillsInput.Field
                  w='40%'
                  value={notifyInput}
                  onChange={(e) => setNotifyInput(e.currentTarget.value)}
                  onKeyDown={handleNotifyKeyDown}
                  placeholder='Enter email or $variable'
                />
              </PillsInput>
            </Combobox.Target>

            <Combobox.Dropdown>
              <Combobox.Options>
                {mergedOptions.map((opt) => (
                  <Combobox.Option key={opt.value} value={opt.value}>
                    {opt.label}
                  </Combobox.Option>
                ))}
              </Combobox.Options>
            </Combobox.Dropdown>
          </Combobox>
        </Box>
      )}
    </Stack>
  );
};
