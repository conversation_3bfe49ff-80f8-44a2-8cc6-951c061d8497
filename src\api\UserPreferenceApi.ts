import { RequestConfig } from '@core/api';
import { createResponseSchema, ResponseData } from '@core/schema/Common';
import { BaseURL } from '@common/constants/BaseUrl';
import { UserPreference, UserPreferenceListSchema, UserPreferenceSchema } from '@core/schema/UserPreference';
import { createRequest } from '@api/Utils';

export class UserPreferenceApi {
  static getUserPreference(): RequestConfig<ResponseData<UserPreference[]>> {
    return {
      url: `${BaseURL.userPreference}`,
      method: 'GET',
      schema: createResponseSchema(UserPreferenceListSchema),
    };
  }

  static upsert(data: UserPreference): RequestConfig<ResponseData<UserPreference>> {
    return createRequest({
      url: `${BaseURL.userPreference}`,
      method: 'POST',
      schema: createResponseSchema(UserPreferenceSchema),
      data,
    });
  }
}
