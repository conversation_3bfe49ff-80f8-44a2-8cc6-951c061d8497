import { configureStore } from '@reduxjs/toolkit';
import { currentUserSlice } from '@slices/CurrentUserSlice';
import { TypedUseSelectorHook, useSelector } from 'react-redux';
import { useDispatch } from 'react-redux';
import createSagaMiddleware from 'redux-saga';
import { countersSlice } from 'store/slices/CountersSlice';
import { pageLoadingSlice } from 'store/slices/PageLoadingSlice';
import { currentUserPreferenceSlice } from '@slices/UserPreferenceSlice';
import { breadcrumbSlice } from 'store/slices/BreadcrumbSlice';

const sagaMiddleware = createSagaMiddleware();

export const store = configureStore({
  reducer: {
    counters: countersSlice.reducer,
    currentUser: currentUserSlice.reducer,
    currentUserPreference: currentUserPreferenceSlice.reducer,
    pageLoading: pageLoadingSlice.reducer,
    breadcrumbEntityName: breadcrumbSlice.reducer,
  },
  middleware: (getDefaultMiddleware) => getDefaultMiddleware().concat(sagaMiddleware),
});
//sagaMiddleware.run(rootSaga);

export type RootStoreType = ReturnType<typeof store.getState>;

// Inferred type: {posts: PostsState, comments: CommentsState, users: UsersState}
export type AppDispatch = typeof store.dispatch;

// Use throughout your app instead of plain `useDispatch` and `useSelector`
// Sử dụng 2 hook này để get state và dispatch actions.
export const useAppDispatch: () => AppDispatch = useDispatch;
export const useAppSelector: TypedUseSelectorHook<RootStoreType> = useSelector;

export const getDirectState = store.getState;
export const directDispath = store.dispatch;
export const runMiddleWare = (saga: any) => {
  sagaMiddleware.run(saga);
};
