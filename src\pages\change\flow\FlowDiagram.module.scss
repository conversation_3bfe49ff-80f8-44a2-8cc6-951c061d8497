/* ReactFlowFix.module.scss */
.flowCustomEdge {

  /* Trong FlowDiagram.module.scss */
  :global(.react-flow__edges) {
    overflow: visible !important;
    /* <PERSON><PERSON><PERSON> bảo overflow là visible */
    position: absolute !important;
    top: 0;
    left: 0;
    width: 100vw !important;
    /* <PERSON><PERSON><PERSON> bảo SVG rộng hơn */
    height: 95vh !important;
    /* Đ<PERSON>m bảo SVG cao hơn */
  }

  :global(.react-flow__edges svg) {
    overflow: visible !important;
    width: 95vw !important;
    /* <PERSON><PERSON><PERSON> bảo SVG rộng hơn */
    height: 95vh !important;
    /* <PERSON><PERSON><PERSON> bảo SVG cao hơn */
  }


}