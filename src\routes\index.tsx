import React from 'react';
import type { RouterType } from '@components/appShell';
import DashboardPage from '@pages/dashboard';

import { type RouteProps, useLocation, useParams } from 'react-router-dom';

export const navbarConfigs: RouterType[] = [];
import { ROUTE_PATH } from '@common/utils/RouterUtils';
import { UserManagementPage } from '@pages/configuration/user/users';
import GroupsPage from '@pages/group';
import { GroupDetailPage } from '@pages/group/GroupDetailPage';
import RolesPage from '@pages/configuration/user/roles';
import RoleDetailPage from '../pages/configuration/user/roles/RoleDetailPage';
import { CustomFieldManagementPage } from '@pages/change/customField';
import CustomFieldDetailtPage from '@pages/change/customField/CustomFieldDetailPage';
import GuardRoute from '@components/GuardRoute';
import { ACL_PERMISSIONS } from '@common/constants/AclPermissionConstants';
import { UserDetailPage } from '@pages/configuration/user/users/UserDetailPage';
import ChangeTemplatesPage from '@pages/change/template';
import ChangeTemplateDetailPage from '@pages/change/template/ChangeTemplateDetailPage';
import ChangeStatusManagementPage from '@pages/change/status';
import ChangeStatusDetailtPage from '@pages/change/status/ChangeStatusDetailPage';
import DocumentDetailPage from '@pages/change/document/DocumentDetailPage';
import WorkFlowNodePage from '@pages/change/workflowNodes/WorkFlowNodePage';
import WorkFlowNodeDetailPage from '@pages/change/workflowNodes/WorkFlowNodeDetailPage';
import ChangeRequestPage from '@pages/change/changeList';
import TemplateEmailsPage from 'pages/configuration/notifications/emailTemplate';
import EmailTemplateDetailPage from '@pages/configuration/notifications/emailTemplate/EmailTemplateDetailPage';
import ChangeRequestDetailPage from '@pages/change/request/ChangeRequestDetailPage';
import ChangeFlowManagementPage from '@pages/change/flow';
import ChangeFlowDetailtPage from '@pages/change/flow/ChangeFlowDetailPage';
import WorkflowDetailPage from '@pages/change/request/workflow/WorkflowDetailPage';
import { EntityAction } from '@common/constants/EntityActionConstants';
import DocumentManagementPage from '@pages/change/document';

//ft/role router config authorize
export const headerLinkConfigs: RouterType[] = [];
export const getHeaderLink = (): RouterType[] => {
  return headerLinkConfigs;
};

type RoutePropsOmit = Omit<RouteProps, 'children'>;
export type RoutePropsType = RoutePropsOmit & {
  children?: RoutePropsType[];
};

const ChangeRequestDetailGuard: React.FC = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const action = queryParams.get('action');
  const { id } = useParams();
  const changeRequestId = Number(id);

  let permission = ACL_PERMISSIONS.CHANGE_VIEW;

  if (action === EntityAction.CREATE && changeRequestId === 0) {
    permission = ACL_PERMISSIONS.CHANGE_ADD;
  }
  if (action === EntityAction.EDIT && changeRequestId > 0) {
    permission = ACL_PERMISSIONS.CHANGE_UPDATE;
  }

  return (
    <GuardRoute requirePermissions={[permission]}>
      <ChangeRequestDetailPage />
    </GuardRoute>
  );
};

export const routeConfigs: RoutePropsType[] = [
  {
    path: '/dashboard',
    element: <DashboardPage />,
  },
  {
    path: ROUTE_PATH.CONFIGURATION_USER_USERS,
    element: (
      <GuardRoute requirePermissions={[ACL_PERMISSIONS.ADMIN_MANAGE_SYSTEM]}>
        <UserManagementPage />
      </GuardRoute>
    ),
  },

  {
    path: ROUTE_PATH.CONFIGURATION_USER_USERS_DETAIL,
    element: (
      <GuardRoute requirePermissions={[ACL_PERMISSIONS.ADMIN_MANAGE_SYSTEM]}>
        <UserDetailPage />
      </GuardRoute>
    ),
  },
  {
    path: ROUTE_PATH.CONFIGURATION_USER_GROUPS,
    element: (
      <GuardRoute requirePermissions={[ACL_PERMISSIONS.ADMIN_MANAGE_SYSTEM]}>
        <GroupsPage />
      </GuardRoute>
    ),
  },
  {
    path: ROUTE_PATH.CONFIGURATION_USER_GROUP_DETAIL,
    element: (
      <GuardRoute requirePermissions={[ACL_PERMISSIONS.ADMIN_MANAGE_SYSTEM]}>
        <GroupDetailPage />
      </GuardRoute>
    ),
  },
  {
    path: ROUTE_PATH.CONFIGURATION_USER_ROLES,
    element: (
      <GuardRoute requirePermissions={[ACL_PERMISSIONS.ADMIN_MANAGE_SYSTEM]}>
        <RolesPage />
      </GuardRoute>
    ),
  },
  {
    path: ROUTE_PATH.CONFIGURATION_USER_ROLE_DETAIL,
    element: (
      <GuardRoute requirePermissions={[ACL_PERMISSIONS.ADMIN_MANAGE_SYSTEM]}>
        <RoleDetailPage />
      </GuardRoute>
    ),
  },
  {
    path: ROUTE_PATH.CONFIGURATION_CHANGE_CUSTOM_FIELD,
    element: (
      <GuardRoute requirePermissions={[ACL_PERMISSIONS.ADMIN_MANAGE_SYSTEM]}>
        <CustomFieldManagementPage />
      </GuardRoute>
    ),
  },

  {
    path: ROUTE_PATH.CONFIGURATION_CHANGE_CUSTOM_FIELD_DETAIL,
    element: (
      <GuardRoute requirePermissions={[ACL_PERMISSIONS.ADMIN_MANAGE_SYSTEM]}>
        <CustomFieldDetailtPage />
      </GuardRoute>
    ),
  },

  {
    path: ROUTE_PATH.CONFIGURATION_CHANGE_CHANGE_TEMPLATE,
    element: (
      <GuardRoute requirePermissions={[ACL_PERMISSIONS.ADMIN_MANAGE_SYSTEM]}>
        <ChangeTemplatesPage />
      </GuardRoute>
    ),
  },
  {
    path: ROUTE_PATH.CONFIGURATION_CHANGE_CHANGE_TEMPLATE_DETAIL,
    element: (
      <GuardRoute requirePermissions={[ACL_PERMISSIONS.ADMIN_MANAGE_SYSTEM]}>
        <ChangeTemplateDetailPage />
      </GuardRoute>
    ),
  },

  {
    path: ROUTE_PATH.CONFIGURATION_CHANGE_STATUS,
    element: (
      <GuardRoute requirePermissions={[ACL_PERMISSIONS.ADMIN_MANAGE_SYSTEM]}>
        <ChangeStatusManagementPage />
      </GuardRoute>
    ),
  },

  {
    path: ROUTE_PATH.CONFIGURATION_CHANGE_STATUS_DETAIL,
    element: (
      <GuardRoute requirePermissions={[ACL_PERMISSIONS.ADMIN_MANAGE_SYSTEM]}>
        <ChangeStatusDetailtPage />
      </GuardRoute>
    ),
  },

  {
    path: ROUTE_PATH.CONFIGURATION_CHANGE_DOCUMENT,
    element: (
      <GuardRoute requirePermissions={[ACL_PERMISSIONS.ADMIN_MANAGE_SYSTEM]}>
        <DocumentManagementPage />
      </GuardRoute>
    ),
  },

  {
    path: ROUTE_PATH.CONFIGURATION_CHANGE_DOCUMENT_DETAIL,
    element: (
      <GuardRoute requirePermissions={[ACL_PERMISSIONS.ADMIN_MANAGE_SYSTEM]}>
        <DocumentDetailPage />
      </GuardRoute>
    ),
  },

  {
    path: ROUTE_PATH.CONFIGURATION_CHANGE_WORKFLOW_NODE,
    element: (
      <GuardRoute requirePermissions={[ACL_PERMISSIONS.ADMIN_MANAGE_SYSTEM]}>
        <WorkFlowNodePage />
      </GuardRoute>
    ),
  },

  {
    path: ROUTE_PATH.CONFIGURATION_CHANGE_WORKFLOW_NODE_DETAIL,
    element: (
      <GuardRoute requirePermissions={[ACL_PERMISSIONS.ADMIN_MANAGE_SYSTEM]}>
        <WorkFlowNodeDetailPage />
      </GuardRoute>
    ),
  },
  {
    path: ROUTE_PATH.CHANGE_REQUEST,
    element: (
      <GuardRoute requirePermissions={[ACL_PERMISSIONS.CHANGE_VIEW]}>
        <ChangeRequestPage />
      </GuardRoute>
    ),
  },
  {
    path: ROUTE_PATH.CHANGE_REQUEST_DETAIL,
    element: <ChangeRequestDetailGuard />,
  },
  {
    path: ROUTE_PATH.CONFIGURATION_CHANGE_FLOW,
    element: (
      <GuardRoute requirePermissions={[ACL_PERMISSIONS.ADMIN_MANAGE_SYSTEM]}>
        <ChangeFlowManagementPage />
      </GuardRoute>
    ),
  },
  {
    path: ROUTE_PATH.CONFIGURATION_CHANGE_FLOW_DETAIL,
    element: (
      <GuardRoute requirePermissions={[ACL_PERMISSIONS.ADMIN_MANAGE_SYSTEM]}>
        <ChangeFlowDetailtPage />
      </GuardRoute>
    ),
  },
  {
    path: ROUTE_PATH.CHANGE_REQUEST_WORKFLOW_DETAIL,
    element: (
      <GuardRoute requirePermissions={[ACL_PERMISSIONS.CHANGE_VIEW]}>
        <WorkflowDetailPage />
      </GuardRoute>
    ),
  },
  {
    path: ROUTE_PATH.CONFIGURATION_NOTIFICATIONS_EMAIL_TEMPLATES,
    element: (
      <GuardRoute requirePermissions={[ACL_PERMISSIONS.ADMIN_MANAGE_SYSTEM]}>
        <TemplateEmailsPage />
      </GuardRoute>
    ),
  },
  {
    path: ROUTE_PATH.CONFIGURATION_NOTIFICATIONS_EMAIL_TEMPLATE_DETAIL,
    element: (
      <GuardRoute requirePermissions={[ACL_PERMISSIONS.ADMIN_MANAGE_SYSTEM]}>
        <EmailTemplateDetailPage />
      </GuardRoute>
    ),
  },
];
