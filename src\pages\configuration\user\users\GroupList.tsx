import React, { useEffect, useMemo, useState } from 'react';
import { getDefaultTableAffected, KanbanButton, KanbanIconButton, KanbanText } from 'kanban-design-system';
import { Box, Flex, Pill } from '@mantine/core';
import useFetch from '@core/hooks/useFetch';
import { GroupUser } from '@core/schema/Group';
import { useDisclosure } from '@mantine/hooks';
import { RoleGroupListWithPagination } from './GroupRoleList';
import { IconReload } from '@tabler/icons-react';
import { UserModel } from '@models/UserModel';
import { UseFormReturn } from 'react-hook-form';
import { ItemCheck, DEFAULT_ITEM_CHECK } from '@core/schema/Common';
import { LOAD_ITEM_MAX_LENGTH } from '@common/constants/ValidationConstant';
import GroupUserTableComponent from './component/GroupTableComponent';
import { IconPlus } from '@tabler/icons-react';
import { UserApi } from '@api/UserApi';
import AmtModal from '@components/AmtModal';

interface GroupListWithPaginationProps {
  username: string;
  form: UseFormReturn<UserModel>;
}

export const GroupListWithPagination: React.FC<GroupListWithPaginationProps> = ({ form, username }) => {
  const [groups, setGroups] = useState<GroupUser[]>([]);
  const [page, setPage] = useState(1);

  const [selectedGroups, setSelectedGroups] = useState<ItemCheck<GroupUser>>(DEFAULT_ITEM_CHECK);
  const [toInsertGroups, setToInsertGroups] = useState<ItemCheck<GroupUser>>(DEFAULT_ITEM_CHECK);
  const [toDeleteGroups, setToDeleteGroups] = useState<ItemCheck<GroupUser>>(DEFAULT_ITEM_CHECK);

  const [tempToInsertGroups, setTempToInsertGroups] = useState<ItemCheck<GroupUser>>({});
  const [tempToDeleteGroups, setTempToDeleteGroups] = useState<ItemCheck<GroupUser>>({});

  const [openedGroups, { close: closeGroups, open: openGroups }] = useDisclosure(false);

  const sortedInserts = useMemo(() => {
    return Object.values(toInsertGroups).sort((a, b) => a.groupName.toLowerCase().localeCompare(b.groupName.toLowerCase()));
  }, [toInsertGroups]);

  const { data, isLoading } = useFetch(
    UserApi.findAllSelectedGroupByUserName(username, {
      ...getDefaultTableAffected(),
      page: page,
      rowsPerPage: LOAD_ITEM_MAX_LENGTH,
    }),
    { enabled: !!username },
  );

  useEffect(() => {
    if (data?.data?.content) {
      if (page === 1) {
        setGroups(data.data.content);
      } else {
        setGroups((prev) => [...prev, ...(data?.data?.content || [])]);
      }
    }
  }, [data, page]);

  const loadMore = () => {
    setPage((prev) => prev + 1);
  };

  useEffect(() => {
    const formattedGroups = Object.values(toInsertGroups);
    form.setValue('groupsToInsert', formattedGroups);
  }, [toInsertGroups, form]);

  useEffect(() => {
    const formattedGroups = Object.values(toDeleteGroups);
    form.setValue('groupsToDelete', formattedGroups);
  }, [toDeleteGroups, form]);

  useEffect(() => {
    setTempToDeleteGroups(toDeleteGroups);
  }, [toDeleteGroups]);

  const handleCancelChangeGroups = () => {
    setTempToDeleteGroups(toDeleteGroups);
    setTempToInsertGroups(toInsertGroups);
    closeGroups();
  };

  const handleSaveChangeGroups = () => {
    setToInsertGroups(tempToInsertGroups);
    setToDeleteGroups(tempToDeleteGroups);
    closeGroups();
  };

  return (
    <>
      <Box>
        <Flex direction={'row'} align={'center'} gap={'xs'}>
          <KanbanText fw={'500'}>Group List</KanbanText>
          <KanbanIconButton size='sm' variant='filled' onClick={openGroups}>
            <IconPlus />
          </KanbanIconButton>
          <AmtModal
            title=''
            size={'80%'}
            opened={openedGroups}
            actions={
              <KanbanButton variant='filled' onClick={handleSaveChangeGroups}>
                Save
              </KanbanButton>
            }
            onClose={handleCancelChangeGroups}>
            <GroupUserTableComponent
              username={username || ''}
              selecteds={selectedGroups}
              setSelecteds={setSelectedGroups}
              setToInserts={setTempToInsertGroups}
              setToDeletes={setTempToDeleteGroups}
              toDeletes={tempToDeleteGroups}
              toInserts={tempToInsertGroups}
            />
          </AmtModal>
        </Flex>
        <Flex wrap={'wrap'} gap={'xs'} mt='xs'>
          {sortedInserts.map((item) => (
            <Pill
              onRemove={() =>
                setToInsertGroups((prev) => {
                  const newGroups = { ...prev };
                  delete newGroups[item.groupId];
                  return newGroups;
                })
              }
              withRemoveButton
              size='md'
              bg='var(--mantine-color-green-1)'
              bd='1px solid var(--mantine-color-green-1)'
              key={item.id}>
              {item.groupName}
            </Pill>
          ))}

          {groups.map(
            (group) =>
              !toDeleteGroups[group.groupId] && (
                <Pill
                  onRemove={() =>
                    setToDeleteGroups((prev) => {
                      const newGroups = { ...prev };
                      newGroups[group.groupId] = group;
                      return newGroups;
                    })
                  }
                  withRemoveButton
                  size='md'
                  bg='white'
                  bd='1px solid var(--mantine-color-primary-2)'
                  key={group.groupId}>
                  {group.groupName}
                </Pill>
              ),
          )}

          {data?.data && !data.data.last && (
            <KanbanButton size='xs' variant='light' onClick={loadMore} disabled={isLoading}>
              <IconReload size={16} style={{ marginRight: 5 }} />
            </KanbanButton>
          )}
        </Flex>
        <RoleGroupListWithPagination username={username} toInsert={toInsertGroups} toDelete={toDeleteGroups} />
      </Box>
    </>
  );
};
