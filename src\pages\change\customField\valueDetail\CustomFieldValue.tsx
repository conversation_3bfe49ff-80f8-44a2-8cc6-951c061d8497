import React from 'react';
import { Flex } from '@mantine/core';
import { Controller, UseFormReturn } from 'react-hook-form';
import { CustomFieldModel } from '@models/CustomField';
import { KanbanInput, KanbanNumberInput, KanbanTextarea } from 'kanban-design-system';
import { CustomFieldType, CustomFieldTypeEnum } from '@common/constants/CustomFieldType';

const MAX_VALUE_SINGLE_LINE_LENGTH = 1000;
const MAX_VALUE_MULTI_LINE_LENGTH = 4000;

type Props = {
  type: CustomFieldType;
  form: UseFormReturn<CustomFieldModel>;
};
export const CustomFieldValue: React.FC<Props> = ({ form, type }) => {
  const { control, trigger, watch } = form;

  const renderNumberInput = (name: string, placeholder: string, maxLength: number, isTypeNumber: boolean, label?: string, max?: number) => (
    <Controller
      key={`${type}-${name}`}
      name={name as any}
      control={control}
      render={({ field, fieldState }) => (
        <KanbanNumberInput
          label={label}
          allowNegative={!!isTypeNumber}
          allowDecimal={!!isTypeNumber}
          maxLength={maxLength}
          max={max}
          placeholder={placeholder}
          style={{ flex: 1 }}
          value={field.value ?? undefined}
          onChange={(value) => {
            field.onChange(value ?? undefined);
            trigger(['fieldConstraint.min', 'fieldConstraint.max']);
          }}
          error={fieldState.error?.message}
        />
      )}
    />
  );

  const renderTextField = () => (
    <Controller
      key={type}
      name='fieldConstraint.defaultValue'
      control={control}
      render={({ field, fieldState }) =>
        type === CustomFieldTypeEnum.Enum.SINGLE_LINE ? (
          <KanbanInput
            minLength={watch('fieldConstraint.min') || undefined}
            maxLength={watch('fieldConstraint.max') || MAX_VALUE_SINGLE_LINE_LENGTH}
            label='Default Value'
            {...field}
            value={field.value || ''}
            error={fieldState.error?.message}
          />
        ) : (
          <KanbanTextarea
            {...field}
            minLength={watch('fieldConstraint.min') || undefined}
            maxLength={watch('fieldConstraint.max') || MAX_VALUE_MULTI_LINE_LENGTH}
            label='Default Value'
            autosize
            value={field.value ?? ''}
            maxRows={20}
            minRows={3}
            error={fieldState.error?.message}
          />
        )
      }
    />
  );

  return (
    <>
      {(type === CustomFieldTypeEnum.Enum.SINGLE_LINE || type === CustomFieldTypeEnum.Enum.MULTI_LINE) && (
        <>
          {renderTextField()}
          <Flex gap='md' align='flex-end' mt='sm'>
            {renderNumberInput(
              'fieldConstraint.min',
              'Minimum',
              4,
              false,
              'Length',
              type === CustomFieldTypeEnum.Enum.SINGLE_LINE ? MAX_VALUE_SINGLE_LINE_LENGTH : MAX_VALUE_MULTI_LINE_LENGTH,
            )}
            {renderNumberInput(
              'fieldConstraint.max',
              'Maximum',
              4,
              false,
              undefined,
              type === CustomFieldTypeEnum.Enum.SINGLE_LINE ? MAX_VALUE_SINGLE_LINE_LENGTH : MAX_VALUE_MULTI_LINE_LENGTH,
            )}
          </Flex>
        </>
      )}

      {CustomFieldTypeEnum.Enum.NUMBER === type && (
        <>
          {/* Set maxLength to 10 to limit input size to fit within the range of an int, ensuring valid and manageable values.*/}
          {renderNumberInput('fieldConstraint.defaultValue', 'Number', 10, true, 'Default Value')}
          <Flex gap='md' align='flex-end' mt='sm'>
            {renderNumberInput('fieldConstraint.min', 'Minimum', 10, true, 'Range')}
            {renderNumberInput('fieldConstraint.max', 'Maximum', 10, true)}
          </Flex>
        </>
      )}
    </>
  );
};
