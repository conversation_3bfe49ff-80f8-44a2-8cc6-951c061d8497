.box {
  min-width: 200px;
  position: relative;
  background-color: var(--mantine-color-white);
  border: 2px solid var(--mantine-color-dark-9);
  border-radius: var(--mantine-radius-md);
}

.box-header {
  text-align: center;
  font-weight: 700;
  border-radius: var(--mantine-radius-md) var(--mantine-radius-md) 0 0;
  border-bottom: 1px solid var(--mantine-color-gray-3);
}

.labelBox {
  @extend .box-header;
  background-color: var(--mantine-color-blue-0);
  padding: var(--mantine-spacing-xs) var(--mantine-spacing-md);
}

.nameBox {
  @extend .box-header;
  padding: var(--mantine-spacing-sm);
}

.cab {
  background-color: var(--mantine-color-violet-0);
}

.approval {
  background-color: var(--mantine-color-orange-0);
}

.handle {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: var(--mantine-spacing-sm);
  height: var(--mantine-spacing-sm);
  border: 2px solid white;
  border-radius: 50%;
  z-index: 10;

  &.left {
    left: calc(-1 * var(--mantine-spacing-xs));
    background-color: var(--mantine-color-blue-filled);

  }

  &.right {
    right: calc(-1 * var(--mantine-spacing-xs));
    background-color: var(--mantine-color-green-filled);

    &.reject {
      background-color: var(--mantine-color-red-filled);
    }
  }

}

.handleTop {
  position: absolute;
  top: calc(-1 * var(--mantine-spacing-xs));
  left: 50%;
  transform: translateX(-50%);
  width: var(--mantine-spacing-sm);
  height: var(--mantine-spacing-sm);
  background-color: var(--mantine-color-violet-filled);
  border: 2px solid white;
  border-radius: 50%;
}

.header,
.body {
  position: relative;
  padding: var(--mantine-spacing-sm) var(--mantine-spacing-md);
  margin: var(--mantine-spacing-xs) 0;
  border: 1px solid var(--mantine-color-gray-3);
  border-radius: var(--mantine-radius-sm);
  display: flex;
}

.mailBox {
  position: absolute;
  right: 5px;
  top: 50%;
}