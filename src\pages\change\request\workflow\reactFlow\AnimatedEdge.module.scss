.edgePath {
  stroke-width: 2.5;
  stroke-linecap: round;
  stroke-linejoin: round;
  opacity: 0.8;
  transition: opacity 0.1s ease-out, stroke-width 0.1s ease-out;
  cursor: pointer;
  will-change: auto;

  &:hover {
    opacity: 1;
    stroke-width: 3.5;
  }
}

.successEdge {
  stroke: #22c55e;
}

.failEdge {
  stroke: #ef4444;
}

.controlsGroup {
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
  pointer-events: all;
}

.controlsBackground {
  fill: rgba(255, 255, 255, 0.95);
  stroke: #e5e7eb;
  stroke-width: 1;
  filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.15));
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

.controlButton {
  cursor: pointer;
  pointer-events: all;

  rect {
    transition: fill 0.15s ease, stroke 0.15s ease;

    &:hover {
      filter: brightness(1.1);
    }

    &:active {
      filter: brightness(0.9);
    }
  }

  text {
    transition: fill 0.15s ease;
    pointer-events: none;
  }
}

.buttonBackground {
  fill: #f8f9fa;
  stroke: #e5e7eb;
  stroke-width: 1.5;
}

.successButtonBackground {
  fill: #22c55e;
  stroke: #16a34a;

  &:hover {
    fill: #16a34a;
  }

  &.activeButton {
    fill: #15803d;
    stroke: #14532d;
    filter: brightness(0.9);
  }
}

.failButtonBackground {
  fill: #ef4444;
  stroke: #dc2626;

  &:hover {
    fill: #dc2626;
  }

  &.activeButton {
    fill: #b91c1c;
    stroke: #7f1d1d;
    filter: brightness(0.9);
  }
}

.buttonText {
  fill: white;
  font-size: 9px;
  font-weight: 600;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  text-rendering: optimizeSpeed;
  dominant-baseline: middle;
}

// Disable transitions when dragging for better performance
.dragging {
  .edgePath {
    transition: none !important;
  }

  .controlsGroup {
    animation: none !important;
  }
}

// Hide controls when dragging
.react-flow__pane.dragging~* {
  .controlsGroup {
    display: none;
  }
}