import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import { Controller, useFieldArray, useFormContext } from 'react-hook-form';
import { ChangeRequestRoleList } from '@core/schema/ChangeRequestRole';
import { KanbanAccordion, KanbanTabs, KanbanTabsType, KanbanText } from 'kanban-design-system';
import { EntityAction } from '@common/constants/EntityActionConstants';
import { ChangeRequestApprovalMode } from '@common/constants/ChangeRequestApprovalConstants';
import { ChangeFlowNodeTypeEnum } from '@models/ChangeFlowModel';
import ChangeRequestApprovalComponent from './approval/ChangeRequestApprovalComponent';
import { useSelector } from 'react-redux';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import { MappedUser } from '@pages/change/request/role/ChangeRequestDetailRole';

interface NodeCabWorkflowTabsProps {
  mode: EntityAction;
  modeApproval: ChangeRequestApprovalMode;
  refetchChangeRequestRoles: () => void;
}

const CurrentUserCabTasks: React.FC<NodeCabWorkflowTabsProps> = ({ mode, modeApproval, refetchChangeRequestRoles }) => {
  const { control } = useFormContext<ChangeRequestRoleList>();
  const { fields: roles } = useFieldArray({
    control,
    name: `roles`,
  });
  const { cabRole, roleIdx } = useMemo(() => {
    const idx = roles.findIndex((role) => ChangeFlowNodeTypeEnum.Enum.CAB === role.changeFlowNode?.type);
    return {
      roleIdx: idx,
      cabRole: idx !== -1 ? roles[idx] : undefined,
    };
  }, [roles]);

  const { fields: workflows } = useFieldArray({
    control,
    name: `roles.${roleIdx}.workflows`,
  });
  const curentUser = useSelector(getCurrentUser).userInfo?.userName;

  const [activeWorkFlowIdx, setActiveWorkFlowIdx] = useState<string>('0');
  const [selectedUsers, setSelectedUsers] = useState<MappedUser[]>([]);
  const handleCheckboxChange = useCallback(
    (user: MappedUser, checked: boolean) => {
      const updateSelected = checked ? [...selectedUsers, user] : selectedUsers.filter((u) => u.username !== user.username);
      setSelectedUsers(updateSelected.filter((value) => value.usernameActive));
    },
    [selectedUsers],
  );

  const workflowTabs: KanbanTabsType = useMemo(() => {
    const tabs: KanbanTabsType = {};

    workflows.forEach((workflow, workflowIdx) => {
      tabs[workflowIdx] = {
        title: workflow.changeWorkflowName,
        content: (
          <>
            {workflow.groups?.flatMap(
              (group, cabGroupIdx) =>
                group.users?.flatMap((user, cabGroupUserIdx) => {
                  if (user.username === curentUser) {
                    return (
                      <ChangeRequestApprovalComponent
                        key={`${workflowIdx}-${cabGroupIdx}-${cabGroupUserIdx}`}
                        mode={mode}
                        workflowIdx={workflowIdx}
                        control={control}
                        cabGroupIdx={cabGroupIdx}
                        cabGroupUserIdx={cabGroupUserIdx}
                        onDelete={undefined}
                        roleIdx={roleIdx}
                        changeFlowNodeId={cabRole?.changeFlowNodeId || 0}
                        modeApproval={modeApproval}
                        handleCheckboxChange={handleCheckboxChange}
                        refetchChangeRequestRoles={refetchChangeRequestRoles}
                      />
                    );
                  }
                  return [];
                }) ?? [],
            )}
          </>
        ),
      };
    });

    return tabs;
  }, [cabRole?.changeFlowNodeId, control, curentUser, handleCheckboxChange, mode, modeApproval, refetchChangeRequestRoles, roleIdx, workflows]);

  const isCurrentUserofCabs = useMemo(() => {
    return workflows.some((workflow) => workflow.groups?.some((group) => group.users?.some((user) => user.username === curentUser)));
  }, [workflows, curentUser]);

  if (!isCurrentUserofCabs) {
    return null;
  }
  return (
    <Controller
      control={control}
      name={`activeRoles.${roleIdx}`}
      render={({ field }) => (
        <KanbanAccordion
          value={field.value}
          onChange={(value) => {
            if (typeof value === 'string' || value === null) {
              field.onChange(value);
            }
          }}
          data={[
            {
              content: (
                <KanbanTabs
                  configs={{
                    value: activeWorkFlowIdx,
                    onChange: (value) => {
                      if (value) {
                        setActiveWorkFlowIdx(value);
                      }
                    },
                  }}
                  tabs={workflowTabs}
                />
              ),
              title: <KanbanText fw={500}>{'My tasks'}</KanbanText>,
            },
          ]}
        />
      )}
    />
  );
};

export default CurrentUserCabTasks;
