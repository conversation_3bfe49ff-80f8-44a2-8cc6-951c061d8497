import React, { useCallback, useState } from 'react';
import { DragDropContext, DragUpdate, Droppable, DropResult } from '@hello-pangea/dnd';
import { useFieldArray, useFormContext } from 'react-hook-form';
import { Box } from '@mantine/core';
import NodeCabGroup from './NodeCabGroup';
import { ChangeRequestRole, ChangeRequestRoleList, ChangeRequestRoleUserList } from '@core/schema/ChangeRequestRole';
import { ChangeRequestRoleUser } from '@core/schema/ChangeRequestRoleUser';
import { KanbanButton } from 'kanban-design-system';
import styled from '../ChangeRequestDetail.module.scss';
import { EntityAction } from '@common/constants/EntityActionConstants';
import { clsx } from 'clsx';
import { ChangeRequestApprovalMode } from '@common/constants/ChangeRequestApprovalConstants';
import { MappedUser } from '@pages/change/request/role/ChangeRequestDetailRole';
import { ApprovalNotificationModal } from '@pages/change/request/role/notify/ApprovalNotificationModal';
import { ChangeStageType } from '@common/constants/ChageStageType';

interface NodeCabComponentProps {
  roleIdx: number;
  workflowIdx: number;
  mode: EntityAction;
  role: ChangeRequestRole;
  modeApproval: ChangeRequestApprovalMode;
  refetchChangeRequestRoles: () => void;
  stage?: ChangeStageType;
}

const NodeCabGroupList: React.FC<NodeCabComponentProps> = ({ mode, modeApproval, refetchChangeRequestRoles, role, roleIdx, stage, workflowIdx }) => {
  const { control, getValues, resetField, setValue } = useFormContext<ChangeRequestRoleList>();

  const groupsPath = `roles.${roleIdx}.workflows.${workflowIdx}.groups` as const;
  const {
    append: appendGroup,
    fields: cabGroups,
    remove: removeGroup,
  } = useFieldArray({
    control,
    name: groupsPath,
  });

  const changeFlowNodeId = getValues(`roles.${roleIdx}.changeFlowNodeId`);
  const [isHighlightListLevel, setIsHighlightListLevel] = useState(false);

  const handleDragEnd = useCallback(
    (result: DropResult) => {
      setIsHighlightListLevel(false);
      const { destination, source } = result;

      if (!destination) {
        const sourceGroupIndex = parseInt(source.droppableId);
        if (!isNaN(sourceGroupIndex) && cabGroups[sourceGroupIndex] !== undefined) {
          let currentGroupedCabUserGroups: ChangeRequestRoleUserList[] = getValues(groupsPath) || [];

          const sourceGroupUsers = [...currentGroupedCabUserGroups[sourceGroupIndex].users];
          sourceGroupUsers.splice(source.index, 1);

          currentGroupedCabUserGroups[sourceGroupIndex].users = sourceGroupUsers;

          if (sourceGroupUsers.length === 0) {
            currentGroupedCabUserGroups = currentGroupedCabUserGroups.filter((_, idx) => idx !== sourceGroupIndex);
            resetField(`${groupsPath}.${sourceGroupIndex}.cabGroupName`);
          }
          setValue(groupsPath, currentGroupedCabUserGroups);
        }
        return;
      }

      if (destination.droppableId.startsWith('add-level-zone-')) {
        const insertIndex = parseInt(destination.droppableId.replace('add-level-zone-', ''));
        const sourceGroupIndex = parseInt(source.droppableId);
        const currentGroupedCabUserGroups: ChangeRequestRoleUserList[] = getValues(groupsPath) || [];

        if (isNaN(sourceGroupIndex) || !currentGroupedCabUserGroups[sourceGroupIndex]) {
          console.warn('Invalid source group index for adding new level.');
          return;
        }

        const sourceGroupUsers = [...currentGroupedCabUserGroups[sourceGroupIndex].users];
        const draggedItem: ChangeRequestRoleUser | undefined = sourceGroupUsers.splice(source.index, 1)[0];

        if (!draggedItem) {
          return;
        }

        currentGroupedCabUserGroups[sourceGroupIndex].users = sourceGroupUsers;

        const newGroupObject: ChangeRequestRoleUserList = {
          users: [draggedItem],
        };

        const updatedGroupedCabUserGroups = [...currentGroupedCabUserGroups];
        updatedGroupedCabUserGroups.splice(insertIndex, 0, newGroupObject);

        if (sourceGroupUsers.length === 0) {
          const targetRemoveIndex = sourceGroupIndex < insertIndex ? sourceGroupIndex : sourceGroupIndex + 1;
          updatedGroupedCabUserGroups.splice(targetRemoveIndex, 1);
          resetField(`${groupsPath}.${sourceGroupIndex}.cabGroupName`);
        }

        setValue(groupsPath, updatedGroupedCabUserGroups);
        return;
      }

      const sourceGroupIndex = parseInt(source.droppableId);
      const destinationGroupIndex = parseInt(destination.droppableId);

      const isValidSourceGroup = !isNaN(sourceGroupIndex) && cabGroups[sourceGroupIndex] !== undefined;
      const isValidDestinationGroup = !isNaN(destinationGroupIndex) && cabGroups[destinationGroupIndex] !== undefined;

      if (!isValidSourceGroup || !isValidDestinationGroup) {
        console.warn('Invalid source or destination group index.');
        return;
      }

      if (source.droppableId === destination.droppableId) {
        const currentGroupUsers = [...(getValues(`${groupsPath}.${sourceGroupIndex}.users`) || [])];
        const [removed] = currentGroupUsers.splice(source.index, 1);
        if (!removed) {
          return;
        }

        currentGroupUsers.splice(destination.index, 0, removed);
        setValue(`${groupsPath}.${sourceGroupIndex}.users`, currentGroupUsers);
        return;
      }

      if (source.droppableId !== destination.droppableId) {
        let currentGroupedCabUserGroups: ChangeRequestRoleUserList[] = getValues(groupsPath) || [];

        const sourceGroupUsers = [...currentGroupedCabUserGroups[sourceGroupIndex].users];
        const destGroupUsers = [...currentGroupedCabUserGroups[destinationGroupIndex].users];

        const [removed] = sourceGroupUsers.splice(source.index, 1);
        if (!removed) {
          return;
        }

        destGroupUsers.splice(destination.index, 0, removed);

        currentGroupedCabUserGroups[sourceGroupIndex].users = sourceGroupUsers;
        currentGroupedCabUserGroups[destinationGroupIndex].users = destGroupUsers;

        if (sourceGroupUsers.length === 0) {
          currentGroupedCabUserGroups = currentGroupedCabUserGroups.filter((_, idx) => idx !== sourceGroupIndex);
          resetField(`${groupsPath}.${sourceGroupIndex}.cabGroupName`);
        }

        setValue(groupsPath, currentGroupedCabUserGroups);
        return;
      }
    },
    [cabGroups, getValues, groupsPath, setValue, resetField],
  );

  //Params for sent notification
  const [selectedUsers, setSelectedUsers] = useState<MappedUser[]>([]);

  const handleDragUpdate = useCallback(
    (update: DragUpdate) => {
      const { destination } = update;
      const isOutOfLevel = !destination || !cabGroups.some((l) => l.id === destination.droppableId);
      setIsHighlightListLevel(isOutOfLevel);
    },
    [cabGroups],
  );

  //Method for sent notification
  const handleCheckboxChange = (user: MappedUser, checked: boolean) => {
    const updateSelected = checked ? [...selectedUsers, user] : selectedUsers.filter((u) => u.username !== user.username);
    setSelectedUsers(updateSelected.filter((value) => value.usernameActive));
  };

  return (
    <DragDropContext onDragEnd={handleDragEnd} onDragUpdate={handleDragUpdate}>
      <Box
        className={clsx(styled.boxBase, {
          [styled.boxHighlight]: isHighlightListLevel,
        })}>
        {/* Drop zone trên cùng */}
        <Droppable droppableId='add-level-zone-0'>
          {(provided, snapshot) => (
            <Box
              ref={provided.innerRef}
              {...provided.droppableProps}
              className={clsx(styled.dropWrapper, snapshot.isDraggingOver && styled.isDraggingOver)}>
              {provided.placeholder}
            </Box>
          )}
        </Droppable>

        {/* Render từng CAB group */}
        {cabGroups?.map((cabGroup, cabGroupIdx) => (
          <Box key={cabGroup.id}>
            <Box mb='sm'>
              <ApprovalNotificationModal
                changeRequestId={role.changeRequestId ?? 0}
                changeFlowNodeId={role?.changeFlowNode?.id || 0}
                selectedUsers={selectedUsers}
                stage={stage}
              />
            </Box>

            <NodeCabGroup
              mode={mode}
              modeApproval={modeApproval}
              key={`cab-${cabGroupIdx}`}
              roleIdx={roleIdx}
              control={control}
              cabGroupIdx={cabGroupIdx}
              changeFlowNodeId={changeFlowNodeId || 0}
              workflowIdx={workflowIdx}
              handleCheckboxChange={handleCheckboxChange}
              refetchChangeRequestRoles={refetchChangeRequestRoles}
              onDeleteCabGroup={() => {
                resetField(`${groupsPath}.${cabGroupIdx}.cabGroupName`);
                removeGroup(cabGroupIdx);
              }}
            />

            {/* Drop zone dưới mỗi group */}
            <Droppable droppableId={`add-level-zone-${cabGroupIdx + 1}`}>
              {(provided, snapshot) => (
                <Box
                  ref={provided.innerRef}
                  {...provided.droppableProps}
                  className={clsx(styled.dropWrapper, snapshot.isDraggingOver && styled.isDraggingOver)}>
                  {provided.placeholder}
                </Box>
              )}
            </Droppable>
          </Box>
        ))}
      </Box>

      {EntityAction.VIEW !== mode && (
        <Box mt='sm'>
          <KanbanButton
            size='compact-xs'
            variant='filled'
            onClick={() =>
              appendGroup({
                users: [
                  {
                    id: -1,
                    username: '',
                    changeRequestRoleId: 0,
                    cabGroup: cabGroups.length + 1,
                    cabGroupOrder: 1,
                  },
                ],
              })
            }>
            Add CAB +
          </KanbanButton>
        </Box>
      )}
    </DragDropContext>
  );
};

export default NodeCabGroupList;
