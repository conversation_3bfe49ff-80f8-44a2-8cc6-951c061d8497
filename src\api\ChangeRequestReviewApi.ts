import { BaseURL } from '@common/constants/BaseUrl';
import { createRequest } from './Utils';
import { createResponseSchema } from '@core/schema/Common';
import { RequestConfig } from '@core/api';
import { ChangeRequestReviewSchema, CoordinatorReviewRequest, OwnerReviewRequest, ApproverStatusRequest } from '@core/schema/ChangeRequestReview';

/**
 * ChangeRequestReviewApi - API client for Change Request Review operations
 *
 * Matches backend Spring Boot controller endpoints:
 * - GET /review - Find review details
 * - POST /review/saveOrUpdateForCoordinator - Save/update for coordinator
 * - POST /review/saveOrUpdateForOwner - Save/update for owner (with file upload)
 * - PUT /review/updateReviewStatus - Update review status
 * - PUT /review/updateApproverStatus - Update approver status
 * - GET /review/downloads - Download review document
 */
export class ChangeRequestReviewApi {
  /**
   * Find Change Request Review - GET endpoint
   * Retrieves the review details for a specific change request
   */
  static findChangeRequestReview(changeRequestId: number) {
    return createRequest({
      url: BaseURL.changeRequestReview(changeRequestId),
      method: 'GET',
      schema: createResponseSchema(ChangeRequestReviewSchema),
    });
  }

  /**
   * Save/Update for Coordinator - POST endpoint with JSON body
   * Allows coordinator to assign owner and add notes
   */
  static saveOrUpdateForCoordinator(changeRequestId: number, data: CoordinatorReviewRequest) {
    return createRequest({
      url: `${BaseURL.changeRequestReview(changeRequestId)}/saveOrUpdateForCoordinator`,
      method: 'POST',
      data,
      schema: createResponseSchema(ChangeRequestReviewSchema),
    });
  }

  /**
   * Save/Update for Owner - POST endpoint with multipart form data (file upload)
   * Allows owner to assign approvers, upload documents, and add notes
   */
  static saveOrUpdateForOwner(changeRequestId: number, data: OwnerReviewRequest, file?: File) {
    const formData = new FormData();

    // Add the request data as JSON Blob (following the pattern from other APIs)
    const jsonBlob = new Blob([JSON.stringify(data)], {
      type: 'application/json',
    });
    formData.append('request', jsonBlob);

    // Add file if provided
    if (file) {
      formData.append('file', file);
    }

    return createRequest({
      url: `${BaseURL.changeRequestReview(changeRequestId)}/saveOrUpdateForOwner`,
      method: 'POST',
      data: formData,
      schema: createResponseSchema(ChangeRequestReviewSchema),
    });
  }

  /**
   * Update Review Status - PUT endpoint with status parameter
   * Updates the review status (IN_PROGRESS, SENT_TO_OWNER, SENT_TO_APPROVER, etc.)
   */
  static updateReviewStatus(changeRequestId: number, status: string) {
    return createRequest({
      url: `${BaseURL.changeRequestReview(changeRequestId)}/updateReviewStatus?status=${encodeURIComponent(status)}`,
      method: 'PUT',
      schema: createResponseSchema(ChangeRequestReviewSchema),
    });
  }

  /**
   * Update Approver Status - PUT endpoint with JSON body
   * Allows approvers to accept or reject the review
   */
  static updateApproverStatus(changeRequestId: number, data: ApproverStatusRequest) {
    return createRequest({
      url: `${BaseURL.changeRequestReview(changeRequestId)}/updateApproverStatus`,
      method: 'PUT',
      data,
      schema: createResponseSchema(ChangeRequestReviewSchema),
    });
  }

  /**
   * Download Review Document - GET endpoint returning file stream
   * Downloads the review document as a file
   */
  static downloadReviewDocument(changeRequestId: number): RequestConfig<Blob> {
    return createRequest<Blob>({
      url: `${BaseURL.changeRequestReview(changeRequestId)}/downloads`,
      method: 'GET',
      responseType: 'blob', // Expect binary data
    });
  }

  /**
   * Legacy method for backward compatibility
   * @deprecated Use specific methods instead
   */
  static updateReview(changeRequestId: number, data: any) {
    return createRequest({
      url: BaseURL.changeRequestReview(changeRequestId),
      method: 'PUT',
      data,
      schema: createResponseSchema(ChangeRequestReviewSchema),
    });
  }
}
