<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="36701bc7-1084-489a-8e2b-fe3b00302ce0" name="Changes" comment="merge">
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/AmtShortTextContent.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/AmtShortTextContent.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/change/template/components/FieldRenderer.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/change/template/components/FieldRenderer.tsx" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="28ea3bad370c56b4b37c4684f4490bf6a226abd4" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitToolBoxStore">
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="ft/change-template" />
                    <option name="lastUsedInstant" value="1746668282" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$" />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
    &quot;associatedIndex&quot;: 1
    }</component>
  <component name="ProjectId" id="2vAcI8uZYySrCxsAu95F0V3NKlP" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "git-widget-placeholder": "ft/change-template",
    "last_opened_file_path": "D:/mbamt-frontend",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "ts.external.directory.path": "D:\\mbamt-frontend\\node_modules\\typescript\\lib",
    "vue.rearranger.settings.migration": "true"
  }
}</component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="36701bc7-1084-489a-8e2b-fe3b00302ce0" name="Changes" comment="" />
      <created>1743591238799</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1743591238799</updated>
      <workItem from="1743591240348" duration="208000" />
      <workItem from="1745204706405" duration="494000" />
      <workItem from="1745205543747" duration="2953000" />
      <workItem from="1746667740016" duration="4441000" />
      <workItem from="1747191937445" duration="1096000" />
    </task>
    <task id="LOCAL-00001" summary="merge">
      <option name="closed" value="true" />
      <created>1745205050867</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1745205050867</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="merge" />
    <option name="LAST_COMMIT_MESSAGE" value="merge" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/pages/configuration/user/roles/RoleDetailPage.tsx</url>
          <line>33</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>