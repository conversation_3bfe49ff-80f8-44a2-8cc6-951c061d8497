import { ChangeRequestApi } from '@api/ChangeRequestApi';
import useInfiniteFetch from '@core/hooks/useInfiniteFetch';
import { Stack, Switch } from '@mantine/core';
import { getDefaultTableAffected, KanbanMultiSelect, KanbanNumberInput } from 'kanban-design-system';
import React, { useCallback, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import { CustomNodeData } from '@pages/change/request/workflow/reactFlow/CustomNode';
import { SelectWithPage } from '@components/SelectWithPage';
import { initOrUpdatedFilterPayloads, UserPageFilter } from '@pages/configuration/user/users';
import { UserApi } from '@api/UserApi';

type SettingsTabProps = {
  customNodeData: CustomNodeData | undefined;
  setCustomNodeData: (data: React.SetStateAction<CustomNodeData | undefined>) => void;
};

export const SettingsTab: React.FC<SettingsTabProps> = ({ customNodeData, setCustomNodeData }) => {
  const changeId = Number(useParams().changeId);
  const [inputFilters, setInputFilters] = useState<UserPageFilter>({});
  const {
    fetchNextPage: fetchNextPageUsers,
    flatData: usersData,
    isFetching: isCabsFetching,
  } = useInfiniteFetch(ChangeRequestApi.findAllUsersCab(initOrUpdatedFilterPayloads(getDefaultTableAffected(), inputFilters), changeId), {
    showLoading: false,
  });

  const [inputAssignFilters, setInputAssignFilters] = useState<UserPageFilter>({});
  const {
    fetchNextPage: fetchAssignNextPageUsers,
    flatData: usersAssignData,
    isFetching: isAssignFetching,
  } = useInfiniteFetch(UserApi.findAll(initOrUpdatedFilterPayloads(getDefaultTableAffected(), inputAssignFilters)), {
    showLoading: false,
  });

  const usersComboxOptions = useMemo(() => {
    return usersData.map((obj) => ({ value: `${obj.userName}`, label: obj.userName }));
  }, [usersData]);

  const usersAssignComboxOptions = useMemo(() => {
    return usersAssignData.map((obj) => ({ value: `${obj.userName}`, label: obj.userName }));
  }, [usersAssignData]);

  const handleInputFilterChange = useCallback(
    (key: keyof typeof inputFilters, value: string) => {
      const updatedFilters = { ...inputFilters, [key]: value };
      setInputFilters(updatedFilters);
    },
    [inputFilters],
  );

  const handleAssignInputFilterChange = useCallback(
    (key: keyof typeof inputAssignFilters, value: string) => {
      const updatedFilters = { ...inputAssignFilters, [key]: value };
      setInputAssignFilters(updatedFilters);
    },
    [inputAssignFilters],
  );

  return (
    <Stack gap='md'>
      <Switch
        label='Retry on Fail'
        description='Retry the operation if it fails'
        checked={customNodeData?.settings?.retryOnFail}
        onChange={(data) =>
          setCustomNodeData((prev) => {
            if (!prev) {
              return prev;
            }
            return {
              ...prev,
              settings: {
                ...prev.settings,
                retryOnFail: data.currentTarget.checked,
              },
            };
          })
        }
      />

      {customNodeData?.settings?.retryOnFail && (
        <>
          <KanbanNumberInput
            allowDecimal={false}
            label='Max Tries'
            description='Maximum number of retry attempts'
            value={customNodeData?.settings?.maxTries}
            onChange={(data) =>
              setCustomNodeData((prev) => {
                if (!prev) {
                  return prev;
                }
                return {
                  ...prev,
                  settings: {
                    ...prev.settings,
                    maxTries: Number(data || 0),
                  },
                };
              })
            }
            min={1}
            max={10}
          />
          <KanbanNumberInput
            allowDecimal={false}
            label='Wait Between Tries (ms)'
            description='Time to wait between retry attempts'
            value={customNodeData?.settings?.waitBetweenTries}
            onChange={(data) =>
              setCustomNodeData((prev) => {
                if (!prev) {
                  return prev;
                }
                return {
                  ...prev,
                  settings: {
                    ...prev.settings,
                    waitBetweenTries: Number(data || 0),
                  },
                };
              })
            }
            min={0}
            max={600}
          />
        </>
      )}

      <SelectWithPage
        options={usersAssignComboxOptions}
        onChange={(data) => {
          setCustomNodeData((prev) => {
            if (!prev) {
              return prev;
            }
            return {
              ...prev,
              settings: {
                ...prev.settings,
                assign: data,
              },
            };
          });
        }}
        required
        label='Assign'
        onSearch={(val) => handleAssignInputFilterChange('filterUsername', val || '')}
        handleScrollToBottom={fetchAssignNextPageUsers}
        value={
          [
            ...usersAssignComboxOptions,
            {
              value: customNodeData?.settings?.assign || '',
              label: customNodeData?.settings?.assign || '',
            },
          ].filter((e) => e.value === customNodeData?.settings?.assign)[0] || ''
        }
        isLoading={isAssignFetching}
        onBlur={() => handleAssignInputFilterChange('filterUsername', '')}
      />

      <SelectWithPage
        options={usersComboxOptions}
        onChange={(data) => {
          setCustomNodeData((prev) => {
            if (!prev) {
              return prev;
            }
            return {
              ...prev,
              settings: {
                ...prev.settings,
                userCab: data,
              },
            };
          });
        }}
        required
        label='User CAB'
        onSearch={(val) => handleInputFilterChange('filterUsername', val || '')}
        handleScrollToBottom={fetchNextPageUsers}
        value={
          [
            ...usersComboxOptions,
            {
              value: customNodeData?.settings?.userCab || '',
              label: customNodeData?.settings?.userCab || '',
            },
          ].filter((e) => e.value === customNodeData?.settings?.userCab)[0] || undefined
        }
        isLoading={isCabsFetching}
        onBlur={() => handleInputFilterChange('filterUsername', '')}
      />

      <KanbanMultiSelect
        label='Cis tác động'
        placeholder='Select Cis'
        data={[
          { value: 'AMT', label: 'AMT' },
          { value: 'Monitor', label: 'Monitor' },
        ]}
        value={customNodeData?.settings?.cis || []}
        onChange={(value) =>
          setCustomNodeData((prev) => {
            if (!prev) {
              return prev;
            }
            return {
              ...prev,
              settings: {
                ...prev.settings,
                cis: value || [],
              },
            };
          })
        }
      />
    </Stack>
  );
};

export default SettingsTab;
