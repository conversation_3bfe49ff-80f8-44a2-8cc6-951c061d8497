import React from 'react';
import { Box, Group, Radio } from '@mantine/core';
import { CustomFieldModel } from '@models/CustomField';
import { Controller, UseFormReturn, useWatch } from 'react-hook-form';
import PicklistValueSection from './PicklistValueSection';
import ReferenceEntitySection from './PicklistReferenceEntitySection';
import { KanbanCheckbox } from 'kanban-design-system';

type Props = {
  form: UseFormReturn<CustomFieldModel>;
};

const PicklistConfigTable: React.FC<Props> = ({ form }) => {
  const isReference = useWatch({ control: form.control, name: 'isReference' });

  return (
    <Box>
      <Box mt='sm' mb='lg'>
        <KanbanCheckbox
          label='Is multiple selections'
          disabled={!!form.getValues('id')}
          checked={form.watch('isMultiple') ?? false}
          onChange={(event) => form.setValue('isMultiple', event.currentTarget.checked)}
        />
      </Box>

      <Box mb='sm'>
        <Controller
          name='isReference'
          control={form.control}
          render={({ field }) => (
            <Radio.Group {...field} value={isReference ? 'true' : 'false'} onChange={(val) => field.onChange(val === 'true')}>
              <Group mt='xs'>
                <Radio value='false' label='Value' />
                <Radio value='true' label='Reference Entity' />
              </Group>
            </Radio.Group>
          )}
        />
      </Box>

      {isReference ? <ReferenceEntitySection form={form} /> : <PicklistValueSection form={form} />}
    </Box>
  );
};

export default PicklistConfigTable;
