import { string, z } from 'zod';
import { COMMON_DESCRIPTION_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { INPUT_REQUIRE } from '@core/message/MesageConstant';

export const ChangeFlowNodeTypeEnum = z.enum(['CAB', 'APPROVAL']);
export type ChangeFlowNodeType = z.infer<typeof ChangeFlowNodeTypeEnum>;

export const ChangeFlowNodeGroupModelSchema = z.object({
  id: z.string(),
  name: z.string(),
});
export type ChangeFlowNodeGroupModel = z.infer<typeof ChangeFlowNodeGroupModelSchema>;

export const ChangeFlowNodeModelSchema = z.object({
  id: z.number().nullish(),
  nodeId: z.string(),
  name: z.string().trim().min(1, { message: INPUT_REQUIRE }),
  groups: z.array(ChangeFlowNodeGroupModelSchema),
  nodeLevel: z.number(),
  type: ChangeFlowNodeTypeEnum,
});
export type ChangeFlowNodeModel = z.infer<typeof ChangeFlowNodeModelSchema>;

export const ChangeFlowNotificationModelSchema = z.object({
  emailTemplateId: z.number().nullable(),
  notifyTo: z.array(string()).optional(),
  notifyNode: z.array(string()).optional(),
  statusId: z.string().optional(),
});
export type ChangeFlowNotificationModel = z.infer<typeof ChangeFlowNotificationModelSchema>;

export const ChangeFlowModelSchema = z.object({
  id: z.number().nullish(),
  name: z.string().trim().min(1, { message: INPUT_REQUIRE }),
  description: z.string().max(COMMON_DESCRIPTION_MAX_LENGTH).nullish(),
  flowNodes: z.string(),
  flowEdges: z.string(),
  nodes: z.array(ChangeFlowNodeModelSchema).nullish(),
  notifications: z.array(ChangeFlowNotificationModelSchema).nullish(),
});
export type ChangeFlowModel = z.infer<typeof ChangeFlowModelSchema>;
