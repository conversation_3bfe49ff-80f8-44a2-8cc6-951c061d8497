import React from 'react';
import { Box } from '@mantine/core';
import { UseFormReturn } from 'react-hook-form';
import { CustomFieldModel } from '@models/CustomField';
import { CustomFieldType, CustomFieldTypeEnum } from '@common/constants/CustomFieldType';
import customStyled from '@resources/styles/Common.module.scss';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import PicklistConfigTable from './valueDetail/PicklistOption';
import { CustomFieldValue } from './valueDetail/CustomFieldValue';

type Props = {
  type: CustomFieldType;
  form: UseFormReturn<CustomFieldModel>;
};

export const CustomFieldRenderer: React.FC<Props> = ({ form, type }) => {
  return (
    <Box mt='sm' className={customStyled.elementBorder}>
      <HeaderTitleComponent title='Field - Information' />
      {(type === CustomFieldTypeEnum.Enum.SINGLE_LINE ||
        type === CustomFieldTypeEnum.Enum.MULTI_LINE ||
        type === CustomFieldTypeEnum.Enum.NUMBER) && <CustomFieldValue form={form} type={type} />}
      {type === CustomFieldTypeEnum.Enum.PICKLIST && <PicklistConfigTable form={form} />}
    </Box>
  );
};
