import { Box, Tooltip } from '@mantine/core';
import { IconAlertTriangle } from '@tabler/icons-react';
import { Handle, Position } from '@xyflow/react';
import React, { useEffect, useState } from 'react';
import stylesCss from './CustomNode.module.scss';
import { ChangeApplicationTypeEnum } from '@common/constants/ChangeApplicationConstants';
import { KanbanText } from 'kanban-design-system';
import clsx from 'clsx';

export type CustomNodeData = {
  label: string;
  icon: string;
  parameters?: NodeParameterData;
  subtitle?: string;
  settings?: NodeSettingData;
  cis?: string[];
  application: ChangeApplicationTypeEnum;
  content?: string;
  title?: string;
  onResizeStart?: () => void;
  onResizeStop?: () => void;
  draggable?: boolean;
};

type CustomNodeProps = {
  id: string;
  data: CustomNodeData;
  selected?: boolean;
};

export type NodeParameterData = {
  operation?: string;
  parameter1?: string;
  parameter2?: string;
};

export type NodeSettingData = {
  userCab?: string;
  cis?: string[];
  assign?: string;
  retryOnFail?: boolean;
  maxTries?: number;
  waitBetweenTries?: number;
};

const CustomNode = ({ data, selected }: CustomNodeProps) => {
  const [isError, setIsError] = useState<boolean>(false);
  const [errorDetails, setErrorDetails] = useState<string>('');
  const iconSrc = data.icon;

  useEffect(() => {
    const hasError = () => {
      const parameters = data.parameters;
      const settings = data.settings;
      const errors: string[] = [];

      // Kiểm tra các tham số
      if (!parameters?.parameter1) {
        errors.push('Parameter 1 is required.');
      }
      if (!parameters?.parameter2) {
        errors.push('Parameter 2 is required.');
      }

      // Kiểm tra các cài đặt
      if (!settings?.userCab) {
        errors.push('User CAB is required.');
      }

      if (errors.length > 0) {
        setErrorDetails(errors.join('\n'));
        return true;
      }

      return false;
    };

    setIsError(hasError());
  }, [data]);

  // Build các class names với clsx
  const nodeClass = clsx(stylesCss.customNode, selected && stylesCss.selected, !selected && isError && stylesCss.error);

  const statusIndicatorClass = clsx(stylesCss.statusIndicator, {
    [stylesCss.errorStatus]: isError,
    [stylesCss.successStatus]: !isError,
  });

  const targetHandleClass = clsx(stylesCss.handle, stylesCss.targetHandle, stylesCss.leftHandle);

  const sourceHandleClass = clsx(stylesCss.handle, stylesCss.sourceHandle, stylesCss.rightHandle);

  const leftHintClass = clsx(stylesCss.connectionHint, stylesCss.leftHint);

  const rightHintClass = clsx(stylesCss.connectionHint, stylesCss.rightHint);

  return (
    <Box className={stylesCss.nodeWrapper}>
      <Box className={nodeClass}>
        {/* Icon Container */}
        <Box className={stylesCss.iconContainer}>
          <img src={iconSrc} alt={data.label} className={stylesCss.nodeIcon} />
        </Box>

        {/* Content Container */}
        <Box className={stylesCss.contentContainer}>
          <KanbanText lineClamp={2} size='sm' fw={600} className={stylesCss.nodeLabel}>
            {data.label}
          </KanbanText>

          {data.subtitle && (
            <KanbanText size='xs' lineClamp={1} className={stylesCss.nodeSubtitle}>
              {data.subtitle}
            </KanbanText>
          )}
        </Box>

        {/* Error Warning Triangle with Tooltip */}
        {isError && (
          <Tooltip label={errorDetails} position='top' withArrow multiline className={stylesCss.errorTooltip}>
            <Box className={stylesCss.errorIcon}>
              <IconAlertTriangle size={18} />
            </Box>
          </Tooltip>
        )}

        {/* Status Indicator */}
        <Box className={statusIndicatorClass} />

        {/* Target Handle - For incoming connections (Left) */}
        <Handle type='target' position={Position.Left} className={targetHandleClass} id='target-left' />

        {/* Source Handle - For outgoing connections (Right) */}
        <Handle type='source' position={Position.Right} className={sourceHandleClass} id='source-right' />

        {/* Connection Hints */}
        <Box className={stylesCss.connectionHints}>
          <Box className={leftHintClass}>
            <Box className={stylesCss.hintDot} />
          </Box>
          <Box className={rightHintClass}>
            <Box className={stylesCss.hintDot} />
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default CustomNode;
