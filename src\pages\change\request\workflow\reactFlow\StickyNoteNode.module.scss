.stickyNote {
  position: relative;
  width: 100%;
  height: 100%;
  min-width: 150px;
  min-height: 100px;
  background-color: #fff8c5;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border: 1px solid #e6d700;
  overflow: hidden;
  cursor: move;
  transition: all 0.2s ease;
  z-index: 0;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  &.selected {
    border-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }

  &.resizing {
    cursor: default;
  }
}

.contentWrapper {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.resizeIndicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  color: #999;
  transition: opacity 0.2s ease;
  pointer-events: none;
  z-index: 1;
}

.editMode {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 8px;

  .titleInput {
    flex-shrink: 0;

    input {
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid #ddd;
      font-weight: 600;

      &:focus {
        border-color: #3b82f6;
      }
    }
  }

  .contentTextarea {
    flex: 1;
    min-height: 60px;

    textarea {
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid #ddd;
      resize: none;
      height: 100%;

      &:focus {
        border-color: #3b82f6;
      }
    }
  }

  .editActions {
    display: flex;
    gap: 12px;
    flex-shrink: 0;
  }

  .saveButton,
  .cancelButton {
    cursor: pointer;
    font-weight: 600;
    padding: 2px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.1);
    }
  }

  .saveButton {
    color: #3b82f6;
  }

  .cancelButton {
    color: #666;
  }
}

.displayMode {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 8px;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 8px;
    flex-shrink: 0;
  }

  .title {
    font-weight: 700;
    word-wrap: break-word;
    flex: 1;
    color: #2d3748;
    line-height: 1.3;
  }

  .content {
    flex: 1;
    word-wrap: break-word;
    white-space: pre-wrap;
    overflow-y: auto;
    color: #4a5568;
    line-height: 1.4;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 2px;
    }
  }

  .editIcon {
    cursor: pointer;
    color: #666;
    transition: all 0.2s ease;
    flex-shrink: 0;
    padding: 2px;
    border-radius: 2px;

    &:hover {
      color: #3b82f6;
      background: rgba(59, 130, 246, 0.1);
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .stickyNote {
    min-width: 120px;
    min-height: 80px;
    padding: 8px;
  }

  .editMode {
    .titleInput input {
      font-size: 12px;
    }

    .contentTextarea textarea {
      font-size: 11px;
    }
  }

  .displayMode {
    .title {
      font-size: 12px;
    }

    .content {
      font-size: 11px;
    }
  }
}