import { z } from 'zod';

export const CustomFieldTypeEnum = z.enum(['SINGLE_LINE', 'MULTI_LINE', 'RICH_TEXT', 'NUMBER', 'DATE', 'PICKLIST']);
export type CustomFieldType = z.infer<typeof CustomFieldTypeEnum>;

export const CUSTOM_FIELD_TYPE_LABEL: Record<CustomFieldType, string> = {
  SINGLE_LINE: 'Single Line',
  MULTI_LINE: 'Multi Line',
  NUMBER: 'Numeric',
  DATE: 'Date/Time',
  PICKLIST: 'Picklist',
  RICH_TEXT: 'Rich Text',
};
