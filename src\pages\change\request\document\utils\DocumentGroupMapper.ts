import {
  ChangeRequestDocumentForm,
  ChangeRequestDocumentGroupCoordinatorRequest,
  ChangeRequestDocumentGroupForm,
  ChangeRequestDocumentGroupModel,
  ChangeRequestDocumentOwnerForm,
  ChangeRequestDocumentApproverForm,
} from '@models/ChangeRequestDocumentGroupModel';

export function mapChangeRequestDocumentGroupModelToForm(groups: ChangeRequestDocumentGroupModel[]): ChangeRequestDocumentGroupForm[] {
  return groups.map((group) => ({
    documentGroupId: group.id,
    documentGroupName: group.name,
    owners: group.owners.map<ChangeRequestDocumentOwnerForm>((owner) => ({
      username: owner.username,
      displayName: owner.displayName,
      status: owner.status,
      isActive: owner.isActive,
      documents:
        owner.documents && owner.documents.length > 0
          ? owner.documents?.map<ChangeRequestDocumentForm>((doc) => ({
              tempId: undefined,
              id: doc.id,
              type: doc.type,
              file: undefined,
              documentUrl: doc.documentUrl || undefined,
              documentName: doc.documentName || undefined,
              approvers: doc.approvers.map<ChangeRequestDocumentApproverForm>((appr) => ({
                username: appr.user.username,
                displayName: appr.user.displayName,
                documentApproverLevel: appr.documentApproverLevel,
              })),
            }))
          : undefined,
    })),
    cabs:
      group.approvers && group.approvers.length > 0
        ? group.approvers?.map((appr) => ({
            username: appr.user.username,
            displayName: appr.user.displayName,
          }))
        : undefined,
  }));
}

export function mapFormToCoordinatorRequests(forms: ChangeRequestDocumentGroupForm[]): ChangeRequestDocumentGroupCoordinatorRequest[] {
  return forms.map((formGroup) => ({
    id: formGroup.documentGroupId,
    name: formGroup.documentGroupName,
    owners: formGroup.owners.flatMap((owner) => owner.username),
    documentGroup: formGroup.owners.some((owner) => (owner.documents ?? []).length > 0)
      ? {
          id: undefined,
          documents: formGroup.owners.flatMap((owner) =>
            (owner.documents ?? []).map((doc) => ({
              tempId: doc.tempId,
              id: doc.id ?? undefined,
              type: doc.type,
              file: doc.file,
              documentUrl: doc.documentUrl,
              documentName: doc.documentName,
              approvers: doc.approvers.map((ap) => ({
                username: ap.username,
                documentApproverLevel: ap.documentApproverLevel,
              })),
            })),
          ),
          approvers: (formGroup.cabs ?? []).map((cab) => cab.username),
        }
      : undefined,
    ownerToChange: undefined,
  }));
}
