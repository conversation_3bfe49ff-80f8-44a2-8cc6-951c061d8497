import { DIGITS_ONLY_REGEX } from '@common/constants/RegexConstant';

export const isId = (s: string): boolean => DIGITS_ONLY_REGEX.test(s);

export const capitalizeFirst = (s: string): string => s.charAt(0).toUpperCase() + s.slice(1).toLowerCase();

export const truncateText = (text: string, maxLength = 20): string => {
  if (text.length <= maxLength) {
    return text;
  }
  return `${text.slice(0, maxLength)}...`;
};
