import React, { ReactNode, useCallback, useMemo } from 'react';
import cx from 'clsx';
import { AppShell as AppShellMantine, Avatar, Center, DefaultMantineColor, Flex, Group, Menu, ThemeIcon, UnstyledButton, rem } from '@mantine/core';
import { NavLink as NavLinkItem } from '@mantine/core';
import { IconChevronRight, TablerIconsProps, IconLogout, IconSettings, IconSearch, IconHome, IconChevronDown } from '@tabler/icons-react';
import classes from './AppShell.module.scss';
import styled from 'styled-components';
import { useMatch, useNavigate, useResolvedPath } from 'react-router-dom';
import { useSelector } from 'react-redux';
import KeycloakService from '@core/auth/Keycloak';
import { Spotlight, createSpotlight } from '@mantine/spotlight';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import { ZIndexAppShellHeader } from '@common/constants/ZIndexConstants';
import { KanbanIconButton, KanbanInput, KanbanText } from 'kanban-design-system';
import { CLEAR_WHEN_LOGOUT_KEYS } from '@common/constants/LocalStorageKeyConstants';
import BreadcrumbComponent from '@components/breadcrumb/BreadcrumbComponent';

const AppShellMainStyled = styled(AppShellMantine.Main)`
  display: flex;
  flex-direction: column;
  --kanban-appshell-header-height: calc(var(--app-shell-header-offset, 0px) + var(--app-shell-padding));
  --kanban-appshell-footer-height: calc(var(--app-shell-footer-offset, 0px) + var(--app-shell-padding));
  --kanban-appshell-maxheight-content: calc(100vh - var(--kanban-appshell-header-height) - var(--kanban-appshell-footer-height));
`;

export type RouterType = {
  path?: string;
  name: string;
  icon?: React.ComponentType<TablerIconsProps>;
  children?: RouterType[];
};
type CustomLinkType = RouterType & {
  isRoot: boolean;
  iconColor?: DefaultMantineColor;
  onClick?: () => void;
};
export type AppShellProps = {
  routers: RouterType[];
  headerLinks?: RouterType[];
  children: ReactNode;
};

const CustomLink = (props: CustomLinkType) => {
  const navigate = useNavigate();
  const resolved = useResolvedPath(props.path || '');
  let match = useMatch({ path: resolved.pathname, end: false });
  if (!props.path) {
    match = null;
  }
  const Icons = props.icon;

  return (
    <NavLinkItem
      className={!props.isRoot ? classes.link : ''}
      label={props.name}
      leftSection={
        Icons && (
          <ThemeIcon variant='outline' color={props.iconColor || 'primary'} size={25}>
            {' '}
            <Icons size='1.3rem' stroke={2} />
          </ThemeIcon>
        )
      }
      rightSection={props.children?.length && <IconChevronRight size='0.8rem' stroke={1.5} />}
      active={!!match}
      variant='light'
      onClick={() => {
        if (props.onClick) {
          props.onClick();
          return;
        }
        if (!props.path) {
          return;
        }
        navigate(props.path);
      }}>
      {props.children?.map((item, key) => {
        return <CustomLink key={key} isRoot={false} {...item}></CustomLink>;
      })}
    </NavLinkItem>
  );
};

const CustomHeaderLink = (props: CustomLinkType) => {
  const navigate = useNavigate();
  const resolved = useResolvedPath(props.path || '');
  let match = useMatch({ path: resolved.pathname, end: false });
  if (!props.path) {
    match = null;
  }

  const Icons = props.icon;

  const menuItems = props.children?.map((item) => (
    <Menu.Item
      key={item.path}
      onClick={() => {
        if (props.onClick) {
          props.onClick();
          return;
        }
        if (!item.path) {
          return;
        }
        navigate(item.path);
      }}>
      {item.name}
    </Menu.Item>
  ));

  if (menuItems) {
    return (
      <Menu key={props.name} trigger='hover' transitionProps={{ exitDuration: 0 }} withinPortal>
        <Menu.Target>
          <a
            href={props.path}
            className={`${classes['header-link']} ${match && classes['header-link-active']}`}
            onClick={(event) => {
              event.preventDefault();
            }}>
            {
              <div className={classes['header-link-icon']}>
                {Icons && (
                  <ThemeIcon variant='outline' color={props.iconColor || 'primary'} size={15}>
                    <Icons size='1.3rem' stroke={2} />
                  </ThemeIcon>
                )}
              </div>
            }
            <Center style={{ display: 'inline-block' }}>
              <span className={classes.linkLabel}>{props.name}</span>
              <IconChevronDown size='0.9rem' stroke={1.5} />
            </Center>
          </a>
        </Menu.Target>
        <Menu.Dropdown>{menuItems}</Menu.Dropdown>
      </Menu>
    );
  }

  return (
    <div>
      <a
        key={props.name}
        href={props.path}
        className={`${classes['header-link']} ${match && classes['header-link-active']}`}
        onClick={(event) => {
          event.preventDefault();
          if (props.onClick) {
            props.onClick();
            return;
          }
          if (!props.path) {
            return;
          }
          navigate(props.path);
        }}>
        {
          <div className={classes['header-link-icon']}>
            {Icons && (
              <ThemeIcon variant='outline' color={props.iconColor || 'primary'} size={15}>
                <Icons size='1.3rem' stroke={2} />
              </ThemeIcon>
            )}
          </div>
        }
        <span>{props.name}</span>
      </a>
    </div>
  );
};

export const [searchStore, searchSpotlight] = createSpotlight();

export const AppShell: React.FC<AppShellProps> = (props) => {
  const currentUserState = useSelector(getCurrentUser);
  const currentUser = currentUserState.userInfo;
  const navigate = useNavigate();
  const onLogout = useCallback(() => {
    CLEAR_WHEN_LOGOUT_KEYS.forEach((key) => localStorage.removeItem(key));
    KeycloakService.doLogout();
  }, []);

  const headerLinks = useMemo(() => {
    return props.headerLinks?.map((link, index) => {
      return <CustomHeaderLink isRoot key={index} {...link} />;
    });
  }, [props.headerLinks]);
  return (
    <>
      <Spotlight
        store={searchStore}
        searchProps={{
          placeholder: 'Type to search',
          leftSection: <IconSearch style={{ width: rem(20), height: rem(20) }} stroke={1.5} />,
        }}
        limit={10}
        highlightQuery
        actions={[
          {
            id: 'home',
            label: 'Home',
            description: 'Get to home page',
            onClick: () => console.info('Home'),
            leftSection: <IconHome style={{ width: rem(24), height: rem(24) }} stroke={1.5} />,
          },
        ]}
      />
      <AppShellMantine header={{ height: 50 }} padding='xs'>
        <AppShellMantine.Header zIndex={ZIndexAppShellHeader}>
          <Group h='100%' px='md' justify='space-between' align='center'>
            <Group mr='auto' gap={10} visibleFrom='xs' pl='3.5%'>
              {headerLinks}
              <BreadcrumbComponent />
            </Group>
            <Flex align={'center'} gap={'xs'}>
              <Menu width={260} position='bottom-end' transitionProps={{ transition: 'pop-top-right' }} withinPortal>
                <Menu.Target>
                  <UnstyledButton className={cx(classes.user, { [classes.userActive]: false })} ml={'xs'}>
                    <Group gap={7}>
                      <Avatar src={null} alt={currentUser?.userName || 'No one'} radius='xl' size={20} />
                      <KanbanText fw={500} size='sm' lh={1} mr={3}>
                        {currentUser?.userName || 'No one'}
                      </KanbanText>
                      <IconChevronDown style={{ width: rem(12), height: rem(12) }} stroke={1.5} />
                    </Group>
                  </UnstyledButton>
                </Menu.Target>
                <Menu.Dropdown>
                  <Menu.Item
                    leftSection={<IconSettings style={{ width: rem(16), height: rem(16) }} color={'blue'} stroke={1.5} />}
                    onClick={() => {
                      navigate('admins');
                    }}>
                    Admin Settings
                  </Menu.Item>
                  <Menu.Item leftSection={<IconLogout style={{ width: rem(16), height: rem(16) }} color={'red'} stroke={1.5} />} onClick={onLogout}>
                    Logout
                  </Menu.Item>
                </Menu.Dropdown>
              </Menu>
              {false && (
                <KanbanInput
                  mb={0}
                  placeholder='Type to search'
                  radius={'lg'}
                  onClick={() => {
                    searchSpotlight.open();
                  }}
                  rightSection={
                    <KanbanIconButton variant='transparent'>
                      <IconSearch></IconSearch>
                    </KanbanIconButton>
                  }></KanbanInput>
              )}
            </Flex>
          </Group>
        </AppShellMantine.Header>
        <AppShellMainStyled h={'var(--kanban-appshell-header-height)'} bg='#F3F3F9'>
          {props.children}
        </AppShellMainStyled>
      </AppShellMantine>
    </>
  );
};
export default AppShell;
