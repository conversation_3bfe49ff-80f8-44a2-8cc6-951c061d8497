import React, { useCallback, useState } from 'react';
import { KanbanModal } from 'kanban-design-system';
import { Box } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';

import { ChangeRequestApproval, ChangeRequestApprovalStatus, ChangeRequestApprovalStatusEnum } from '@core/schema/ChangeRequestApproval';
import { ChangeRequestApprovalResultList } from './ChangeRequestApprovalResultList';
import { IconCheck, IconSend } from '@tabler/icons-react';
import { EntityAction } from '@common/constants/EntityActionConstants';
import { Control, useFormContext } from 'react-hook-form';
import { ChangeRequestRoleList } from '@core/schema/ChangeRequestRole';
import { IconX } from '@tabler/icons-react';
import { IconClock } from '@tabler/icons-react';
import { ChangeRequestApprovalMode } from '@common/constants/ChangeRequestApprovalConstants';
import { ChangeFlowNodeTypeEnum } from '@models/ChangeFlowModel';
import { RenderApproval } from '../RenderApproval';
import { RenderCAB } from '../RenderCAB';
import { MappedUser } from '@pages/change/request/role/ChangeRequestDetailRole';
import { ChangeRequestApprovalContext, ChangeRequestApprovalContextType } from '@pages/change/request/role/approval/ChangeRequestApprovalContext';

export const WAITING_APPROVAL: ChangeRequestApproval = {
  id: 0,
  changeRequestRoleUserId: 0,
  overAllStatus: ChangeRequestApprovalStatusEnum.Enum.TOBE_SEND,
  lastApprovalDate: null,
  comment: '',
  createdBy: '',
  createdDate: null,
};
export const statusConfig: Record<
  ChangeRequestApprovalStatus,
  {
    label: string;
    color: string;
    icon: React.ReactNode;
  }
> = {
  ACCEPT: {
    label: 'Approved',
    color: 'green',
    icon: <IconCheck size={14} />,
  },
  REJECT: {
    label: 'Rejected',
    color: 'red',
    icon: <IconX size={14} />,
  },
  PENDING_APPROVAL: {
    label: 'Pending Approval',
    color: 'orange',
    icon: <IconClock size={14} />,
  },
  TOBE_SEND: {
    label: 'To Be Sent',
    color: 'gray',
    icon: <IconSend size={14} />,
  },
};

export type ChangeRequestApprovalProps = {
  onDelete?: () => void;
  onDeleteUser?: () => void;
  control: Control<ChangeRequestRoleList>;
  roleIdx: number;
  cabGroupIdx: number;
  cabGroupUserIdx: number;
  changeFlowNodeId: number;
  mode: EntityAction;
  modeApproval: ChangeRequestApprovalMode;
  workflowIdx: number;
  handleCheckboxChange: (user: MappedUser, checked: boolean) => void;
  refetchChangeRequestRoles: () => void;
};

export const ChangeRequestApprovalComponent: React.FC<ChangeRequestApprovalProps> = ({
  cabGroupIdx,
  cabGroupUserIdx,
  changeFlowNodeId,
  control,
  handleCheckboxChange,
  mode,
  modeApproval,
  onDelete,
  onDeleteUser,
  refetchChangeRequestRoles,
  roleIdx,
  workflowIdx,
}) => {
  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);
  const [currentChangeRequestApproval, setChangeRequestApproval] = useState<ChangeRequestApproval>();

  const { getValues } = useFormContext<ChangeRequestRoleList>();
  const roleType = getValues(`roles.${roleIdx}.changeFlowNode.type`);

  const changeRequestApprovalPath =
    `roles.${roleIdx}.workflows.${workflowIdx}.groups.${cabGroupIdx}.users.${cabGroupUserIdx}.changeRequestApproval` as const;
  const changeRequestApproval = getValues(changeRequestApprovalPath) || WAITING_APPROVAL;
  const handleOpenApprovalResults = useCallback(() => {
    setChangeRequestApproval(changeRequestApproval);
    openModal();
  }, [changeRequestApproval, openModal]);
  const contextValue: ChangeRequestApprovalContextType = {
    cabGroupIdx,
    cabGroupUserIdx,
    changeFlowNodeId,
    control,
    handleCheckboxChange,
    mode,
    modeApproval,
    onDelete,
    onDeleteUser,
    refetchChangeRequestRoles,
    roleIdx,
    workflowIdx,
    handleOpenApprovalResults,
  };
  return (
    <ChangeRequestApprovalContext.Provider value={contextValue}>
      <Box w='100%'>
        <KanbanModal title='Comments' size={'80%'} opened={openedModal} onClose={closeModal}>
          <ChangeRequestApprovalResultList changeRequestApprovalId={currentChangeRequestApproval?.id || 0} />
        </KanbanModal>
        {roleType === ChangeFlowNodeTypeEnum.Enum.CAB ? <RenderCAB /> : <RenderApproval />}
      </Box>
    </ChangeRequestApprovalContext.Provider>
  );
};

ChangeRequestApprovalComponent.whyDidYouRender = true;
export default ChangeRequestApprovalComponent;
