FROM k8sdev.mbbank.com.vn/support/nginx:1.21.4
COPY cicd/tanzu-uat/L01/configmap-k8s/nginx.conf /etc/nginx/conf.d/default.conf
COPY build /usr/share/nginx/html/mbamt-frontend

RUN chown -R 101:101 /var/cache/nginx \
    && chown -R 101:101 /var/log/nginx \
    && chown -R 101:101 /etc/nginx/ \
    && touch /var/run/nginx.pid \
    && chown -R 101:101 /var/run/nginx.pid

# Set the default user. 
VOLUME /var/cache/nginx/ /var/log/nginx /tmp /etc/nginx
