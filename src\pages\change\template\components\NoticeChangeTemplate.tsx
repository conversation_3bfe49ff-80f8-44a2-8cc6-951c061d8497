import React, { useEffect, useRef, useState } from 'react';
import { useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { Box, Flex, Group, Stack, Tooltip } from '@mantine/core';
import { RichTextEditor } from '@mantine/tiptap';
import styles from '@resources/styles/ChangeTemplateBuilder.module.scss';
import { KanbanText } from 'kanban-design-system';
import { ChangeTemplate } from '@core/schema/ChangeTemplate';
import { Control, Controller } from 'react-hook-form';
import { CHANGE_NOTICE_MAX_LENGTH, ROW_HEIGHT } from '@common/constants/ChangeTemplateConstants';
import Underline from '@tiptap/extension-underline';
import { IconAlertCircle } from '@tabler/icons-react';

type NoticeChangeTemplateProps = {
  control: Control<ChangeTemplate>;
};

export const NoticeChangeTemplate: React.FC<NoticeChangeTemplateProps> = ({ control }) => {
  const onChangeRef = useRef<(value: string) => void>();
  const [htmlLength, setHtmlLength] = useState(0);
  const noticeEditor = useEditor({
    extensions: [StarterKit, Underline],
    content: '',
    onUpdate: ({ editor }) => {
      const encoder = new TextEncoder();
      const html = editor.getHTML();
      setHtmlLength(encoder.encode(html).length);
      onChangeRef.current?.(html);
    },
  });
  const showError = htmlLength > CHANGE_NOTICE_MAX_LENGTH;

  useEffect(() => {
    if (noticeEditor && noticeEditor.getHTML() !== control._formValues.notice) {
      noticeEditor.commands.setContent(control._formValues.notice || '', false);
    }
  }, [control._formValues.notice, noticeEditor]);

  return (
    <Stack gap='0' style={{ width: '100%' }} pt='xs' h={ROW_HEIGHT * 6}>
      <Group justify='space-between'>
        <KanbanText>Change notice</KanbanText>
      </Group>

      <Controller
        name='notice'
        control={control}
        render={({ field: controllerField }) => {
          onChangeRef.current = controllerField.onChange;

          return (
            <Flex h='100%' pos='relative'>
              <RichTextEditor
                editor={noticeEditor}
                className={styles.richTextEditor}
                withTypographyStyles={false}
                flex={1}
                styles={{
                  root: {
                    height: '100%',
                    overflowY: 'auto',
                  },
                  content: {
                    height: '100%',
                  },
                  typographyStylesProvider: {
                    height: '100%',
                  },
                }}>
                <RichTextEditor.Toolbar sticky>
                  <RichTextEditor.ControlsGroup>
                    <RichTextEditor.Bold />
                    <RichTextEditor.Italic />
                    <RichTextEditor.Underline />
                    <RichTextEditor.Strikethrough />
                    <RichTextEditor.ClearFormatting />
                  </RichTextEditor.ControlsGroup>
                  <RichTextEditor.ControlsGroup>
                    <RichTextEditor.Blockquote />
                    <RichTextEditor.Hr />
                    <RichTextEditor.BulletList />
                    <RichTextEditor.OrderedList />
                  </RichTextEditor.ControlsGroup>
                </RichTextEditor.Toolbar>
                <RichTextEditor.Content h='100%' pr={10} />
              </RichTextEditor>

              {showError && (
                <Box className={styles.errorRichText}>
                  <Tooltip label={`The content must not exceed ${CHANGE_NOTICE_MAX_LENGTH} bytes.`} color='red' withArrow position='left'>
                    <IconAlertCircle color='red' size={18} />
                  </Tooltip>
                </Box>
              )}
            </Flex>
          );
        }}
      />
    </Stack>
  );
};
