import { ACL_PERMISSIONS } from '@common/constants/AclPermissionConstants';
import { ROUTE_PATH } from '@common/utils/RouterUtils';
import { AclPermissionModel } from '@core/schema/AclPermission';
import {
  IconCircleDot,
  IconSettings,
  IconUser,
  IconBook,
  IconHome,
  IconMessageExclamation,
  IconTemplate,
  IconRocket,
  IconReportSearch,
  IconBell,
  IconTransform,
} from '@tabler/icons-react';
import React from 'react';

export interface SideBarSelectionConfigType {
  name: string;
  path: string;
  icon?: React.ReactNode;
  children?: SideBarSelectionConfigType[];
  requiredPermissions?: AclPermissionModel[];
  allMatchPermissions?: boolean;
}
//ft/role side bar config authorize
export const sideBarItem: SideBarSelectionConfigType[] = [
  { name: 'Home & Dashboard', icon: <IconHome size={20} />, path: ROUTE_PATH.HOME_DASHBOARD },
  {
    name: 'Change',
    icon: <IconCircleDot size={20} />,
    requiredPermissions: [ACL_PERMISSIONS.CHANGE_VIEW],
    path: ROUTE_PATH.CHANGE_REQUEST,
  },
  {
    name: 'Report',
    icon: <IconMessageExclamation size={20} />,
    path: ROUTE_PATH.REPORT,
  },
  {
    name: 'Configuration',
    icon: <IconSettings size={20} />,
    path: ROUTE_PATH.CONFIGURATION,
    requiredPermissions: [ACL_PERMISSIONS.ADMIN_MANAGE_SYSTEM],
    children: [
      {
        name: 'User & Permission',
        icon: <IconUser size={18} />,
        path: ROUTE_PATH.CONFIGURATION_USER,
        children: [
          { name: 'User', path: ROUTE_PATH.CONFIGURATION_USER_USERS },
          { name: 'Group', path: ROUTE_PATH.CONFIGURATION_USER_GROUPS },
          { name: 'Role', path: ROUTE_PATH.CONFIGURATION_USER_ROLES },
        ],
      },
      {
        name: 'Change',
        icon: <IconTransform size={18} />,
        path: ROUTE_PATH.CONFIGURATION_CHANGE,
        children: [
          { name: 'Custom Field', path: ROUTE_PATH.CONFIGURATION_CHANGE_CUSTOM_FIELD },
          { name: 'Change Template', path: ROUTE_PATH.CONFIGURATION_CHANGE_CHANGE_TEMPLATE },
          { name: 'Change Status', path: ROUTE_PATH.CONFIGURATION_CHANGE_STATUS },
          { name: 'Document Catalog', path: ROUTE_PATH.CONFIGURATION_CHANGE_DOCUMENT },
          { name: 'Change Flow', path: ROUTE_PATH.CONFIGURATION_CHANGE_FLOW },
          { name: 'Workflow Node', path: ROUTE_PATH.CONFIGURATION_CHANGE_WORKFLOW_NODE },
        ],
      },
      {
        name: 'Template',
        icon: <IconTemplate size={18} />,
        path: ROUTE_PATH.CONFIGURATION_TEMPLATE,
      },
      {
        name: 'Action',
        icon: <IconRocket size={18} />,
        path: ROUTE_PATH.CONFIGURATION_ACTION,
      },
      {
        name: 'Library',
        icon: <IconBook size={18} />,
        path: ROUTE_PATH.CONFIGURATION_LIBRARY,
      },
      {
        name: 'Audit',
        icon: <IconReportSearch size={18} />,
        path: ROUTE_PATH.CONFIGURATION_AUDIT,
      },
      {
        name: 'Notifications',
        icon: <IconBell size={18} />,
        path: ROUTE_PATH.CONFIGURATION_NOTIFICATIONS,
        children: [{ name: 'Email Template', path: ROUTE_PATH.CONFIGURATION_NOTIFICATIONS_EMAIL_TEMPLATES }],
      },
    ],
  },
];
