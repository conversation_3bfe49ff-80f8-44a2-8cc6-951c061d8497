import { ActionIcon, Anchor, Flex, Group, Table } from '@mantine/core';
import {
  ChangeRequestDocumentActionStatusType,
  ChangeRequestDocumentEnums,
  ChangeRequestDocumentForm,
  ChangeRequestDocumentGroupFormWrapper,
  ChangeRequestDocumentRole,
} from '@models/ChangeRequestDocumentGroupModel';
import React, { useEffect } from 'react';
import classes from './CommonTable.module.scss';
import { useFieldArray, useFormContext, Controller, useWatch } from 'react-hook-form';
import { IconCirclePlus, IconFile, IconLink, IconTrash, IconX } from '@tabler/icons-react';
import UserSelectComponent from '@pages/change/request/document/components/selectBox/UserSelectComponent';
import { LeaderCell } from './LeaderCell';
import { UploaderFilePopover } from './UploaderFilePopover';
import UserSingleSelectComponent from '../selectBox/UserSingleSelectComponent';
import { renderStatusPill } from '@pages/change/request/document/utils/TableUtils';
import { DocumentApproverLevelEnum } from '@common/constants/ChangeDocumentConstants';
import { useClipboard } from '@mantine/hooks';

const emptyDocument: ChangeRequestDocumentForm = {
  documentName: '',
  documentUrl: undefined,
  tempId: undefined,
  type: undefined,
  file: undefined,
  approvers: [],
};

interface OwnerRowProps {
  ownerIndex: number;
  removeOwner: () => void;
  canEditCab: boolean;
  userName: string;
  totalDocumentRows: number;
  cabName: `items.${number}.cabs`;
  ownerName: `items.${number}.owners.${number}`;
  isLeaderLevel2Visible: boolean;
  openedPopoverId: string | null;
  ownerLength: number;
  setOpenedPopoverId: (id: string | null) => void;
  hasMatchingRole: (targetRole: ChangeRequestDocumentRole) => boolean;
}

export const OwnerRow = React.memo(function OwnerRow({
  cabName,
  canEditCab,
  hasMatchingRole,
  isLeaderLevel2Visible,
  openedPopoverId,
  ownerIndex,
  ownerLength,
  ownerName,
  removeOwner,
  setOpenedPopoverId,
  totalDocumentRows,
  userName,
}: OwnerRowProps) {
  const { control, setValue } = useFormContext<ChangeRequestDocumentGroupFormWrapper>();
  const owner = useWatch({ control, name: ownerName });
  const clipboard = useClipboard();

  const canEditRow =
    ChangeRequestDocumentEnums.ActionStatus.Enum.SENT_TO_OWNER === owner?.status &&
    userName === owner?.username &&
    hasMatchingRole(ChangeRequestDocumentEnums.Role.enum.OWNER);

  const {
    append,
    fields: documentFields,
    remove: removeDoc,
  } = useFieldArray({
    control,
    name: `${ownerName}.documents`,
  });

  const isFirstOwnerRow = ownerIndex === 0;

  useEffect(() => {
    if (canEditRow && documentFields.length === 0) {
      setValue(cabName, []);
      append({ documentName: '', approvers: [] });
    }
  }, [canEditRow, documentFields.length, append, setValue, cabName]);

  const handleCopyApprovers = (users: { username: string; fullName?: string | null }[]) => {
    if (!users?.length) {
      return;
    }

    const text = users.map((u) => u.username).join('\t');
    clipboard.copy(text);
  };

  const appendEmptyDocument = () => append({ documentName: '', approvers: [] });

  const handleDeleteDocument = (docIndex: number) => {
    if (documentFields.length === 1) {
      removeOwner();
      if (userName === owner?.username) {
        setValue(cabName, null);
      }
    } else {
      removeDoc(docIndex);
    }
  };

  const renderUsernameCell = (rowSpan: number) => (
    <Controller
      control={control}
      name={`${ownerName}.username`}
      render={({ field, fieldState }) => (
        <Table.Td className={classes.cell} style={{ verticalAlign: 'top' }} rowSpan={rowSpan}>
          <UserSingleSelectComponent
            value={{ value: field.value, label: field.value }}
            onChange={(val) => field.onChange(val || '')}
            error={fieldState.error?.message}
          />
        </Table.Td>
      )}
    />
  );

  const renderDocumentCell = (docIndex: number, value: ChangeRequestDocumentForm, onChange: (val: ChangeRequestDocumentForm) => void) => (
    <Table.Td className={classes.cell}>
      {value?.documentName ? (
        <Group gap={4}>
          {value.documentUrl ? <IconLink size={14} /> : <IconFile size={14} />}
          <Anchor fw='500' size='sm' href={value.documentUrl ?? undefined} target='_blank' rel='noopener noreferrer'>
            {value.documentName}
          </Anchor>
          {canEditRow && (
            <ActionIcon
              variant='subtle'
              onClick={() => onChange({ ...value, tempId: undefined, type: undefined, file: undefined, documentName: '', documentUrl: '' })}>
              <IconX size={14} />
            </ActionIcon>
          )}
        </Group>
      ) : (
        <UploaderFilePopover
          disableUpload={!canEditRow}
          opened={openedPopoverId === `${ownerName}.documents.${docIndex}`}
          setOpened={(open) => setOpenedPopoverId(open ? `${ownerName}.documents.${docIndex}` : null)}
          nameDocument={`${ownerName}.documents.${docIndex}`}
        />
      )}
    </Table.Td>
  );

  const renderLeaderCells = (docIndex: number) =>
    [DocumentApproverLevelEnum.LEVEL_1, ...(isLeaderLevel2Visible ? [DocumentApproverLevelEnum.LEVEL_2] : [])].map((level) => (
      <Table.Td key={level} className={classes.cell}>
        <LeaderCell canEditRow={canEditRow} name={`${ownerName}.documents.${docIndex}.approvers`} level={level} onCopy={handleCopyApprovers} />
      </Table.Td>
    ));

  const renderCabCell = (rowSpan: number) =>
    isFirstOwnerRow && (
      <Table.Td rowSpan={rowSpan} className={classes.cell} style={{ verticalAlign: 'top' }}>
        <Controller
          control={control}
          name={cabName}
          render={({ field, fieldState }) => (
            <UserSelectComponent
              disable={!canEditCab}
              value={(field.value ?? []).map((u) => ({ id: u.username, name: u.displayName }))}
              onChange={(val) => field.onChange(val.map((v) => ({ username: v.id, displayName: v.name })))}
              error={fieldState.error?.message}
              isViewMode={false}
              onBlur={field.onBlur}
            />
          )}
        />
      </Table.Td>
    );

  return documentFields.length > 0 ? (
    documentFields.map((doc, docIndex) => (
      <Table.Tr key={doc.id}>
        {docIndex === 0 && renderUsernameCell(documentFields.length)}
        <Controller name={`${ownerName}.documents.${docIndex}`} render={({ field }) => renderDocumentCell(docIndex, field.value, field.onChange)} />
        {renderLeaderCells(docIndex)}
        {isFirstOwnerRow && docIndex === 0 && renderCabCell(totalDocumentRows)}
        <Table.Td className={classes.cell}>{owner.status && renderStatusPill(owner.status as ChangeRequestDocumentActionStatusType)}</Table.Td>
        <Table.Td className={classes.cell}></Table.Td>
        <Table.Td className={classes.cell}>
          <Flex align='center' gap='md'>
            {docIndex === documentFields.length - 1 && (
              <ActionIcon title='Add row document' variant='subtle' onClick={appendEmptyDocument}>
                <IconCirclePlus size={20} />
              </ActionIcon>
            )}
            <ActionIcon title='Delete row document' variant='subtle' color='red' onClick={() => handleDeleteDocument(docIndex)}>
              <IconTrash size={20} />
            </ActionIcon>
          </Flex>
        </Table.Td>
      </Table.Tr>
    ))
  ) : (
    <Table.Tr>
      {renderUsernameCell(1)}
      {canEditRow ? (
        <Controller name={`${ownerName}.documents.0`} render={({ field }) => renderDocumentCell(0, field.value, field.onChange)} />
      ) : (
        renderDocumentCell(0, emptyDocument, () => {})
      )}
      {renderLeaderCells(0)}
      {isFirstOwnerRow && renderCabCell(totalDocumentRows)}
      <Table.Td className={classes.cell}>{owner?.status && renderStatusPill(owner.status as ChangeRequestDocumentActionStatusType)}</Table.Td>
      <Table.Td className={classes.cell}></Table.Td>
      <Table.Td className={classes.cell}>
        <Flex align='center' gap='md'>
          <ActionIcon title='Add row document' variant='subtle' disabled={!canEditRow} onClick={appendEmptyDocument}>
            <IconCirclePlus size={20} />
          </ActionIcon>
          {ownerLength > 1 && (
            <ActionIcon title='Delete row document' color='red' variant='subtle' onClick={removeOwner}>
              <IconTrash size={20} />
            </ActionIcon>
          )}
        </Flex>
      </Table.Td>
    </Table.Tr>
  );
});
