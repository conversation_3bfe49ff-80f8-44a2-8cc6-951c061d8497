import { Box, Flex, Grid, ScrollArea } from '@mantine/core';
import { KanbanCheckbox, KanbanInput } from 'kanban-design-system';
import React, { useCallback, useMemo, useState } from 'react';
import { IconSearch } from '@tabler/icons-react';
import { AclPermissionModel } from '@core/schema/AclPermission';
import { getAclPermissionId } from '@common/utils/AclPermissionUtils';
export type ListPermissionProps = {
  permissions: AclPermissionModel[];
  setPermissions: React.Dispatch<React.SetStateAction<AclPermissionModel[]>>;
};
export const ViewListPermission: React.FC<ListPermissionProps> = ({ permissions, setPermissions }) => {
  const [search, setSearch] = useState('');
  const [selectAll, setSelectAll] = useState(permissions.every((item) => item.isChecked));
  const handleCheckboxChange = useCallback(
    (changedPermission: AclPermissionModel) => {
      const newPermissions = permissions.map((item) => {
        if (getAclPermissionId(item) === getAclPermissionId(changedPermission)) {
          return { ...item, isChecked: !item.isChecked };
        } else {
          return item;
        }
      });
      setPermissions(newPermissions);
      const allChecked = newPermissions.every((item) => item.isChecked);
      setSelectAll(allChecked);
    },
    [permissions, setPermissions],
  );

  const filteredPermissions = useMemo(
    () =>
      permissions.filter(
        (permission) =>
          permission.permission.toLowerCase().includes(search.toLowerCase()) || permission.description?.toLowerCase().includes(search.toLowerCase()),
      ),
    [permissions, search],
  );
  const handleSelectAllChange = useCallback(() => {
    const newSelectAllState = !selectAll;
    setSelectAll(newSelectAllState);

    // Only update the filtered permissions
    const updatedPermissions = permissions.map((item) => {
      const isFiltered = filteredPermissions.some((filteredItem) => getAclPermissionId(filteredItem) === getAclPermissionId(item));
      // Update only the filtered items
      return isFiltered ? { ...item, isChecked: newSelectAllState } : item;
    });

    setPermissions(updatedPermissions);
  }, [filteredPermissions, permissions, selectAll, setPermissions]);

  return (
    <Box p={20}>
      <Flex justify='space-between' mb={'xs'}>
        <KanbanCheckbox checked={selectAll} label={'Select all'} onChange={handleSelectAllChange} />
        <KanbanInput
          w={'30%'}
          placeholder='Search here'
          value={search}
          maxLength={255}
          onChange={(e) => {
            const value = e.target.value;
            setSearch(value);
          }}
          leftSection={<IconSearch />}
        />
      </Flex>
      <ScrollArea h={600} scrollbars='y'>
        <Grid>
          {filteredPermissions.map((it) => (
            <Grid.Col span={{ base: 12, md: 12, lg: 6, sm: 12, xl: 3, xs: 12 }} key={getAclPermissionId(it)}>
              <KanbanCheckbox
                checked={it.isChecked || false}
                value={getAclPermissionId(it)}
                //TODO: tam thoi de la permission : sau nay se doi lai khi permission chi la CREATE / UPDATE/ ...
                label={it.permission}
                description={it.description}
                onChange={() => handleCheckboxChange(it)}
              />
            </Grid.Col>
          ))}
        </Grid>
      </ScrollArea>
    </Box>
  );
};

export default ViewListPermission;
