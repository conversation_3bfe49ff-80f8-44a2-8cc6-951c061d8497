import { AdvancedFilterMappingType, getDefaultTableAffected, KanbanCheckbox, KanbanText, KanbanTitle } from 'kanban-design-system';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanTable, KanbanTableProps, TableAffactedSafeType } from 'kanban-design-system';
import { renderDateTime } from 'kanban-design-system';
import { Box, Flex } from '@mantine/core';
import equal from 'fast-deep-equal';
import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { GroupApi } from '@api/GroupApi';

import useFetch from '@core/hooks/useFetch';
import customStyled from '@resources/styles/Common.module.scss';

import { GroupRoleModel } from '@models/GroupRoleUserModel';
import { FilterTextInput } from '@components/AmtFilterTextInput';
import { ItemCheckComponentProps } from '@core/schema/Common';

export type GroupRolePopupFilter = Partial<GroupRoleModel>;

export const getKeyRoleInGroup = (roleId: number) => {
  return `${roleId};`;
};

const initOrUpdatedFilterPayloads = (prev?: TableAffactedSafeType, initFilters?: GroupRolePopupFilter) => {
  const prevFilter = prev || getDefaultTableAffected();
  if (initFilters) {
    const filterObj: AdvancedFilterMappingType<GroupRolePopupFilter> = {
      ['roleName']: {
        filterOption: 'contains',
        value: {
          fromValue: initFilters.roleName,
        },
      },
    };
    const advancedFilterMapping = { ...prevFilter.advancedFilterMapping, ...filterObj };
    return { ...prevFilter, advancedFilterMapping: { ...advancedFilterMapping } };
  }
  return prevFilter;
};
export type SearchObj = { field: string; value: { fromValue?: any; toValue?: any } };
export const GroupRoleTableComponent: React.FC<ItemCheckComponentProps<GroupRoleModel>> = ({
  parentId: groupId,
  selecteds: selecteds,
  setOldPagings,
  setSelecteds,
  setToDeletes,
  setToInserts,
  setUpdatePagings,
  toDeletes: toDeletes,
}) => {
  const [totalRecords, setTotalRecords] = useState(0);
  const [listData, setListData] = useState<GroupRoleModel[]>([]);
  const [tableAffected, setTableAffectedChange] = useState<TableAffactedSafeType>();
  const [inputFilters, setInputFilters] = useState<GroupRolePopupFilter>({});
  const [onBlurSearchFilter, setOnBlurSearchFilter] = useState<GroupRolePopupFilter>({});

  //table data
  const { data: tableDataResponse } = useFetch(
    GroupApi.findPagingRolesByGroupIdAndOtherRoles(groupId, initOrUpdatedFilterPayloads(tableAffected, onBlurSearchFilter)),
    { enabled: !!tableAffected },
  );
  useEffect(() => {
    if (tableDataResponse?.data?.content) {
      const results = tableDataResponse.data.content;
      setListData(results);
      setTotalRecords(tableDataResponse.data.totalElements);
      setSelecteds((prev) => {
        const updateMap = { ...prev };
        [
          ...results.filter((it) => !(getKeyRoleInGroup(it.roleId) in updateMap) && !(getKeyRoleInGroup(it.roleId) in toDeletes) && it.groupSelected),
        ].forEach((item) => {
          updateMap[getKeyRoleInGroup(item.roleId)] = item;
        });
        return updateMap;
      });

      if (setOldPagings) {
        setOldPagings((prev) => {
          const updateMap = { ...prev };
          [...results.filter((it) => !(getKeyRoleInGroup(it.roleId) in updateMap) && it.groupSelected)].forEach((item) => {
            updateMap[getKeyRoleInGroup(item.roleId)] = item;
          });
          return updateMap;
        });
      }
    }
  }, [tableDataResponse?.data, setSelecteds, toDeletes, groupId, setOldPagings, setUpdatePagings]);

  const handleSearch = useCallback(() => {
    const filter = { ...inputFilters };
    setOnBlurSearchFilter(filter);
    setTableAffectedChange((prev) => initOrUpdatedFilterPayloads(prev, filter));
  }, [inputFilters]);
  const handleCheckAndUnCheck = useCallback(
    (rowData: GroupRoleModel, checked: boolean) => {
      const key = getKeyRoleInGroup(rowData.roleId);
      setSelecteds((prev) => {
        const updateMap = { ...prev };
        if (checked) {
          updateMap[key] = rowData;
        } else {
          delete updateMap[key];
        }

        return updateMap;
      });

      //add /remove item in insert list
      setToInserts((prev) => {
        const updateMap = { ...prev };
        if (checked) {
          updateMap[key] = rowData;
        } else {
          delete updateMap[key];
        }
        return updateMap;
      });
      // add /remove item in delete list
      setToDeletes((prev) => {
        const updateMap = { ...prev };
        if (checked) {
          delete updateMap[key];
        } else {
          updateMap[key] = rowData;
        }
        return updateMap;
      });
    },
    [setSelecteds, setToDeletes, setToInserts],
  );
  const tableProps: KanbanTableProps<GroupRoleModel> = useMemo(() => {
    const tblProps: KanbanTableProps<GroupRoleModel> = {
      title: '',
      columns: [
        {
          name: 'groupSelected',
          title: '',
          width: '5%',
          customRender: (_, row: GroupRoleModel) => {
            return (
              <KanbanCheckbox
                checked={getKeyRoleInGroup(row.roleId) in selecteds}
                onChange={(e) => handleCheckAndUnCheck(row, e.target.checked)}></KanbanCheckbox>
            );
          },
        },
        {
          name: 'index',
          title: 'No.',
          width: '5%',
          customRender: (_, rowData) => {
            return <KanbanText>{listData.indexOf(rowData) + 1 + ((tableAffected?.page || 0) - 1) * (tableAffected?.rowsPerPage || 0)}</KanbanText>;
          },
        },
        {
          name: 'id',
          title: 'ID',
          hidden: true,
        },
        {
          name: 'roleName',
          title: 'Rolename',
        },
        {
          name: 'description',
          title: 'Description',
        },
        {
          name: 'modifiedBy',
          title: 'Modified By',
          hidden: true,
        },
        {
          name: 'modifiedDate',
          title: 'Modified date',
          hidden: true,
          customRender: renderDateTime,
        },
        {
          name: 'createdBy',
          title: 'Created By',
          hidden: true,
        },
        {
          name: 'createdDate',
          title: 'Created date',
          customRender: renderDateTime,
          hidden: true,
        },
      ],
      data: listData,

      serverside: {
        totalRows: totalRecords,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffected, dataSet)) {
            setTableAffectedChange((prev) => (prev ? { ...dataSet, advancedFilterMapping: prev.advancedFilterMapping } : { ...dataSet }));
          }
        },
      },
      pagination: {
        enable: true,
      },
    };
    return tblProps;
  }, [listData, totalRecords, selecteds, handleCheckAndUnCheck, tableAffected]);

  return (
    <Box>
      <HeaderTitleComponent
        title=''
        leftSection={
          <Flex dir='row' gap='xs' align={'center'}>
            <KanbanTitle fz={'h4'}>Roles</KanbanTitle>
          </Flex>
        }
      />
      <Flex dir='row' gap='xs' mb='xs'>
        <FilterTextInput
          placeholder='Rolename'
          value={inputFilters.roleName || ''}
          onBlur={handleSearch}
          onChange={(val) => {
            setInputFilters((prev) => ({ ...prev, roleName: val.target.value }));
          }}
        />
      </Flex>
      <Box className={customStyled.elementBorder}>
        <KanbanTable {...tableProps} title='' />
      </Box>
    </Box>
  );
};

export default GroupRoleTableComponent;
