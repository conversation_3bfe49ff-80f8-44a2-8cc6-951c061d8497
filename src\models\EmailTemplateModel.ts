import { AuditSchema } from '@core/schema/Common';
import { z } from 'zod';
import { INPUT_REQUIRE } from '@core/message/MesageConstant';
import { JsonKeyConstants } from '@common/constants/JsonKeyConstants';
import { EmailTemplateTypeEnum } from '@common/constants/EmailTemplateConstants';

export type MentionNode = {
  type: JsonKeyConstants.MENTION_TYPE;
  attrs: { id: string; label?: string; deleted?: boolean; mentionSuggestionChar?: string };
};
export type TextNode = { type: JsonKeyConstants.TEXT_TYPE; text: string };

export type ShortRichNode = MentionNode | TextNode;

export type ParagraphNode = {
  type: JsonKeyConstants.PARAGRAPH_TYPE;
  content?: ShortRichNode[];
};

export type DocumentNode = ParagraphNode[];

export const EmailTemplateDetailSchema = z
  .object({
    id: z.number(),
    name: z.string().trim().nonempty(INPUT_REQUIRE),
    subject: z
      .string()
      .trim()
      .nonempty(INPUT_REQUIRE)
      .transform((val) => {
        try {
          return JSON.parse(val) as DocumentNode;
        } catch {
          return val;
        }
      }),
    content: z
      .string()
      .trim()
      .nonempty(INPUT_REQUIRE)
      .transform((val) => {
        try {
          return JSON.parse(val) as DocumentNode;
        } catch {
          return val;
        }
      }),
    type: EmailTemplateTypeEnum.nullish(),
  })
  .merge(AuditSchema);

export type EmailTemplateDetailModel = z.infer<typeof EmailTemplateDetailSchema>;
