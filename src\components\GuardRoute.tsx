import React from 'react';
import { useSelector } from 'react-redux';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import ForbiddenPage from '@pages/base/ForbiddenPage';
import { AclPermission } from '@core/schema/AclPermission';
import { ProtectedRoute } from '@core/auth/hocs/ProtectedRoute';

//ft/role router authorize
export type GuardRouteType = {
  requirePermissions: AclPermission[];
  children: React.ReactNode;
  allMatchPermissions?: boolean;
};

const GuardRoute: React.FC<GuardRouteType> = (props: GuardRouteType) => {
  const currentUser = useSelector(getCurrentUser).userInfo;
  return (
    <ProtectedRoute
      errorElement={<ForbiddenPage />}
      requirePermissions={props.requirePermissions}
      userPermissions={currentUser?.aclPermissions || []}
      allMatchPermissions={props.allMatchPermissions}>
      {props.children}
    </ProtectedRoute>
  );
};

export default GuardRoute;
