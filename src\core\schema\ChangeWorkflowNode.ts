import { ChangeApplicationTypeEnum } from '@common/constants/ChangeApplicationConstants';
import { z } from 'zod';

// Đ<PERSON>nh nghĩa schema cho ChangeWorkflowNode
export const ChangeWorkflowNodeSchema = z.object({
  id: z.number(),
  nodeId: z.string().optional(),
  nodeName: z.string(),
  description: z.string().nullish(),
  application: z.nativeEnum(ChangeApplicationTypeEnum).optional(),
  authentication: z.string().nullish(),
  config: z.string().nullish(),
  createdDate: z.string().nullish(),
  createdBy: z.string().nullish(),
  modifiedBy: z.string().nullish(),
  modifiedDate: z.string().nullish(),
});

export type ChangeWorkflowNode = z.infer<typeof ChangeWorkflowNodeSchema>;

export const ChangeWorkflowSchema = z.object({
  id: z.number().optional(),
  name: z.string().optional(),
  changeId: z.number().optional(),
  workflowData: z.string().optional(),
  createdDate: z.string().nullish(),
  createdBy: z.string().nullish(),
  modifiedBy: z.string().nullish(),
  modifiedDate: z.string().nullish(),
});

export type ChangeWorkflow = z.infer<typeof ChangeWorkflowSchema>;
