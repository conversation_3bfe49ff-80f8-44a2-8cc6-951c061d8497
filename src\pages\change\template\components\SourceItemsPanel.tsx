import React, { useCallback, useRef } from 'react';
import { IconArrowRight, IconSearch } from '@tabler/icons-react';
import { Paper, ScrollArea, Stack, TextInput, Title, Flex, Group, Tooltip, ActionIcon, MantineTheme, useMantineTheme } from '@mantine/core';
import { KanbanButton, KanbanText, KanbanTooltip } from 'kanban-design-system';
import { TemplateCustomFieldTypeEnum, TemplateFieldItem } from '@core/schema/ChangeTemplate';
import { FetchNextPageOptions } from '@tanstack/react-query';
import type { TablerIconKeys } from '@common/utils/IconsUtils';
import { GetIcon } from './GetIcon';

import styles from '@resources/styles/ChangeTemplateBuilder.module.scss';
import { CUSTOM_FIELD_MAX_LENGTH } from '@common/constants/ValidationConstant';

export interface SourceItemsPanelProps {
  filteredSourceItems: TemplateFieldItem[];
  searchTerm: string;
  onSearchChange: (value: string) => void;
  onDoubleClickItem: (sourceItem: TemplateFieldItem) => void;
  onDragStart?: (sourceItem: TemplateFieldItem) => void;
  onDragEnd?: () => void;
  fetchNextPage: ((opts?: FetchNextPageOptions | undefined) => void) & {
    flush: () => void;
  };

  hasNextPage?: boolean;
  isFetchingNextPage?: boolean;
}
export const getTypeColor = (item: TemplateFieldItem, theme: MantineTheme) => {
  switch (item.customFieldType) {
    case TemplateCustomFieldTypeEnum.Enum.SINGLE_LINE:
    case TemplateCustomFieldTypeEnum.Enum.NUMBER:
      return theme.colors.blue[6];
    case TemplateCustomFieldTypeEnum.Enum.DATE:
      return theme.colors.teal[6];
    case TemplateCustomFieldTypeEnum.Enum.PICKLIST:
      return theme.colors.red[6];
    case TemplateCustomFieldTypeEnum.Enum.RICH_TEXT:
    case TemplateCustomFieldTypeEnum.Enum.MULTI_LINE:
      return theme.colors.violet[6];
    case TemplateCustomFieldTypeEnum.Enum.BREAK:
      return theme.colors.orange[6];
    default:
      return theme.colors.gray[6];
  }
};

const getIconName = (item: TemplateFieldItem): TablerIconKeys => {
  // Get icon by type
  switch (item.customFieldType) {
    case TemplateCustomFieldTypeEnum.Enum.SINGLE_LINE:
      return 'IconLetterCase';
    case TemplateCustomFieldTypeEnum.Enum.RICH_TEXT:
      return 'IconTextSize';
    case TemplateCustomFieldTypeEnum.Enum.MULTI_LINE:
      return 'IconPilcrow'; // Icon for text field
    case TemplateCustomFieldTypeEnum.Enum.NUMBER:
      return 'IconNumbers'; // Icon for number field
    case TemplateCustomFieldTypeEnum.Enum.DATE:
      return 'IconCalendar'; // Icon for date field
    case TemplateCustomFieldTypeEnum.Enum.PICKLIST:
      return 'IconSelect'; // Icon for select field
    case TemplateCustomFieldTypeEnum.Enum.BREAK:
      return 'IconTextSize'; // Icon for break field
    default:
      return 'IconLetterCase'; // Default icon
  }
};
const RenderFieldItem = ({
  item,
  onDoubleClickItem,
  onDragEnd,
  onDragStart,
}: {
  item: TemplateFieldItem;
  onDragStart?: (sourceItem: TemplateFieldItem) => void;
  onDragEnd?: () => void;
  onDoubleClickItem: (sourceItem: TemplateFieldItem) => void;
}) => {
  const theme = useMantineTheme();
  const fieldColor = getTypeColor(item, theme);

  const handleDragStart = useCallback(
    (event: React.DragEvent, item: TemplateFieldItem) => {
      const target = event.currentTarget as HTMLElement;
      if (target) {
        const dragImage = target.cloneNode(true) as HTMLElement;
        const rect = target.getBoundingClientRect();
        dragImage.style.width = `${rect.width}px`;
        dragImage.style.height = `${rect.height}px`;
        const computedStyle = window.getComputedStyle(target);
        for (let i = 0; i < computedStyle.length; i++) {
          const key = computedStyle[i];
          dragImage.style.setProperty(key, computedStyle.getPropertyValue(key), computedStyle.getPropertyPriority(key));
        }
        dragImage.style.position = 'absolute';
        dragImage.style.top = '-9999px';
        dragImage.style.left = '-9999px';
        dragImage.style.pointerEvents = 'none';
        dragImage.style.opacity = '0.8';
        document.body.appendChild(dragImage);
        event.dataTransfer.effectAllowed = 'move';
        event.dataTransfer.dropEffect = 'move';
        event.dataTransfer.setDragImage(dragImage, 10, 10);
        setTimeout(() => document.body.removeChild(dragImage), 0);
      }
      onDragStart?.(item);
    },
    [onDragStart],
  );

  return (
    <Paper
      key={item.customFieldId}
      withBorder
      p='sm'
      style={{
        cursor: 'pointer',
        paddingRight: 'xs',
        backgroundColor: item.customFieldType === TemplateCustomFieldTypeEnum.Enum.BREAK ? 'var(--mantine-color-blue-1)' : '',
      }}
      onDoubleClick={() => onDoubleClickItem(item)}
      draggable
      onDragStart={(e) => handleDragStart(e, item)}
      onDragEnd={onDragEnd}>
      <Group justify='space-between' wrap='nowrap'>
        <Flex align='center'>
          <GetIcon name={getIconName(item)} color={fieldColor} />

          <KanbanTooltip label={item.name} maw={'14%'} multiline>
            <KanbanText className={styles.fieldName} miw={150} ml={'xs'}>
              {item.customFieldType === TemplateCustomFieldTypeEnum.Enum.BREAK ? '' : item.name}
            </KanbanText>
          </KanbanTooltip>
        </Flex>
        <Group gap={4} wrap='nowrap' justify='flex-end'>
          <Tooltip label='Add to field' position='left' withArrow>
            <ActionIcon variant='subtle' color='var(--mantine-color-primary-5)' size='sm' onClick={() => onDoubleClickItem(item)}>
              <IconArrowRight size='1rem' />
            </ActionIcon>
          </Tooltip>
        </Group>
      </Group>
    </Paper>
  );
};

export const SourceItemsPanel: React.FC<SourceItemsPanelProps> = ({
  fetchNextPage,
  filteredSourceItems,
  hasNextPage,
  isFetchingNextPage,
  onDoubleClickItem,
  onDragEnd,
  onDragStart,
  onSearchChange,
  searchTerm,
}) => {
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  const handleScroll = (x: number, y: number) => {
    const scrollArea = scrollAreaRef.current;

    if (scrollArea) {
      const { clientHeight, scrollHeight } = scrollArea;
      const isAtBottom = y + clientHeight >= scrollHeight - 50;
      const isScrollable = scrollHeight > clientHeight;

      if (isAtBottom && isScrollable && hasNextPage && !isFetchingNextPage) {
        fetchNextPage();
      }
    }
  };

  return (
    <Paper
      shadow='xs'
      p='md'
      withBorder
      h='calc(100vh - 90px)'
      style={{
        position: 'sticky',
        top: 30,
        zIndex: 10,
        backgroundColor: 'white',
        display: 'flex',
        flexDirection: 'column',
      }}>
      <div style={{ backgroundColor: 'white' }}>
        <Flex justify={'flex-start'} gap={'xs'} align={'center'} mb={'xs'}>
          <Title order={4}>Available Fields</Title>
          <KanbanButton size='compact-xs' variant='filled'>
            Add +
          </KanbanButton>
        </Flex>
        <TextInput
          placeholder='Search fields...'
          leftSection={<IconSearch size='1rem' />}
          value={searchTerm}
          maxLength={CUSTOM_FIELD_MAX_LENGTH}
          onChange={(event) => onSearchChange(event.currentTarget.value)}
        />
      </div>
      <ScrollArea
        pt={'xs'}
        h={'100%'}
        style={{ flex: 1 }}
        scrollbarSize={8}
        viewportRef={scrollAreaRef}
        onScrollPositionChange={(position) => handleScroll(position.x, position.y)}>
        <Stack gap='sm' pr='xs'>
          {filteredSourceItems.map((it) => (
            <RenderFieldItem item={it} key={it.customFieldId} onDoubleClickItem={onDoubleClickItem} onDragEnd={onDragEnd} onDragStart={onDragStart} />
          ))}
        </Stack>
      </ScrollArea>
    </Paper>
  );
};
