import React, { useEffect, useState } from 'react';
import { KanbanButton, KanbanIconButton, KanbanText, KanbanTooltip, SortOrder } from 'kanban-design-system';
import { Box, Flex, Pill } from '@mantine/core';
import useFetch from '@core/hooks/useFetch';
import { GroupRole, GroupUser } from '@core/schema/Group';
import { useDisclosure } from '@mantine/hooks';
import { GroupRoleTableComponent } from './component/GroupRoleTableComponent';
import { LOAD_ITEM_MAX_LENGTH, LOAD_ITEM_ROLE_GROUP_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { ItemCheck } from '@core/schema/Common';
import { IconEye, IconReload } from '@tabler/icons-react';
import { UserApi } from '@api/UserApi';
import AmtModal from '@components/AmtModal';

interface GroupRoleListWithPaginationProps {
  username?: string;
  toInsert?: ItemCheck<GroupUser>;
  toDelete?: ItemCheck<GroupUser>;
}

export const RoleGroupListWithPagination: React.FC<GroupRoleListWithPaginationProps> = ({ toDelete, toInsert, username }) => {
  const [groups, setGroups] = useState<GroupRole[]>([]);
  const [page, setPage] = useState(1);

  const [openedRoles, { close: closeRoles, open: openRoles }] = useDisclosure(false);

  const { data, isLoading } = useFetch(
    UserApi.findAllGroupRolesByUsername(username || '0', {
      page,
      rowsPerPage: LOAD_ITEM_MAX_LENGTH,
      sortOrder: SortOrder.DESC,
      search: '',
    }),
    { enabled: !!username },
  );

  const { data: roleNew } = useFetch(
    UserApi.findAllRolesByGroupIdIn(Object.values(toInsert || [])?.map((e) => e.groupId) || [], {
      page: 1,
      rowsPerPage: LOAD_ITEM_ROLE_GROUP_MAX_LENGTH,
      sortOrder: SortOrder.DESC,
      search: '',
    }),
    { enabled: toInsert && Object.keys(toInsert).length > 0 },
  );

  useEffect(() => {
    if (data?.data?.content) {
      if (page === 1) {
        setGroups(data.data.content);
      } else {
        setGroups((prev) => [...prev, ...(data?.data?.content || [])]);
      }
    }
  }, [data, page]);

  const loadMore = () => {
    setPage((prev) => prev + 1);
  };

  const handleCancelChangeRoles = () => {
    closeRoles();
  };

  return (
    <Box mt='xl'>
      <Flex direction={'row'} align={'center'} gap={'xs'}>
        <KanbanText fw={'500'}>Roles granted Group</KanbanText>
        <KanbanIconButton size='sm' variant='light' onClick={openRoles}>
          <IconEye />
        </KanbanIconButton>
        <AmtModal size={'80%'} opened={openedRoles} onClose={handleCancelChangeRoles}>
          <GroupRoleTableComponent toDeletes={Object.values(toDelete || [])} toInserts={Object.values(toInsert || [])} username={username} />
        </AmtModal>
      </Flex>
      <Flex wrap={'wrap'} gap={'xs'} mt='xs'>
        {roleNew?.data?.content.map((group) => (
          <KanbanTooltip label={group.groupName} key={group.id} withArrow>
            <Pill bg='var(--mantine-color-green-1)' size='md'>
              {group.roleName}
            </Pill>
          </KanbanTooltip>
        ))}

        {groups
          .filter((group) => !Object.values(toDelete || [])?.some((del) => del.groupId === group.groupId))
          .map((group) => (
            <KanbanTooltip label={group.groupName} key={group.id} withArrow>
              <Pill size='md'>{group.roleName}</Pill>
            </KanbanTooltip>
          ))}

        {data?.data && !data.data.last && (
          <KanbanButton size='xs' variant='light' onClick={loadMore} disabled={isLoading}>
            <IconReload size={16} style={{ marginRight: 5 }} />
          </KanbanButton>
        )}
      </Flex>
    </Box>
  );
};
