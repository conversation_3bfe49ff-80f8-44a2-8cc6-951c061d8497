import React from 'react';
import { Box, Flex, ThemeIcon } from '@mantine/core';
import { CHANGE_STAGE_LABEL, ChangeStageType, ChangeStageTypeEnum } from '@common/constants/ChageStageType';
import { KanbanText } from 'kanban-design-system';
import { IconCircle, IconCircleCheck, IconCircleDashed } from '@tabler/icons-react';

interface StageProgressTextLineProps {
  currentStage: ChangeStageType;
  flowStages: ChangeStageType[];
}

const STAGE_STEPS: { stage: ChangeStageType; step: number }[] = [
  { stage: ChangeStageTypeEnum.Enum.SUBMISSION_PLANNING, step: 1 },
  { stage: ChangeStageTypeEnum.Enum.APPROVAL, step: 2 },
  { stage: ChangeStageTypeEnum.Enum.IMPLEMENTATION, step: 3 },
  { stage: ChangeStageTypeEnum.Enum.REVIEW_CLOSE, step: 4 },
];

const StageProgressTextLine = ({ currentStage, flowStages }: StageProgressTextLineProps) => {
  const stageSteps = STAGE_STEPS.filter((step) => flowStages.includes(step.stage));
  const currentStep = stageSteps.find((s) => s.stage === currentStage)?.step ?? 1;

  return (
    <Box mb='md' px='md' w='100%'>
      <Flex align='center' wrap='nowrap' justify='space-between' gap='md'>
        {stageSteps.map(({ stage, step }, index) => {
          const isDone = step < currentStep;
          const isCurrent = step === currentStep;
          const statusLabel = isDone ? '[Đã hoàn thành]' : isCurrent ? '[Đang thực hiện]' : '[Chưa hoàn thành]';
          const color = isDone ? 'green' : isCurrent ? 'blue' : 'gray';
          let icon: React.ReactNode;
          let bgColor = 'gray';
          let iconColor = 'gray';

          if (isDone) {
            bgColor = 'green';
            iconColor = 'white';
            icon = <IconCircleCheck size={16} color={iconColor} />;
          } else if (isCurrent) {
            bgColor = 'blue';
            iconColor = 'white';
            icon = <IconCircle size={16} color={iconColor} />;
          } else {
            bgColor = 'gray';
            icon = <IconCircleDashed size={16} />;
          }

          return (
            <React.Fragment key={stage}>
              <Flex direction='column' align='center' miw={80} flex='0 0 auto'>
                <ThemeIcon variant='filled' color={bgColor} radius='xl' size='xs'>
                  {icon}
                </ThemeIcon>
                <KanbanText fw={600} size='xs' ta='center' truncate>
                  {CHANGE_STAGE_LABEL[stage]}
                </KanbanText>
                <KanbanText size='xs' c={color} ta='center'>
                  {statusLabel}
                </KanbanText>
              </Flex>
              {index < STAGE_STEPS.length - 1 && <Box flex={1} h={1} bg='gray.5' mx={5} />}
            </React.Fragment>
          );
        })}
      </Flex>
    </Box>
  );
};

export default StageProgressTextLine;
