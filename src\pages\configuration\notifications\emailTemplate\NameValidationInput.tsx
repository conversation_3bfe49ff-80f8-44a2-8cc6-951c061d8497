import React, { useState, useEffect } from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';
import { KanbanInput } from 'kanban-design-system';
import { COMMON_MAX_LENGTH } from '@common/constants/ValidationConstant';
import useMutate from '@core/hooks/useMutate';
import { NotificationsEmailTemplateApi } from '@api/NotificationsEmailTemplateApi';
import { EmailTemplateDetailModel } from '@models/EmailTemplateModel';

interface NameValidationInputProps {
  form: UseFormReturn<EmailTemplateDetailModel>;
  id: number | undefined;
  name?: string;
}

export const NameValidationInput: React.FC<NameValidationInputProps> = ({ form, id, name }) => {
  const [nameToCheck, setNameToCheck] = useState<string>('');
  const [isNameTaken, setIsNameTaken] = useState<boolean | undefined>(undefined);

  const { data, mutate } = useMutate((params: { id: number | undefined; name: string }) => NotificationsEmailTemplateApi.existsByName(params), {
    successNotification: { enable: false },
    errorNotification: { enable: false },
  });

  const { clearErrors, control, setError, setValue } = form;

  useEffect(() => {
    if (name && name.trim() !== nameToCheck) {
      setNameToCheck(name.trim());
    }
  }, [name, nameToCheck]);

  useEffect(() => {
    if (data) {
      setIsNameTaken(data.data || false);
    }
  }, [data]);

  useEffect(() => {
    if (isNameTaken === true) {
      setError('name', { type: 'manual', message: 'This Template Name exists' });
    } else {
      clearErrors('name');
    }
  }, [isNameTaken, setError, clearErrors]);

  const handleNameBlur = (event: React.FocusEvent<HTMLInputElement>) => {
    const newName = event.target.value.trim();
    if (!newName) {
      setIsNameTaken(undefined);
      clearErrors('name');
      setNameToCheck('');
      setValue('name', '', { shouldValidate: true });
      return;
    }

    if (newName !== nameToCheck) {
      setNameToCheck(newName);
      setValue('name', newName, { shouldValidate: false });
      mutate({ id: id, name: newName });
    }
  };

  return (
    <Controller
      name={'name'}
      control={control}
      render={({ field, fieldState }) => (
        <KanbanInput
          {...field}
          withAsterisk
          label='Template Name'
          maxLength={COMMON_MAX_LENGTH}
          error={fieldState.error?.message}
          onBlur={handleNameBlur}
        />
      )}></Controller>
  );
};
