import { KanbanButton, KanbanIconButton, KanbanInput, KanbanText, KanbanTextarea, KanbanTitle } from 'kanban-design-system';
import { useDisclosure } from '@mantine/hooks';
import { Box, Flex } from '@mantine/core';
import { IconArrowLeft } from '@tabler/icons-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import React, { useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import { RoleApi } from '@api/RoleApi';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { buildUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import UnsaveConfirmModal from '@components/utils/ConfirmMessageUtils';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import customStyled from '@resources/styles/Common.module.scss';

import { callRequest } from '@core/api';
import { RoleDetailModel, RoleDetailSchema } from '@models/RoleModel';
import ViewListPermission from './components/ViewListPermission';
import { AclPermissionModel } from '@core/schema/AclPermission';
import { ACL_PERMISSIONS_CHECKLIST } from '@common/constants/AclPermissionConstants';
import { getAclPermissionId } from '@common/utils/AclPermissionUtils';
import { trimStringFields } from '@common/utils/Helpers';
import { buildNavigateState } from '@core/hooks/useSavedFilterCycle';

type RoleDetailPageProps = {
  idProp?: number;
};

const DEFAULT_ROLE: RoleDetailModel = {
  id: 0,
  name: '',
  description: '',
  toInsertPermissions: [],
};

export const RoleDetailPage: React.FC<RoleDetailPageProps> = () => {
  const id = Number(useParams().id);
  const { data: roleResponse } = useFetch(RoleApi.getDetail(id), { enabled: !!id });
  const { data: selectedPermissonResponse } = useFetch(RoleApi.findAllPermissionByRoleId(id), { enabled: !!id });

  const [role, setRole] = useState<RoleDetailModel>(DEFAULT_ROLE);
  const [oldRole, setOldRole] = useState<RoleDetailModel>(DEFAULT_ROLE);
  const isUpdateMode = !!role?.id && role.id !== 0;

  const [oldSelectedPermissions, setOldSelectedPermissions] = useState<AclPermissionModel[]>([]);
  const oldSelectedPermissionsSet = new Set(oldSelectedPermissions.map((it) => getAclPermissionId(it)));
  const [displayPermissions, setDisplayPermissions] = useState<AclPermissionModel[]>(ACL_PERMISSIONS_CHECKLIST);
  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);

  const navigate = useNavigate();

  const { mutate: saveMutate } = useMutate(RoleApi.saveOrUpdate, {
    successNotification: `Role saved successfully`,
    onSuccess: () => {
      handleNavigateToList();
    },
  });

  useEffect(() => {
    const dataLst = selectedPermissonResponse?.data;
    if (dataLst) {
      const selectedPermissionSet = new Set(
        dataLst.map((it) => {
          return getAclPermissionId(it);
        }),
      );

      setDisplayPermissions(
        ACL_PERMISSIONS_CHECKLIST.map((it) => {
          return selectedPermissionSet.has(getAclPermissionId(it)) ? { ...it, isChecked: true } : it;
        }),
      );

      setOldSelectedPermissions(dataLst.map((it) => ({ ...it })));
    }
  }, [selectedPermissonResponse?.data]);

  const handleNavigateToList = useCallback(() => {
    navigate(buildUrl(ROUTE_PATH.CONFIGURATION_USER_ROLES), { state: buildNavigateState({ fromDetail: true }) });
  }, [navigate]);

  const { clearErrors, formState, getValues, handleSubmit, register, setError, setValue } = useForm<RoleDetailModel>({
    defaultValues: DEFAULT_ROLE,
    resolver: zodResolver(RoleDetailSchema),
    mode: 'onBlur',
  });
  const validateNameOnBlur = async (name: string) => {
    const isValidZod = !formState.errors.name;

    if (isValidZod && name && name.trim()) {
      const checkNameResponse = await callRequest(RoleApi.getSameNameDetail(id, name.trim()));
      if (checkNameResponse.data) {
        setError('name', { type: 'api', message: 'Role Name existed' });
        return false;
      }
      clearErrors('name');
    }
    return true;
  };

  const handleSave = () => {
    const currentSelectPermissions = displayPermissions.filter((it) => it.isChecked).map((it) => ({ ...it }));
    const currentSelectPermissionsSet = new Set(currentSelectPermissions.map((it) => getAclPermissionId(it)));
    const updatedRole: RoleDetailModel = {
      ...getValues(),
      toInsertPermissions: currentSelectPermissions.filter((it) => !oldSelectedPermissionsSet.has(getAclPermissionId(it))),
      toDeletePermissions: oldSelectedPermissions.filter((it) => !currentSelectPermissionsSet.has(getAclPermissionId(it))),
    };
    saveMutate(updatedRole);
  };

  useEffect(() => {
    if (roleResponse?.data) {
      setRole(roleResponse.data);
      setOldRole(roleResponse.data);
      setValue('id', roleResponse.data.id);
      setValue('name', roleResponse.data.name);
      setValue('description', roleResponse.data.description);
    }
  }, [roleResponse?.data, setValue]);

  const handleExistWithoutSave = () => {
    const normalizedCurrents = trimStringFields(getValues());
    const normalizedOlds = trimStringFields(oldRole);

    const currentSelectPermissions = displayPermissions.filter((it) => it.isChecked).map((it) => ({ ...it }));
    const currentSelectPermissionsSet = new Set(currentSelectPermissions.map((it) => getAclPermissionId(it)));
    const toInsertPermissions = currentSelectPermissions.filter((it) => !oldSelectedPermissionsSet.has(getAclPermissionId(it)));
    const toDeletePermissions = oldSelectedPermissions.filter((it) => !currentSelectPermissionsSet.has(getAclPermissionId(it)));
    if (
      normalizedCurrents.name !== normalizedOlds.name ||
      normalizedCurrents.description !== normalizedOlds.description ||
      Object.keys(toInsertPermissions).length ||
      Object.keys(toDeletePermissions).length
    ) {
      openModal();
    } else {
      handleNavigateToList();
    }
  };

  return (
    <Box>
      <form onSubmit={handleSubmit(handleSave)}>
        <HeaderTitleComponent
          title=''
          rightSection={
            <Flex direction={'row'} align={'center'} gap={'xs'}>
              <KanbanButton size='xs' variant='subtle' onClick={handleExistWithoutSave}>
                Cancel
              </KanbanButton>
              <KanbanButton size='xs' variant='filled' type='submit'>
                Save
              </KanbanButton>
            </Flex>
          }
          leftSection={
            <Flex direction={'row'} align={'center'} gap={'xs'}>
              <KanbanIconButton size='sm' variant='subtle' onClick={handleExistWithoutSave}>
                <IconArrowLeft />
              </KanbanIconButton>
              <KanbanTitle fz={'h4'}>{isUpdateMode ? 'Edit Role' : 'Add Role'}</KanbanTitle>
            </Flex>
          }
        />

        <Box className={customStyled.elementBorder}>
          <KanbanInput
            label='Role Name'
            mb={'xs'}
            withAsterisk
            {...register('name', {
              onBlur: async (e) => {
                validateNameOnBlur(e.target.value);
              },
              onChange: () => {
                if (formState.errors.name) {
                  clearErrors('name');
                }
              },
            })}
            error={formState.errors['name']?.message}
            maxLength={255}
          />
          <KanbanTextarea
            label='Description'
            mb={'lg'}
            {...register('description')}
            error={formState.errors['description']?.message}
            maxLength={1000}
          />
          <Flex direction={'row'} align={'center'} gap={'xs'} mt='xs' mb='xs'>
            <KanbanText fw={'500'}>Permission</KanbanText>
          </Flex>

          <ViewListPermission permissions={displayPermissions} setPermissions={setDisplayPermissions} />
        </Box>

        <UnsaveConfirmModal opened={openedModal} onClose={closeModal} onConfirm={handleNavigateToList} />
      </form>
    </Box>
  );
};

export default RoleDetailPage;
