import { EntityAction } from '@common/constants/EntityActionConstants';
import { buildDetailUrl } from '@common/utils/RouterUtils';
import { Anchor, Popover, Text } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { KanbanText, KanbanTextProps } from 'kanban-design-system';
import React, { useCallback, useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

export const AmtShortTextContent = ({ data, ...props }: { data?: string; props?: KanbanTextProps }) => {
  const [opened, { close, open }] = useDisclosure(false);
  const [clickOpened, setClickOpened] = useState(false);

  useEffect(() => {
    setClickOpened(false);
    close();
  }, [close, data]);

  const handleOpen = useCallback(() => {
    setClickOpened(!opened);
    if (opened) {
      close();
    } else {
      open();
    }
  }, [close, open, opened]);

  return (
    <Popover width={'30%'} position='top-start' withArrow shadow='md' opened={opened} trapFocus>
      <Popover.Target>
        <Text
          fz={'sm'}
          lineClamp={1}
          {...props}
          h={'100%'}
          onClick={handleOpen}
          onMouseEnter={clickOpened ? undefined : open}
          onMouseLeave={clickOpened ? undefined : close}>
          {data}
        </Text>
      </Popover.Target>
      <Popover.Dropdown bg={'black'} c='white' onMouseEnter={clickOpened ? undefined : open} onMouseLeave={clickOpened ? undefined : close}>
        <KanbanText size='sm' mah={200} w='100%' style={{ overflowY: 'auto' }}>
          {data}
        </KanbanText>
      </Popover.Dropdown>
    </Popover>
  );
};

export const AmtShortTextLink = ({
  data,
  disableNavigate,
  disableShorten,
  entityAction = EntityAction.EDIT,
  entityId,
  routePath,
  ...otherTextProps
}: {
  routePath: string;
  entityId: number;
  entityAction?: EntityAction;
  data: string;
  disableShorten?: boolean;
  disableNavigate?: boolean;
} & Omit<KanbanTextProps, 'children'>) => {
  return (
    <Anchor
      component={Link}
      to={disableNavigate ? '' : buildDetailUrl(routePath, entityId, entityAction)}
      underline='hover'
      size='sm'
      c='var(--mantine-color-primary-7)'>
      {disableShorten ? (
        <KanbanText fw='500' {...otherTextProps}>
          {data}
        </KanbanText>
      ) : (
        <AmtShortTextContent data={data} {...otherTextProps} />
      )}
    </Anchor>
  );
};
