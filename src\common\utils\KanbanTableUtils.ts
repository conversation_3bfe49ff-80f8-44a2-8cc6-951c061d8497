import type { PaginationRequestModel } from '@models/EntityModelBase';
import type { TableAffactedSafeType } from 'kanban-design-system';

export function tableAffectedToPaginationRequestModel<T>(tableAffected: TableAffactedSafeType<T>): PaginationRequestModel<T> {
  return {
    page: tableAffected.page - 1,
    size: tableAffected.rowsPerPage || 10,
    search: tableAffected.search,
    sortBy: tableAffected.sortedBy || ('createdDate' as keyof T),
    sortOrder: tableAffected.sortOrder,
  };
}

export function tableAffectedToMultiColumnFilterPaginationRequestModel<T>(tableAffected: TableAffactedSafeType<T>): PaginationRequestModel<T> {
  return {
    page: tableAffected.page - 1,
    size: tableAffected.rowsPerPage || 10,
    search: tableAffected.search,
    sortBy: tableAffected.sortedBy,
    sortOrder: tableAffected.sortOrder,
    advancedFilterMapping: tableAffected.advancedFilterMapping,
  };
}
