import {
  AdvancedFilterMappingType,
  ColumnType,
  getDefaultTableAffected,
  KanbanIconButton,
  KanbanTitle,
  renderDateTime,
  SortOrder,
} from 'kanban-design-system';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanButton } from 'kanban-design-system';
import { KanbanTable, KanbanTableProps, TableAffactedSafeType } from 'kanban-design-system';
import { Box, Flex } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IconEdit, IconPlus, IconTrash } from '@tabler/icons-react';
import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { GroupModel } from '@models/GroupRoleUserModel';
import { GroupApi } from '@api/GroupApi';
import { useNavigate } from 'react-router-dom';
import { EntityAction } from '@common/constants/EntityActionConstants';
import useFetch from '@core/hooks/useFetch';
import customStyled from '../../resources/styles/Common.module.scss';

import useMutate from '@core/hooks/useMutate';
import equal from 'fast-deep-equal';
import { LocalStorageKey } from '@common/constants/LocalStorageKeyConstants';
import { AmtDateRangeInput, DateRangeFilter, dateRangeKeys } from '@components/AmtDateRangeInput';
import { dateToIsoString, parseFilterWithDate } from '@common/utils/DateUtils';
import { buildDetailUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import { FilterTextInput } from '@components/AmtFilterTextInput';
import { deleteConfirm } from '@components/utils/ConfirmMessageUtils';
import useSavedFilterCycle from '@core/hooks/useSavedFilterCycle';
import AmtModal from '@components/AmtModal';
import { useSavedColumns } from '@core/hooks/useSavedColumns';
import { AmtShortTextContent, AmtShortTextLink } from '@components/AmtShortTextContent';

export type GroupsPageFilter = DateRangeFilter & {
  groupName?: string;
};

const initOrUpdatedFilterPayloads = (prev?: TableAffactedSafeType<GroupModel>, initFilters?: GroupsPageFilter) => {
  const prevFilter = prev || getDefaultTableAffected();
  if (initFilters) {
    const filterObj: AdvancedFilterMappingType<GroupModel> = {
      ['name']: {
        filterOption: 'contains',
        value: {
          fromValue: initFilters.groupName,
        },
      },
      ['modifiedDate']: {
        filterOption:
          initFilters.startDate && initFilters.endDate ? 'betweenInclusive' : initFilters.startDate ? 'greaterThanOrEqualTo' : 'lessThanOrEqualTo',
        value: {
          fromValue: initFilters.startDate ? dateToIsoString(initFilters.startDate) : '',
          toValue: initFilters.endDate ? dateToIsoString(initFilters.endDate) : '',
        },
      },
    };
    const advancedFilterMapping = { ...prevFilter.advancedFilterMapping, ...filterObj };
    return {
      ...prevFilter,
      advancedFilterMapping: { ...advancedFilterMapping },
      sortedBy: 'modifiedDate',
      sortOrder: SortOrder.DESC,
    } as TableAffactedSafeType<GroupModel>;
  }
  return prevFilter;
};

export const GroupsPage = () => {
  const [totalRecords, setTotalRecords] = useState(0);

  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);

  const [groups, setGroups] = useState<GroupModel[]>([]);

  const [curentToDeleteMultiGroups, setCurentToDeleteMultiGroups] = useState<GroupModel[]>([]);
  const [currentToDeleteGroup, setCurrentToDeleteGroup] = useState<GroupModel>();

  const navigate = useNavigate();

  const [inputFilters, setInputFilters] = useState<GroupsPageFilter>({} as GroupsPageFilter);

  const [savedFilters, setSavedFilters] = useSavedFilterCycle({
    key: LocalStorageKey.SEARCH_VALUE_GROUPS_PAGE,
    deserialize: (str) => parseFilterWithDate<GroupsPageFilter>(str, dateRangeKeys),
    setInputFilters,
  });

  const [isDateRangeInputValid, setIsDateRangeInputValid] = useState<boolean>(true);
  const [tableAffectedChange, setTableAffectedChange] = useState<TableAffactedSafeType>();

  const deleteMessageConfig = deleteConfirm(currentToDeleteGroup ? [currentToDeleteGroup.name] : curentToDeleteMultiGroups.map((it) => it.name));
  const [orderColumn, setOrderColumn] = useState<string[]>();

  const { data: groupsResponse, refetch: refetchList } = useFetch(
    GroupApi.findAllGroups(initOrUpdatedFilterPayloads(tableAffectedChange, savedFilters)),
    {
      enabled: !!tableAffectedChange,
    },
  );

  /**
   * Search by update input of API :tableAffectedChange
   * and handle update search history savedFilters
   */
  const handleSearch = useCallback(() => {
    if (isDateRangeInputValid) {
      setSavedFilters((prev) => ({ ...prev, ...inputFilters }));
      setTableAffectedChange((prev) => {
        return initOrUpdatedFilterPayloads(prev, inputFilters);
      });
    }
  }, [inputFilters, isDateRangeInputValid, setSavedFilters]);

  useEffect(() => {
    if (groupsResponse?.data?.content) {
      setGroups(groupsResponse.data.content);
      setTotalRecords(groupsResponse.data.totalElements);
    }
  }, [groupsResponse?.data]);

  const { mutate: deleteMultiMutate } = useMutate(GroupApi.deleteGroupByIds, {
    successNotification: 'Delete group(s) successfully',
    onSuccess: () => {
      refetchList();
    },
  });

  const handleDeleteMulti = useCallback(
    (datas: GroupModel[]) => {
      deleteMultiMutate(datas.map((it) => it.id));
      setCurentToDeleteMultiGroups([]);
      setCurrentToDeleteGroup(undefined);
      closeModal();
    },
    [closeModal, deleteMultiMutate],
  );
  const columns = useMemo(() => {
    return [
      {
        name: 'name',
        title: 'Group Name',
        width: '30%',
        customRender: (_, rowData) => {
          return (
            <AmtShortTextLink
              routePath={ROUTE_PATH.CONFIGURATION_USER_GROUP_DETAIL}
              entityId={rowData.id}
              data={rowData.name}
              disableShorten
              fw={500}
            />
          );
        },
      },
      {
        name: 'description',
        title: 'Description',
        width: '30%',
        customRender: (data) => <AmtShortTextContent data={data} />,
      },
      {
        name: 'modifiedDate',
        title: 'Modified date',
        customRender: renderDateTime,
      },
      {
        name: 'createdBy',
        title: 'Created By',
      },
      {
        name: 'createdDate',
        title: 'Created date',
        customRender: renderDateTime,
        hidden: true,
      },

      {
        name: 'modifiedBy',
        title: 'Modified By',
        hidden: true,
      },
    ] as ColumnType<GroupModel>[];
  }, []);

  const savedColumnDisplay = useSavedColumns<GroupModel>(LocalStorageKey.COLUMN_DISPLAY_GROUPS_PAGE, columns, orderColumn);

  const tableProps: KanbanTableProps<GroupModel> = useMemo(() => {
    const tblProps: KanbanTableProps<GroupModel> = {
      title: '',
      columns: savedColumnDisplay,
      data: groups,
      customRowProps: (data) => ({
        onDoubleClick: () => {
          navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_USER_GROUP_DETAIL, data.id, EntityAction.EDIT));
        },
      }),
      serverside: {
        totalRows: totalRecords,

        onTableAffected: (dataSet) => {
          if (!equal(tableAffectedChange, dataSet)) {
            setTableAffectedChange((prev) => (prev ? { ...dataSet, advancedFilterMapping: prev.advancedFilterMapping } : { ...dataSet }));
          }
        },
      },
      pagination: {
        enable: true,
      },
      selectableRows: {
        enable: true,
        customAction: (_, __) => (
          <KanbanButton
            leftSection={<IconTrash />}
            size={'xs'}
            bg='red'
            onClick={() => {
              openModal();
            }}>
            Delete(s)
          </KanbanButton>
        ),
        crossPageSelected: {
          rowKey: 'id',
          setSelectedRows: setCurentToDeleteMultiGroups,
          selectedRows: curentToDeleteMultiGroups,
        },
      },
      actions: {
        customAction: (data) => {
          return (
            <>
              <KanbanIconButton
                mr={'xs'}
                variant='transparent'
                size={'sm'}
                onClick={() => {
                  navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_USER_GROUP_DETAIL, data.id, EntityAction.EDIT));
                }}>
                <IconEdit />
              </KanbanIconButton>

              <KanbanIconButton
                mr={'xs'}
                variant='transparent'
                size={'sm'}
                c={'red'}
                onClick={() => {
                  setCurrentToDeleteGroup(data);
                  openModal();
                }}>
                <IconTrash />
              </KanbanIconButton>
            </>
          );
        },
      },
      columnOrderable: {
        /** Default is `true` */
        onOrder: (data) => {
          setOrderColumn(data.map((it) => it.name));
        },
      },
      showNumericalOrderColumn: true,
    };
    return tblProps;
  }, [savedColumnDisplay, groups, totalRecords, curentToDeleteMultiGroups, navigate, tableAffectedChange, openModal]);
  const handleClearFilter = useCallback(
    (key: keyof GroupsPageFilter) => {
      const toUpdate = { [key]: '' };
      setInputFilters((prev) => ({ ...prev, ...toUpdate }));
      setSavedFilters((prev) => ({ ...prev, ...toUpdate }));
    },
    [setSavedFilters],
  );

  const handleClearDateFilter = useCallback(() => {
    const toUpdate: DateRangeFilter = { startDate: undefined, endDate: undefined };

    setInputFilters((prev) => ({ ...prev, ...toUpdate }));
    setSavedFilters((prev) => ({ ...prev, ...toUpdate }));
  }, [setSavedFilters]);

  return (
    <Box className={customStyled.tableCs}>
      <HeaderTitleComponent
        title=''
        rightSection={
          <Flex>
            <KanbanButton
              leftSection={<IconPlus />}
              size={'xs'}
              onClick={() => {
                navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_USER_GROUP_DETAIL, 0, EntityAction.CREATE));
              }}>
              Add Group
            </KanbanButton>
          </Flex>
        }
        leftSection={<KanbanTitle fz={'h4'}>Groups</KanbanTitle>}
      />
      <Flex dir='row' gap='xs' mb='xs'>
        <FilterTextInput
          placeholder='Group name'
          value={inputFilters.groupName ?? ''}
          onChange={(val) => {
            setInputFilters((prev) => ({ ...prev, groupName: val.target.value }));
          }}
          onBlur={() => {
            handleSearch();
          }}
          onClear={() => handleClearFilter('groupName')}
        />

        <AmtDateRangeInput
          startDate={inputFilters.startDate}
          endDate={inputFilters.endDate}
          updateFilters={setInputFilters}
          setValid={setIsDateRangeInputValid}
          onBlur={() => {
            handleSearch();
          }}
          onClear={handleClearDateFilter}
          tooltipLabel='Modified date'
        />
      </Flex>
      <Box className={customStyled.elementBorder}>
        <KanbanTable {...tableProps} title='' />
      </Box>

      <AmtModal
        opened={openedModal}
        title={deleteMessageConfig.title}
        onClose={() => {
          setCurrentToDeleteGroup(undefined);
          closeModal();
        }}
        actions={
          <KanbanButton
            variant='filled'
            onClick={() => (currentToDeleteGroup ? handleDeleteMulti([currentToDeleteGroup]) : handleDeleteMulti(curentToDeleteMultiGroups))}>
            Confirm
          </KanbanButton>
        }>
        {deleteMessageConfig.children}
      </AmtModal>
    </Box>
  );
};

GroupsPage.whyDidYouRender = true;
export default GroupsPage;
