.dragItem {
  user-select: 'none';
  background:  '#007bff' ;
  height: 20px ;
  width: 30px;
  color:'white' ;
  box-shadow:  '0px 4px 8px rgba(0, 0, 0, 0.2)' ;
  transform:  'rotate(2deg)' ;
  transition: 'background 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease';
}

.dropableArea {
  height: 32px;
  border-radius: 8px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1971c2;
  font-size: 13px;
  background: transparent;
  border: '';
}

.dropWrapper {
  border: '';
  border-color: #d0d0d0;
  background: #fafbfc;
  border-radius: 8px;
  padding: 12px;
  transition: border-color 0.2s, background 0.2s;
  display: flex;
  flex-direction: column;
  gap: 12px;

  &.isDraggingOver {
    border: 2px dashed;
    border-radius: 8px;
    border-color: #1971c2;
    background: #e7f5ff;
  }
}


.roleName {
  
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  align-items: center;
  justify-content: flex-start;
  font-weight: 500;
}

.boxBase {
  border-radius: 12px;
  transition: border-color 0.2s;
  min-height: 120px;
  padding: 8px;
  background-color: transparent;
}

.boxHighlight {
  border: 2px dashed #1971c2;
  background-color: #e7f5ff;
}

.textareaComment input{
  background-color: var(--mantine-color-gray-0);
  border-color: var(--mantine-color-gray-3);
  resize: none;
}

