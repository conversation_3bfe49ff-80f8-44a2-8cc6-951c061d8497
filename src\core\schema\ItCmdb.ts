import { z } from 'zod';

export const CiTypeSchema = z.object({
  id: z.number(),
  name: z.string(),
});
export type CiType = z.infer<typeof CiTypeSchema>;

export const AttributeSchema = z.object({
  id: z.number(),
  name: z.string(),
  ciTypeId: z.number(),
});
export type Attribute = z.infer<typeof AttributeSchema>;

export const SearchTypeSchema = z.enum(['=', 'LIKE']);
export type SearchType = z.infer<typeof SearchTypeSchema>;
export const SEARCH_TYPES: SearchType[] = SearchTypeSchema.options;

export const AMT_VALUES = {
  IP: 'IP',
  TASK_NAME: 'TASK NAME',
  URL: 'URL',
  HOST_NAME: 'HOST NAME',
} as const;
export type AmtType = (typeof AMT_VALUES)[keyof typeof AMT_VALUES];
export const AmtTypeSchema = z.enum([AMT_VALUES.IP, AMT_VALUES.TASK_NAME, AMT_VALUES.URL, AMT_VALUES.HOST_NAME]);
export const AMT_TYPES: AmtType[] = AmtTypeSchema.options;
