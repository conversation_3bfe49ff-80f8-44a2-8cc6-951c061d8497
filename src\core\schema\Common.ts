import { z } from 'zod';

export const AuditSchema = z.object({
  createdBy: z.string().nullish(),
  createdDate: z.string().nullish(),
  modifiedBy: z.string().nullish(),
  modifiedDate: z.string().nullish(),
});

export function createPageSchema<T>(schema: z.ZodType<T>) {
  return z.object({
    totalPages: z.number(),
    totalElements: z.number(),
    size: z.number(),
    content: z.array(schema),
    number: z.number(),
    sort: z.object({
      empty: z.boolean(),
      sorted: z.boolean(),
      unsorted: z.boolean(),
    }),
    first: z.boolean(),
    last: z.boolean(),
    numberOfElements: z.number(),
    pageable: z.object({
      pageNumber: z.number(),
      pageSize: z.number(),
      sort: z.object({
        empty: z.boolean(),
        sorted: z.boolean(),
        unsorted: z.boolean(),
      }),
      offset: z.number(),
      unpaged: z.boolean(),
      paged: z.boolean(),
    }),
    empty: z.boolean(),
  });
}

export function createResponseSchema<T>(schema: z.ZodType<T>) {
  return z.object({
    status: z.number().nullish(),
    path: z.string().nullish(),
    clientMessageId: z.string().nullish(),
    errorCode: z.string().nullish(),
    errorDescription: z.string().nullish(),
    data: schema.nullish(),
  });
}

export function createCursorPageSchema<T, C>(schema: z.ZodType<T>, cursor: z.ZodType<C>) {
  return z.object({
    data: z.array(schema).optional(),
    nextCursor: cursor.optional(),
  });
}

export function createListSchema<T>(schema: z.ZodType<T>) {
  return z.array(schema);
}

export type ResponseData<T> = z.infer<ReturnType<typeof createResponseSchema<T>>>;

export type Page<T> = z.infer<ReturnType<typeof createPageSchema<T>>>;

export type CursorPage<T, C> = z.infer<ReturnType<typeof createCursorPageSchema<T, C>>>;

export type ItemCheck<T> = Record<string, T>;
export type ItemCheckComponentProps<T> = {
  parentId: number;
  setToInserts: React.Dispatch<React.SetStateAction<ItemCheck<T>>>;
  toDeletes: ItemCheck<T>;
  setToDeletes: React.Dispatch<React.SetStateAction<ItemCheck<T>>>;
  selecteds: ItemCheck<T>;
  setSelecteds: React.Dispatch<React.SetStateAction<ItemCheck<T>>>;
  setOldPagings?: React.Dispatch<React.SetStateAction<ItemCheck<T>>>;
  setUpdatePagings?: React.Dispatch<React.SetStateAction<ItemCheck<T>>>;
};

export type ItemCheckComponentWithUsernameProps<T> = Omit<ItemCheckComponentProps<T>, 'parentId'> & {
  username: string;
  toInserts: ItemCheck<T>;
};

export const DEFAULT_ITEM_CHECK: ItemCheck<any> = {};

export type SavedDisplayColumns<T> = Partial<{
  [K in keyof T]: boolean;
}>;
