import React from 'react';
import { KanbanText } from 'kanban-design-system';
import { List, ScrollAreaAutosize } from '@mantine/core';

export const AmtConfirmDialogComponent: React.FC<{ listData?: string[] }> = ({ listData }) => {
  return (
    <>
      <KanbanText>This action cannot be undone. Do you still want to delete items(s)?</KanbanText>
      {listData && (
        <ScrollAreaAutosize>
          <List listStyleType='disc'>
            {listData.map((it, idx) => (
              <List.Item key={idx} fz={'sm'}>
                <KanbanText>{it}</KanbanText>
              </List.Item>
            ))}
          </List>
        </ScrollAreaAutosize>
      )}
    </>
  );
};
