import { isAuthorized } from '@common/utils/AclPermissionUtils';
import { AclPermission } from '@core/schema/AclPermission';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import { useSelector } from 'react-redux';
//ft/role function authorize
export const useCheckPermissons = (requirePermissions: AclPermission[], allMatchPermissions = false) => {
  const currentUser = useSelector(getCurrentUser);

  if (!currentUser) {
    return false;
  }
  return isAuthorized(currentUser.userInfo?.aclPermissions || [], requirePermissions, allMatchPermissions);
  //todo bloom filter
};
