import React, { useMemo, useState } from 'react';
import { getDefaultTableAffected, TableAffactedSafeType } from 'kanban-design-system';
import ComboboxLoadMore from '@components/ComboboxLoadMore';
import { UserSelectedModel } from '@models/UserModel';
import useInfiniteFetch from '@core/hooks/useInfiniteFetch';
import { UserApi } from '@api/UserApi';
import { TextLengthConstants } from '@common/constants/TextLengthConstants';

interface UserSelectComponentProps {
  value: UserSelectedModel[];
  onChange: (val: UserSelectedModel[]) => void;
  onBlur?: () => void;
  error?: string;
  isViewMode?: boolean;
  label?: string;
  isSingleSelect?: boolean;
  disable?: boolean;
}

const UserSelectComponent: React.FC<UserSelectComponentProps> = ({ disable, error, isSingleSelect, isViewMode, label, onBlur, onChange, value }) => {
  const [searchValue, setSearchValue] = useState('');

  const serviceSearchParams: TableAffactedSafeType = useMemo(
    () => ({
      ...getDefaultTableAffected(),
      sortedBy: 'name',
      userName: searchValue,
      advancedFilterMapping: {
        userName: {
          filterOption: 'contains',
          value: { fromValue: searchValue },
        },
      },
    }),
    [searchValue],
  );

  const { fetchNextPage, flatData } = useInfiniteFetch(UserApi.findAll(serviceSearchParams), {
    showLoading: false,
    enabled: true,
  });

  const options = useMemo(
    () =>
      flatData.map((item) => ({
        id: item.userName,
        name: `${item.name} (${item.userName})`,
      })),
    [flatData],
  );

  const selectedValues: UserSelectedModel[] = useMemo(() => value?.map((v) => ({ id: v.id, name: v.name })) ?? [], [value]);

  return (
    <ComboboxLoadMore
      label={label}
      required
      clearable={!isViewMode}
      scrollableForValue
      options={options}
      values={selectedValues}
      renderOptionLabel={(data) => data.name}
      renderPillLabel={(item) => item.name}
      onChange={onChange}
      onBlur={onBlur}
      onScroll={fetchNextPage}
      onSearch={(val) => setSearchValue(val)}
      maxHeightValue={TextLengthConstants.PILL_TO_SHOW_MAX_LENGTH}
      maxSelection={isSingleSelect ? 1 : undefined}
      onClickedOption={() => setSearchValue('')}
      error={error}
      disabled={disable}
    />
  );
};

export default UserSelectComponent;
