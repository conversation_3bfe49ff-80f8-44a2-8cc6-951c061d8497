import { z } from 'zod';
import { ChangeStageTypeEnum } from '@common/constants/ChageStageType';

export const ChangeRequestNoteSchema = z.object({
  id: z.number(),
  content: z.string(),
  stage: ChangeStageTypeEnum,
  changeRequestId: z.number(),
  createdDate: z.string(),
  createdBy: z.string().optional(),
  modifiedBy: z.string().optional(),
  modifiedDate: z.string(),
});

export type ChangeRequestNote = z.infer<typeof ChangeRequestNoteSchema>;
