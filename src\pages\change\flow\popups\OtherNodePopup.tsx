import AmtModal from '@components/AmtModal';
import { KanbanButton, KanbanInput, KanbanSelect } from 'kanban-design-system';
import React, { useMemo, useState } from 'react';
import { UseFormReturn, useWatch } from 'react-hook-form';
import ReferenceSession from './GroupUserMultiSelect';
import { ChangeFlowModel, ChangeFlowNodeGroupModel, ChangeFlowNodeModel, ChangeFlowNodeType } from '@models/ChangeFlowModel';
import { COMMON_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { INPUT_REQUIRE } from '@core/message/MesageConstant';

interface OtherNodePopupProps {
  index: number;
  onCancel: () => void;
  onConfirm: (node: ChangeFlowNodeModel) => void;
  form: UseFormReturn<ChangeFlowModel>;
  type: ChangeFlowNodeType;
}

const OtherNodePopup: React.FC<OtherNodePopupProps> = ({ form, index, onCancel, onConfirm, type }) => {
  const rawNodes = useWatch({ control: form.control, name: 'nodes' });
  const nodes = useMemo(() => rawNodes || [], [rawNodes]);

  const initialNode = useMemo(() => nodes[index] || {}, [nodes, index]);

  const [name, setName] = useState(initialNode.name ?? '');
  const [nodeLevel, setNodeLevel] = useState<number | undefined>(initialNode.nodeLevel);
  const [groups, setGroups] = useState<ChangeFlowNodeGroupModel[]>(initialNode.groups ?? []);

  const [errors, setErrors] = useState<{ name?: string; nodeLevel?: string; groups?: string }>({});

  const levelOptions = useMemo(() => {
    const selectedLevels = nodes
      .map((node, i) => (i === index ? null : node?.nodeLevel))
      .filter((val): val is number => val !== null && val !== undefined);
    return [1, 2, 3, 4]
      .filter((val) => !selectedLevels.includes(val) || val === nodeLevel)
      .map((val) => ({
        value: val.toString(),
        label: `Level ${val}`,
      }));
  }, [nodes, index, nodeLevel]);

  const handleConfirm = () => {
    const newErrors: typeof errors = {};
    if (!name.trim()) {
      newErrors.name = INPUT_REQUIRE;
    }
    if (!nodeLevel) {
      newErrors.nodeLevel = INPUT_REQUIRE;
    }

    if (!groups || groups.length === 0) {
      newErrors.groups = INPUT_REQUIRE;
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setErrors({});
    const currentNode: ChangeFlowNodeModel = {
      ...initialNode,
      name: name.trim(),
      nodeLevel: nodeLevel as number,
      groups,
      type: initialNode.type ?? type,
    };

    onConfirm(currentNode);
  };

  const handleNameChange = (val: string) => {
    setName(val);
    if (errors.name) {
      setErrors((prev) => ({ ...prev, name: undefined }));
    }
  };

  const handleLevelChange = (val: string | null) => {
    const level = val ? parseInt(val, 10) : undefined;
    setNodeLevel(level);
    if (errors.nodeLevel) {
      setErrors((prev) => ({ ...prev, nodeLevel: undefined }));
    }
  };

  const handleGroupsChange = (newGroups: ChangeFlowNodeGroupModel[]) => {
    setGroups(newGroups);
    if (errors.groups) {
      setErrors((prev) => ({ ...prev, groups: undefined }));
    }
  };

  return (
    <AmtModal
      title={type === 'CAB' || initialNode.type === 'CAB' ? 'CAB Node' : 'Approval Node'}
      opened
      onClose={onCancel}
      actions={
        <KanbanButton variant='filled' onClick={handleConfirm}>
          Confirm
        </KanbanButton>
      }>
      <KanbanInput
        label='Name'
        withAsterisk
        maxLength={COMMON_MAX_LENGTH}
        value={name}
        onChange={(e) => handleNameChange(e.currentTarget.value)}
        error={errors.name}
      />

      <ReferenceSession type={type} value={groups} onChange={handleGroupsChange} error={errors.groups} />

      <KanbanSelect
        label='Level'
        withAsterisk
        required
        data={levelOptions}
        value={nodeLevel?.toString()}
        onChange={handleLevelChange}
        error={errors.nodeLevel}
        allowDeselect={false}
      />
    </AmtModal>
  );
};

export default OtherNodePopup;
