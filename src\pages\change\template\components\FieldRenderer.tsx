import React, { useCallback, useEffect, useRef } from 'react';
import { IconAlertCircle, IconCalendar } from '@tabler/icons-react';
import { useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { Box, ComboboxItem, Group, Textarea, Tooltip } from '@mantine/core';
import { DateTimePicker } from '@mantine/dates';
import { RichTextEditor } from '@mantine/tiptap';
import { ChangeTemplate, TemplateCustomFieldTypeEnum, TemplateFieldItem } from '@core/schema/ChangeTemplate';
import styles from '@resources/styles/ChangeTemplateBuilder.module.scss';
import {
  getDefaultTableAffected,
  KanbanInput,
  KanbanMultiSelect,
  KanbanNumberInput,
  KanbanText,
  useDebounceCallback,
  useSafetyRenderStateRef,
} from 'kanban-design-system';
import { UseFormRegister, Control, Controller, FieldErrors, ControllerRenderProps, useFormContext, useWatch } from 'react-hook-form';
import { CustomFieldApi } from '@api/CustomFieldApi';
import { useState } from 'react';
import { DRAG_TAG } from '@common/constants/ChangeTemplateConstants';
import { PicklistOption } from '@models/CustomField';
import useInfiniteFetchCore from '@core/hooks/useInfiniteFetchCore';
import { FetchNextPageOptions, InfiniteData, InfiniteQueryObserverResult } from '@tanstack/react-query';
import { Page, ResponseData } from '@core/schema/Common';
import Underline from '@tiptap/extension-underline';
import { MAX_VALUE_MULTI_LINE_LENGTH, MAX_VALUE_NUMBER, MAX_VALUE_SINGLE_LINE_LENGTH } from '@common/constants/ValidationConstant';
import { useDebouncedValue } from '@mantine/hooks';
import { DebounceTime } from '@common/constants/DebounceTimeConstant';
import { DATE_TIME_AMPM_FORMAT } from '@common/constants/DateTimeConstants';

const RichTextFieldRenderer: React.FC<{
  field: TemplateFieldItem;
  controllerField: ControllerRenderProps<ChangeTemplate, `fields.${number}.fieldConstraint.defaultValue`>;
  required: boolean;
}> = ({ controllerField, field, required }) => {
  const onChangeRef = useRef<(value: string) => void>();
  const noticeEditor = useEditor({
    extensions: [StarterKit, Underline],
    content: controllerField.value || '',
    onUpdate: ({ editor }) => {
      onChangeRef.current?.(editor.getHTML());
    },
  });

  useEffect(() => {
    onChangeRef.current = controllerField.onChange;
  }, [controllerField.onChange]);

  useEffect(() => {
    if (noticeEditor && noticeEditor.getHTML() !== controllerField.value) {
      noticeEditor.commands.setContent(controllerField.value || '', false);
    }
  }, [controllerField.value, noticeEditor]);

  return (
    <Group align='center' justify='space-between' pr='xs' gap={'0'} className={styles.fieldGroup}>
      {field.customFieldType !== TemplateCustomFieldTypeEnum.enum.BREAK && <FieldName field={field} required={required} />}
      <RichTextEditor
        editor={noticeEditor}
        className={`${styles.inputField} ${styles.richTextEditor} `}
        maw={field.customFieldType === TemplateCustomFieldTypeEnum.enum.BREAK ? '100%' : ''}
        withTypographyStyles={false}
        styles={{
          root: {
            height: '100%',
            overflowY: 'auto',
          },
          content: {
            height: '100%',
          },

          typographyStylesProvider: {
            height: '100%',
          },
        }}>
        <RichTextEditor.Toolbar sticky>
          <RichTextEditor.ControlsGroup>
            <RichTextEditor.Bold />
            <RichTextEditor.Italic />
            <RichTextEditor.Underline />
            <RichTextEditor.Strikethrough />
            <RichTextEditor.ClearFormatting />
          </RichTextEditor.ControlsGroup>
          <RichTextEditor.ControlsGroup>
            <RichTextEditor.Blockquote />
            <RichTextEditor.Hr />
            <RichTextEditor.BulletList />
            <RichTextEditor.OrderedList />
          </RichTextEditor.ControlsGroup>
        </RichTextEditor.Toolbar>
        <RichTextEditor.Content />
      </RichTextEditor>
    </Group>
  );
};

const FieldName: React.FC<{ field: TemplateFieldItem; required: boolean }> = ({ field, required }) => {
  return (
    <Tooltip label={field.name} multiline maw={'30%'} position='top-start' openDelay={500}>
      <Group className={styles.fieldName} justify='flex-start ' pr='xs' gap={'0'} align='center' w={'120px'}>
        <KanbanText fw={'500'} c={required ? 'red' : 'white'}>
          *
        </KanbanText>
        <KanbanText size='xs' className={`${styles.fieldName} ${styles.dragHandle} ${DRAG_TAG} `} lineClamp={3}>
          {field.name}
        </KanbanText>
      </Group>
    </Tooltip>
  );
};

const FieldError: React.FC<{ message?: string }> = ({ message }) => {
  return (
    message && (
      <Tooltip label={message} color='red' withArrow position='right'>
        <Box ml={6} mr={2} style={{ display: 'flex', alignItems: 'center' }}>
          <IconAlertCircle color='red' size={18} />
        </Box>
      </Tooltip>
    )
  );
};

interface FieldRendererProps {
  templateField: TemplateFieldItem;
  fieldIndex: number;
  register: UseFormRegister<ChangeTemplate>;
  errors: FieldErrors<ChangeTemplate>;
  control: Control<ChangeTemplate>;
}
const appendPickListFieldDataLst = (prevList: PicklistOption[], lst?: PicklistOption[] | null) => {
  const uniqueOptionsMap = new Map<string, PicklistOption>();
  const combinedAndSortedList = [...(prevList || []), ...(lst || [])].sort((a, b) => a.position - b.position);
  combinedAndSortedList.forEach((option) => {
    if (!option.value || option.value.trim() === '') {
      return;
    }
    uniqueOptionsMap.set(option.value, option);
  });

  return Array.from(uniqueOptionsMap.values()).map((option) => ({
    value: option.value,
    label: option.value,
  }));
};
const FieldRenderer: React.FC<FieldRendererProps> = ({ control, errors, fieldIndex: index, register, templateField }) => {
  const [isFocused, setIsFocused] = useState(false);
  const { setValue, trigger } = useFormContext<ChangeTemplate>();

  const handleValidate = useCallback(async () => {
    await trigger(`fields.${index}.fieldConstraint.defaultValue`);
  }, [index, trigger]);

  const required = useWatch({ name: `fields.${index}.required` }) as boolean;
  const minConstraint: number = useWatch({ control, name: `fields.${index}.fieldConstraint.min` }) || 0;
  const maxConstraint: number | null | undefined = useWatch({ control, name: `fields.${index}.fieldConstraint.max` });
  const [picklistFieldDataLst, setPicklistFieldDataLst] = useState<ComboboxItem[]>(appendPickListFieldDataLst([], templateField.picklistOptions));

  const [searchPickList, setSearchPickList] = useState('');
  const [searchPickListDebounced] = useDebouncedValue(searchPickList, DebounceTime.MILLISECOND_300);
  const pageNum = useSafetyRenderStateRef(1);

  const { data: pickListFieldResponse, fetchNextPage } = useInfiniteFetchCore(
    CustomFieldApi.findPickListOptions(templateField.customFieldId, templateField.isReference || false, {
      ...getDefaultTableAffected(),
      page: pageNum.current,
      sortedBy: 'name',
      rowsPerPage: 100,
      advancedFilterMapping: {
        name: {
          filterOption: 'contains',
          value: {
            fromValue: searchPickListDebounced,
          },
        },
      },
    }),
    {
      getPageParam: (requestConfig) => requestConfig.data?.page ?? 0,
      getNextPageParam: (lastPage) => {
        if (lastPage?.data?.number !== undefined && lastPage?.data?.totalPages !== undefined) {
          const nextPage = lastPage.data.number + 1;
          return nextPage < lastPage.data.totalPages ? nextPage : undefined;
        }
        return undefined;
      },
    },
    {
      enabled:
        templateField.customFieldType === TemplateCustomFieldTypeEnum.enum.PICKLIST && (templateField.picklistOptions?.length === 0 || isFocused),
      refetchOnWindowFocus: false,
      showLoading: false,
    },
  );
  const handleLoadPickListFieldDataLst = useCallback(() => {
    if (pickListFieldResponse && pickListFieldResponse.pages) {
      const pages = pickListFieldResponse.pages;

      setPicklistFieldDataLst((prev) => {
        return appendPickListFieldDataLst(
          prev.map((it, index) => ({ value: it.value, position: index, isDefault: false })),
          pages.flatMap(
            (page) =>
              page.data?.content?.map((item) => ({
                ...item,
              })) || [],
          ),
        );
      });

      //load default value to single picklist
      if (!templateField.isMultiple && templateField.isNew && !templateField.picklistOptions?.length) {
        setValue(
          `fields.${index}.picklistOptions`,
          pages.flatMap(
            (page) =>
              page.data?.content
                ?.filter((it) => it.isDefault)
                .map((item) => ({
                  ...item,
                })) || [],
          ),
        );
      }
    }
  }, [index, pickListFieldResponse, setValue, templateField.isMultiple, templateField.isNew, templateField.picklistOptions?.length]);
  const getFieldError = useCallback(() => {
    const constraint = errors.fields?.[index]?.fieldConstraint;
    let currentMaxConstraint = maxConstraint;
    const isMultiLine = templateField.type === TemplateCustomFieldTypeEnum.Enum.MULTI_LINE;
    const isNumber = templateField.type === TemplateCustomFieldTypeEnum.Enum.NUMBER;
    const isSingleLine = templateField.type === TemplateCustomFieldTypeEnum.Enum.SINGLE_LINE;
    const maxConstraintDefault = isMultiLine
      ? MAX_VALUE_MULTI_LINE_LENGTH
      : isNumber
        ? MAX_VALUE_NUMBER
        : isSingleLine
          ? MAX_VALUE_SINGLE_LINE_LENGTH
          : MAX_VALUE_MULTI_LINE_LENGTH;

    if (maxConstraintDefault && (!currentMaxConstraint || currentMaxConstraint > maxConstraintDefault)) {
      currentMaxConstraint = maxConstraintDefault;
    }
    const fieldType = templateField.type;
    let message = '';
    const isHasError = !!constraint?.defaultValue?.message;
    const messagePrefix = `Invalid value.`;
    const subjectDescription = fieldType === TemplateCustomFieldTypeEnum.Enum.NUMBER ? `Value range` : `Value length`;
    if (isHasError) {
      message = messagePrefix;
    } else {
      return '';
    }
    if (minConstraint === null && currentMaxConstraint !== null) {
      message += `${subjectDescription} must not exceed ${currentMaxConstraint}`;
    } else if (minConstraint !== null && currentMaxConstraint === null) {
      message += `${subjectDescription} must be at least ${minConstraint}`;
    } else if (minConstraint !== null && currentMaxConstraint !== null) {
      message += `${subjectDescription} must be between ${minConstraint} and ${currentMaxConstraint}`;
    } else {
      message += `${subjectDescription} has an undefined range or length constraint.`;
    }
    return message;
  }, [errors.fields, index, maxConstraint, templateField.type, minConstraint]);
  useEffect(() => {
    handleLoadPickListFieldDataLst();
  }, [handleLoadPickListFieldDataLst]);

  useDebounceCallback(
    (opts?: FetchNextPageOptions): Promise<InfiniteQueryObserverResult<InfiniteData<ResponseData<Page<PicklistOption>>>>> => {
      return fetchNextPage(opts);
    },
    { delay: 500, flushOnUnmount: true },
  );

  switch (templateField.customFieldType) {
    case TemplateCustomFieldTypeEnum.enum.SINGLE_LINE:
      return (
        <Group className={`${styles.fieldGroup} `}>
          <FieldName field={templateField} required={required} />
          <Controller
            name={`fields.${index}.fieldConstraint.defaultValue`}
            control={control}
            render={({ field }) => (
              <KanbanInput
                className={styles.inputField}
                {...field}
                mb={0}
                value={field.value || ''}
                onBlur={() => {
                  field.onBlur?.();
                  handleValidate();
                }}
                rightSection={<FieldError message={getFieldError()} />}
              />
            )}
          />
        </Group>
      );
    case TemplateCustomFieldTypeEnum.enum.NUMBER:
      return (
        <Group className={`${styles.fieldGroup} `}>
          <FieldName field={templateField} required={required} />
          <Controller
            name={`fields.${index}.fieldConstraint.defaultValue`}
            control={control}
            render={({ field }) => (
              <KanbanNumberInput
                className={styles.inputField}
                allowDecimal
                allowNegative
                maxLength={10}
                {...field}
                mb={0}
                value={field.value || ''}
                onBlur={() => {
                  field.onBlur?.();
                  handleValidate();
                }}
                rightSection={<FieldError message={getFieldError()} />}
              />
            )}
          />
        </Group>
      );
    case TemplateCustomFieldTypeEnum.enum.BREAK:
    case TemplateCustomFieldTypeEnum.enum.RICH_TEXT:
      return (
        <Controller
          name={`fields.${index}.fieldConstraint.defaultValue`}
          control={control}
          render={({ field: controllerField }) => (
            <RichTextFieldRenderer field={templateField} controllerField={controllerField} required={required} />
          )}
        />
      );

    case TemplateCustomFieldTypeEnum.enum.DATE:
      return (
        <Group align='center' justify='space-between' pr='xs' gap={'0'} className={styles.fieldGroup}>
          <FieldName field={templateField} required={required} />

          <Controller
            name={`fields.${index}.fieldConstraint.defaultValue`}
            control={control}
            render={({ field: controllerField }) => (
              <DateTimePicker
                valueFormat={DATE_TIME_AMPM_FORMAT}
                rightSection={
                  <Group align='center' justify='flex-start'>
                    <IconCalendar style={{ width: 18, height: 18 }} stroke={1.5} />
                    <FieldError message={getFieldError()} />
                  </Group>
                }
                className={styles.inputField}
                value={!controllerField.value || isNaN(Date.parse(controllerField.value)) ? null : new Date(controllerField.value)}
                onChange={(date) => {
                  controllerField.onChange(date ? date.toISOString() : null);
                }}
              />
            )}
          />
        </Group>
      );

    case TemplateCustomFieldTypeEnum.enum.MULTI_LINE: {
      return (
        <Group align='center' justify='space-between' pr='xs' gap={'0'} className={styles.fieldGroup}>
          <FieldName field={templateField} required={required} />
          <Textarea
            className={styles.inputField}
            styles={{
              root: {
                height: '100%',
              },
              wrapper: {
                height: '100%',
              },
              input: {
                height: '100%',
              },
            }}
            {...register(`fields.${index}.fieldConstraint.defaultValue`)}
            rightSection={<FieldError message={getFieldError()} />}
          />
        </Group>
      );
    }

    case TemplateCustomFieldTypeEnum.enum.PICKLIST: {
      return (
        <Group align='center' justify='space-between' pr='xs' gap={'0'} className={styles.fieldGroup}>
          <FieldName field={templateField} required={required} />
          <Controller
            name={`fields.${index}.picklistOptions`}
            control={control}
            render={({ field: controllerField }) => (
              // templateField.isMultiple ? (
              <KanbanMultiSelect
                data={picklistFieldDataLst}
                searchable
                searchValue={searchPickList}
                onSearchChange={setSearchPickList}
                clearable
                className={styles.inputField}
                placeholder={templateField.isMultiple ? `Select option(s)` : 'Select option'}
                mb='0'
                value={controllerField.value?.filter((it) => picklistFieldDataLst.some((opt) => opt.value === it.value)).map((it) => it.value) || []}
                onChange={(values) => {
                  let results: PicklistOption[] = [];
                  if (!templateField.isMultiple) {
                    if (values.length === 0) {
                      results = [];
                    } else if (values.length === 2) {
                      results = [{ isDefault: true, value: values[1], position: index } as PicklistOption];
                    } else {
                      results = [{ isDefault: true, value: values[0], position: index } as PicklistOption];
                    }
                  } else {
                    results = values.map((it, index) => ({ isDefault: true, value: it, position: index }) as PicklistOption);
                  }
                  controllerField.onChange(results);
                }}
                styles={{
                  root: {
                    height: '100%',
                  },
                  wrapper: {
                    height: '100%',
                  },
                  input: {
                    height: '100%',
                    overflowY: 'auto',
                  },
                }}
                onFocus={() => {
                  setIsFocused(true);
                }}
                onBlur={() => setIsFocused(false)}
                error={errors.fields?.[index]?.picklistOptions?.message}
              />
            )}
          />
        </Group>
      );
    }

    default:
      return (
        <Group align='center' justify='space-between' pr='xs' gap={'0'} className={styles.fieldGroup}>
          <FieldName field={templateField} required={required} />
          <KanbanInput
            placeholder={`Nhập ${templateField.name?.toLowerCase()}...`}
            className={styles.inputField}
            {...register(`fields.${index}.fieldConstraint.defaultValue`)}
            rightSection={<FieldError message={getFieldError()} />}
          />
        </Group>
      );
  }
};

export default React.memo(FieldRenderer);
