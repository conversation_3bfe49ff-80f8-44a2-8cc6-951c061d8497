import React, { useEffect, useState, useMemo } from 'react';
import { Box, ComboboxItem } from '@mantine/core';
import { Handle, Position, useStore } from 'reactflow';
import { ActionIcon } from '@mantine/core';
import { KanbanText } from 'kanban-design-system';
import { IconMail } from '@tabler/icons-react';
import clsx from 'clsx';
import styles from './Node.module.scss';
import { ChangeStageType } from '@common/constants/ChageStageType';
import { ChangeFlowNotificationModel } from '@models/ChangeFlowModel';
import { ChangeStatusMinimal } from '@core/schema/ChangeStatus';
import { useDisclosure } from '@mantine/hooks';
import { NotificationModal } from '../notify/NotificationModal';
import { useNotificationForm } from '../notify/useNotificationForm';
import useMutate from '@core/hooks/useMutate';
import { NotificationsEmailTemplateApi } from '@api/NotificationsEmailTemplateApi';
import { EntityAction } from '@common/constants/EntityActionConstants';

interface StageNodeData {
  label: string;
  stageType: ChangeStageType;
  statusList: ChangeStatusMinimal[];
  notifications: ChangeFlowNotificationModel[];
  otherNodeOptions: ComboboxItem[];
  updateNode?: (nodeId: string, notifications: ChangeFlowNotificationModel[]) => void;
}

interface StageNodeProps {
  id: string;
  data: StageNodeData;
}

const StageNode: React.FC<StageNodeProps> = ({ data, id }) => {
  const edges = useStore((state) => state.edges);
  const [selectedStatus, setSelectedStatus] = useState<ChangeStatusMinimal | null>(null);
  const [opened, { close, open }] = useDisclosure(false);

  const {
    notifyError,
    notifyItems,
    prepareNotification,
    resetNotificationForm,
    selectedTemplate,
    setNotifyError,
    setNotifyItems,
    setSelectedTemplate,
  } = useNotificationForm();

  const handleOpenModal = (status: ChangeStatusMinimal) => {
    const noti = data.notifications?.find((v) => v.statusId === status.id.toString());
    setSelectedStatus(status);
    setSelectedTemplate(noti?.emailTemplateId ?? null);

    // Only update notifyItems if content actually changed
    const newNotifyItems = [...(noti?.notifyNode ?? []), ...(noti?.notifyTo ?? [])];
    setNotifyItems((prev) => {
      if (prev.length !== newNotifyItems.length || !prev.every((item, index) => item === newNotifyItems[index])) {
        return newNotifyItems;
      }
      return prev;
    });

    open();
  };

  useEffect(() => {
    if (!opened) {
      setSelectedStatus(null);
      resetNotificationForm();
    }
  }, [opened, resetNotificationForm]);

  const notificationStatusMap = useMemo(() => {
    const map = new Map<string, boolean>();
    data.statusList.forEach((status) => {
      const hasNotification = !!data.notifications?.find((v) => v.statusId === status.id.toString() && v.emailTemplateId);
      map.set(status.id.toString(), hasNotification);
    });
    return map;
  }, [data.notifications, data.statusList]);

  const currentNotification = useMemo(() => {
    return selectedStatus ? data.notifications?.find((v) => v.statusId === selectedStatus.id.toString()) : undefined;
  }, [selectedStatus, data.notifications]);

  const updateNotificationsAndClose = (statusId: string) => {
    const result = prepareNotification(data.otherNodeOptions, statusId);
    if (!result) {
      return;
    }

    const updated = result.emailTemplateId
      ? [...(data.notifications ?? []).filter((n) => n.statusId !== result.statusId), result]
      : (data.notifications ?? []).filter((n) => n.statusId !== result.statusId);

    data.updateNode?.(id, updated);
    close();
  };

  const { mutate: checkTemplateExists } = useMutate(NotificationsEmailTemplateApi.findById, {
    successNotification: { enable: false },
    onSuccess: (response) => {
      if (response.data && selectedStatus) {
        updateNotificationsAndClose(selectedStatus.id.toString());
      }
    },
  });

  const handleSave = () => {
    if (!selectedStatus) {
      return;
    }

    if (selectedTemplate) {
      checkTemplateExists(selectedTemplate);
    } else {
      updateNotificationsAndClose(selectedStatus.id.toString());
    }
  };

  return (
    <>
      <Box className={styles.box}>
        <Box className={styles.labelBox}>{data.label}</Box>
        <Box p='sm'>
          {data.statusList.map((status) => (
            <Box key={`${id}-${status.id}`} className={styles.body} style={{ position: 'relative' }}>
              <Handle
                isConnectableStart={false}
                type='target'
                position={Position.Left}
                id={`${status.id}`}
                className={clsx(styles.handle, styles.left)}
              />
              <KanbanText mr='lg' ml='xs'>
                {status.name}
              </KanbanText>
              <ActionIcon
                variant='subtle'
                color={notificationStatusMap.get(status.id.toString()) ? 'yellow' : 'gray'}
                size='md'
                className={styles.mailBox}
                style={{
                  transform: 'translateY(-50%)',
                }}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleOpenModal(status);
                }}>
                <IconMail size={18} />
              </ActionIcon>
              <Handle
                type='source'
                position={Position.Right}
                id={`${status.id}`}
                isValidConnection={(conn) => conn.source !== conn.target && !edges.some((e) => e.source === id && e.sourceHandle === `${status.id}`)}
                className={clsx(styles.handle, styles.right)}
              />
            </Box>
          ))}
        </Box>
      </Box>

      <NotificationModal
        opened={opened}
        onClose={close}
        onSave={handleSave}
        initialMode={currentNotification ? EntityAction.EDIT : EntityAction.CREATE}
        variableOptions={data.otherNodeOptions}
        notification={currentNotification}
        selectedTemplate={selectedTemplate}
        setSelectedTemplate={setSelectedTemplate}
        notifyItems={notifyItems}
        setNotifyItems={setNotifyItems}
        notifyError={notifyError}
        setNotifyError={setNotifyError}
      />
    </>
  );
};

export default StageNode;
