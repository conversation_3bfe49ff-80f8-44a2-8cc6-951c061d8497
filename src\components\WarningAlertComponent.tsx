import { Alert, Stack } from '@mantine/core';
import { IconAlertTriangle, IconCircleFilled } from '@tabler/icons-react';
import { KanbanText } from 'kanban-design-system';
import React from 'react';

type WarningAlertComponentProps = {
  mainEntity: string;
  dependencyEntity: string;
  dependencies: string[];
  color?: string;
  isDeleted?: boolean;
  action?: string;
};
const MAX_DEPENDENCIES = 10;

export const WarningAlertComponent: React.FC<WarningAlertComponentProps> = ({
  action = 'delete',
  color = 'yellow',
  dependencies,
  dependencyEntity,
  isDeleted = false,
  mainEntity,
}) => {
  if (dependencies.length === 0) {
    return null;
  }

  const limitedDependencies = dependencies.slice(0, MAX_DEPENDENCIES);
  const hasMoreDependencies = dependencies.length > MAX_DEPENDENCIES;

  return (
    <Alert my='md' variant='light' color={color} icon={<IconAlertTriangle />}>
      <KanbanText c={color} ml='sm'>
        {mainEntity} is currently being used in the following {dependencyEntity}:
        <Stack ml='sm' gap='xs'>
          {limitedDependencies.map((a) => (
            <KanbanText key={a}>
              <IconCircleFilled size='0.4rem' /> {a}
            </KanbanText>
          ))}
          {hasMoreDependencies && (
            <KanbanText>
              <IconCircleFilled size='0.4rem' /> ...
            </KanbanText>
          )}
        </Stack>
        {isDeleted && ` You must ${action} all ${dependencyEntity} before continuing.`}
      </KanbanText>
    </Alert>
  );
};

export default WarningAlertComponent;
