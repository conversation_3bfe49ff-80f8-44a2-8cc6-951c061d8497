import { callRequest } from '@core/api';
import { ResponseData } from '@core/schema/Common';
import { call, put, select, takeEvery } from 'redux-saga/effects';
import { UserPreference } from '@core/schema/UserPreference';
import { UserPreferenceApi } from '@api/UserPreferenceApi';
import { CreateOrUpdatePayload, currentUserPreferenceSlice, getCurrentUserPreference, UserPreferenceState } from '@slices/UserPreferenceSlice';
import { isEmpty } from 'lodash';

function* fetchData() {
  yield put(
    currentUserPreferenceSlice.actions.setValue({
      isFetching: true,
      userPreferences: {},
    }),
  );
  try {
    const userPreferences: ResponseData<UserPreference[]> = yield call(() => callRequest(UserPreferenceApi.getUserPreference()));
    if (Array.isArray(userPreferences.data)) {
      const userPreferencesMap: Record<string, object> = Object.fromEntries(userPreferences.data.map((pref) => [pref.keyConfig, pref]));
      yield put(
        currentUserPreferenceSlice.actions.setValue({
          isFetching: false,
          userPreferences: userPreferencesMap,
        }),
      );
    }
  } catch (ex) {
    yield put(
      currentUserPreferenceSlice.actions.setValue({
        isFetching: false,
        userPreferences: {},
      }),
    );
  }
}

function* saveUserPreferenceEffect(data: CreateOrUpdatePayload) {
  try {
    const state: UserPreferenceState = yield select(getCurrentUserPreference);
    const currentPrefs = state.userPreferences || {};
    const key = String(data.payload.data.keyConfig);
    if (isEmpty(currentPrefs) || (currentPrefs[key] as UserPreference).content !== data.payload.data.content) {
      yield call(() => callRequest(UserPreferenceApi.upsert(data.payload.data)));
      const updatedPrefs = {
        ...currentPrefs,
        [key]: data.payload.data,
      };

      yield put(
        currentUserPreferenceSlice.actions.setValue({
          isFetching: false,
          userPreferences: updatedPrefs,
        }),
      );
    }
  } catch (err) {
    return;
  }
}

export function* currentUserPreferenceSaga() {
  yield takeEvery(currentUserPreferenceSlice.actions.fetchData, fetchData);
  yield takeEvery(currentUserPreferenceSlice.actions.saveUserPreference, saveUserPreferenceEffect);
}
