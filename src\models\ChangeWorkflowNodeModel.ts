import { z } from 'zod';
import { ChangeApplicationTypeEnum } from '@common/constants/ChangeApplicationConstants';
import { COMMON_DESCRIPTION_MAX_LENGTH, COMMON_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { INPUT_REQUIRE } from '@core/message/MesageConstant';

const BaseAuthenticationSchema = z.object({
  url: z.string().trim().min(1, { message: INPUT_REQUIRE }).max(COMMON_MAX_LENGTH),
});

// Schema for basic authentication (username and password)
const BasicAuthenticationSchema = z.object({
  userName: z.string().trim().min(1, { message: INPUT_REQUIRE }).max(COMMON_MAX_LENGTH),
  password: z.string().trim().min(1, { message: INPUT_REQUIRE }).max(COMMON_MAX_LENGTH),
});

// Schema for API key authentication
const Api<PERSON>eyAuthenticationSchema = z.object({
  apiKey: z.string().trim().min(1, { message: INPUT_REQUIRE }).max(COMMON_MAX_LENGTH),
});

// Combine base schema with specific authentication schemas
export const JenkinsAuthenticationSchema = BaseAuthenticationSchema.and(BasicAuthenticationSchema);
export const WLAAuthenticationSchema = BaseAuthenticationSchema.and(ApiKeyAuthenticationSchema);

export const ChangeWorkflowNodeModelSchema = z
  .object({
    id: z.number().nullish(),
    nodeName: z.string().trim().min(1, { message: INPUT_REQUIRE }).max(COMMON_MAX_LENGTH),
    description: z.string().trim().max(COMMON_DESCRIPTION_MAX_LENGTH).nullish(),
    application: z.nativeEnum(ChangeApplicationTypeEnum).optional(),
    authentication: z.string().nullish(),
    isChange: z.boolean().nullish(),
    config: z.string().nullish(),
  })
  .superRefine((data, ctx) => {
    const { application, authentication, id, isChange } = data;
    if (!application) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['application'],
        message: INPUT_REQUIRE,
      });
    }
    if (application === ChangeApplicationTypeEnum.JENKINS || application === ChangeApplicationTypeEnum.WLA) {
      // Trong chế độ tạo mới (!id), authentication bắt buộc và phải hợp lệ
      if (!id) {
        if (!authentication) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['authentication'],
            message: 'Authentication data is required',
          });
          return;
        }

        try {
          const auth = JSON.parse(authentication);
          const schema = application === ChangeApplicationTypeEnum.JENKINS ? JenkinsAuthenticationSchema : WLAAuthenticationSchema;
          const result = schema.safeParse(auth);

          if (!result.success) {
            result.error.issues.forEach((issue) => {
              ctx.addIssue({
                ...issue,
                path: ['authentication', ...issue.path],
              });
            });
          }
        } catch {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['authentication'],
            message: 'Invalid authentication JSON data',
          });
        }
      }
      // Trong chế độ chỉnh
      else {
        // Nếu isChange là true, authentication bắt buộc và phải hợp lệ
        if (isChange) {
          if (!authentication) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              path: ['authentication'],
              message: 'Authentication data is required',
            });
            return;
          }

          try {
            const auth = JSON.parse(authentication);
            const schema = application === ChangeApplicationTypeEnum.JENKINS ? JenkinsAuthenticationSchema : WLAAuthenticationSchema;
            const result = schema.safeParse(auth);

            if (!result.success) {
              result.error.issues.forEach((issue) => {
                ctx.addIssue({
                  ...issue,
                  path: ['authentication', ...issue.path],
                });
              });
            }
          } catch {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              path: ['authentication'],
              message: 'Invalid authentication JSON data',
            });
          }
        }
        // Nếu isChange là false hoặc không được đặt, bỏ qua xác thực
      }
    }
  });

export type ChangeWorkflowNodeModel = z.infer<typeof ChangeWorkflowNodeModelSchema>;
export type JenkinsAuthenticationModel = z.infer<typeof JenkinsAuthenticationSchema>;
export type WLAAuthenticationModel = z.infer<typeof WLAAuthenticationSchema>;
