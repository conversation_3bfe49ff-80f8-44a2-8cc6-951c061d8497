import { z } from 'zod';
import { AuditSchema } from './Common';
import { ChangeRequestApprovalSchema } from './ChangeRequestApproval';
export const ChangeRequestRoleUserSchema = z
  .object({
    id: z.number().nullish(),
    changeRequestRoleId: z.number().nullish(),
    username: z.string().nullish(),
    displayUsername: z.string().nullish(),
    usernameActive: z.boolean().nullish(),
    cabGroup: z.number(),
    cabGroupOrder: z.number(),
    changeRequestId: z.number().nullish(),
    //change request approval
    changeRequestApproval: ChangeRequestApprovalSchema.nullish(),
    changeNodeName: z.string().nullish(),
    changeRequestWorkflowDetailId: z.number().nullish(),
  })
  .merge(AuditSchema);

export type ChangeRequestRoleUser = z.infer<typeof ChangeRequestRoleUserSchema>;
