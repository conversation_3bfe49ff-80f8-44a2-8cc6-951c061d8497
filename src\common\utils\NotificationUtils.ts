import { NotificationData, notifications } from '@mantine/notifications';

export const NotificationSuccess = (data: NotificationData) => {
  notifications.show({
    color: 'primary',
    bg: 'primary.1',
    title: 'Success',
    withCloseButton: true,
    ...data,
  });
};
export const NotificationError = (data: NotificationData) => {
  notifications.show({
    color: 'red',
    bg: 'red.1',
    title: 'Error',
    withCloseButton: true,
    ...data,
  });
};
