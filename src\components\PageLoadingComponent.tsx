import { Text } from '@mantine/core';
import { getPageLoading } from 'store/slices/PageLoadingSlice';
import React from 'react';
import { useSelector } from 'react-redux';
import styled from 'styled-components';
import { ZIndexLoading } from '@common/constants/ZIndexConstants';
import { KanbanLoading } from 'kanban-design-system';

const Container = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgb(255 255 255 / 80%);
  z-index: ${ZIndexLoading};
  justify-content: center;
  align-items: center;
  display: flex;
`;
const Wrapper = styled.div`
  text-align: center;
`;

export const PageLoadingComponent = () => {
  const pageLoading = useSelector(getPageLoading);
  return pageLoading.value > 0 ? (
    <Container>
      <Wrapper>
        <KanbanLoading></KanbanLoading>
        <Text>Loading...</Text>
      </Wrapper>
    </Container>
  ) : (
    <></>
  );
};
export default PageLoadingComponent;
