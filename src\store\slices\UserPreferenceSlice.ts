import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { type RootStoreType } from '@store';
import { PayloadActionWithCallback } from '@models/redux/ReduxPayloadAction';
import { UserPreference } from '@core/schema/UserPreference';

export type UserPreferenceState = {
  isFetching: boolean;
  userPreferences: Record<string, object>;
};

const initialState: UserPreferenceState = {
  isFetching: false,
  userPreferences: {},
};

export type CreateOrUpdatePayload = PayloadActionWithCallback<UserPreference>;

export const currentUserPreferenceSlice = createSlice({
  name: 'currentUserPreference',
  initialState,
  reducers: {
    fetchData() {},
    setValue(_state, action: PayloadAction<UserPreferenceState>) {
      return action.payload;
    },
    saveUserPreference(_state, _action: CreateOrUpdatePayload) {},
  },
});

export const getCurrentUserPreference = (store: RootStoreType) => store.currentUserPreference;
