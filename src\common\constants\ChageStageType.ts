import { z } from 'zod';

export const ChangeStageTypeEnum = z.enum(['SUBMISSION_PLANNING', 'APPROVAL', 'IMPLEMENTATION', 'REVIEW_CLOSE']);
export type ChangeStageType = z.infer<typeof ChangeStageTypeEnum>;

export const ExtendedChangeStageTypeEnum = z.enum([...ChangeStageTypeEnum.options, 'CAB_NODE', 'APPROVAL_NODE']);
export type ExtendedChangeStageType = z.infer<typeof ExtendedChangeStageTypeEnum>;

export const CHANGE_STAGE_LABEL: Record<ChangeStageType, string> = {
  SUBMISSION_PLANNING: 'Submission & Planning',
  APPROVAL: 'Approval',
  IMPLEMENTATION: 'Implementation',
  REVIEW_CLOSE: 'Review & Close',
};
