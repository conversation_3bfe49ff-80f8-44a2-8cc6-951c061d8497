import { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Stack, Collapse, Box, Image } from '@mantine/core';
import { IconChevronDown } from '@tabler/icons-react';
import React from 'react';
import { KanbanText } from 'kanban-design-system';
import logo from './logo.svg';
import logoMB from './logoMB.svg';
import classes from './Sidebar.module.scss';
import { sideBarItem, SideBarSelectionConfigType } from './SideBarSelectionConfigType';
import { ProtectedComponent } from '@core/auth/hocs/ProtectedComponent';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import { useSelector } from 'react-redux';
import { User } from '@core/schema/User';

interface RenderNavLinkProps {
  config: SideBarSelectionConfigType;
  level?: number;
  isOpen: boolean;
  isActive: boolean;
  onClick?: () => void;
}

const RenderNavLink: React.FC<RenderNavLinkProps> = ({ config, isActive, isOpen, onClick }) => {
  const navigate = useNavigate();
  const hasChildren = !!config.children;

  return (
    <Box
      className={classes.navLinkWrapper}
      onClick={(e) => {
        e.stopPropagation();
        if (hasChildren) {
          onClick?.();
        } else {
          navigate(config.path);
        }
      }}>
      <KanbanText className={`${classes.navLink} ${isActive ? classes.active : ''}`}>
        {config.icon}
        {isOpen && <KanbanText style={{ fontWeight: '600' }}>{config.name}</KanbanText>}
      </KanbanText>
    </Box>
  );
};

function MenuItem({
  config,
  isOpen,
  level = 0,
  location,
  openSubmenus,
  toggleSubmenu,
  userInfo,
}: {
  config: SideBarSelectionConfigType;
  isOpen: boolean;
  openSubmenus: string[];
  toggleSubmenu: (path: string) => void;
  location: ReturnType<typeof useLocation>;
  level?: number;
  userInfo?: User | null;
}) {
  const hasChildren = !!config.children;

  const isPathActive = (config: SideBarSelectionConfigType, pathname: string): boolean => {
    if (pathname === config.path) {
      return true;
    }
    return config.children?.some((child) => isPathActive(child, pathname)) || false;
  };
  const isActive = isPathActive(config, location.pathname);

  const isExpanded = openSubmenus.includes(config.path);

  return (
    <Box className={classes.menuItemWrapper}>
      <Box
        className={`${classes.menuItem} ${isActive ? classes.active : ''}`}
        data-level={level}
        onClick={() => hasChildren && toggleSubmenu(config.path)}>
        <RenderNavLink config={config} level={level} isOpen={isOpen} isActive={isActive} onClick={() => hasChildren && toggleSubmenu(config.path)} />
        {hasChildren && isOpen && <IconChevronDown size={14} style={{ transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)' }} />}
      </Box>

      {hasChildren && isOpen && (
        <Collapse in={isExpanded}>
          <Stack gap='0' className={classes.submenu} data-level={level}>
            {config.children?.map((child, index) => (
              <ProtectedComponent
                requirePermissions={child.requiredPermissions || []}
                userPermissions={userInfo?.aclPermissions || []}
                key={index}
                hiddenOnUnSatisfy
                errorElement={<></>}>
                <MenuItem
                  key={index}
                  config={child}
                  isOpen={isOpen}
                  openSubmenus={openSubmenus}
                  toggleSubmenu={toggleSubmenu}
                  location={location}
                  level={level + 1}
                  userInfo={userInfo}
                />
              </ProtectedComponent>
            ))}
          </Stack>
        </Collapse>
      )}
    </Box>
  );
}

export const Sidebar: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [openSubmenus, setOpenSubmenus] = useState<string[]>([]);
  const location = useLocation();
  const userInfo = useSelector(getCurrentUser).userInfo;

  const toggleSubmenu = (path: string) => {
    setOpenSubmenus((prev) => (prev.includes(path) ? prev.filter((p) => p !== path) : [...prev, path]));
  };

  return (
    <Stack
      h='100vh'
      w={isOpen ? 250 : 60}
      onMouseEnter={() => setIsOpen(true)}
      onMouseLeave={() => setIsOpen(false)}
      className={classes.sidebar}
      style={{ overflowY: 'auto' }}>
      <Box className={classes.logoContainer}>
        <Image src={isOpen ? logoMB : logo} width={40} height={40} style={{ transition: 'opacity 0.3s' }} />
      </Box>
      {sideBarItem.map((item, index) => (
        //ft/role : side bar authrize
        <React.Fragment key={index}>
          <ProtectedComponent
            requirePermissions={item.requiredPermissions || []}
            userPermissions={userInfo?.aclPermissions || []}
            allMatchPermissions={item.allMatchPermissions || true}
            key={index}
            hiddenOnUnSatisfy
            errorElement={<></>}>
            <MenuItem
              config={item}
              isOpen={isOpen}
              openSubmenus={openSubmenus}
              toggleSubmenu={toggleSubmenu}
              location={location}
              userInfo={userInfo}
            />
          </ProtectedComponent>
        </React.Fragment>
      ))}
    </Stack>
  );
};
