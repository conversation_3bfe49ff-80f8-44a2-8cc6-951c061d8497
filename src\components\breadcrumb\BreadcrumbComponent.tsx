import { Breadcrumbs, Anchor, Text, Tooltip } from '@mantine/core';
import React, { useMemo } from 'react';
import { useLocation, Link } from 'react-router-dom';
import { IconHome } from '@tabler/icons-react';
import { useAppSelector } from '@store';
import { getBreadcrumbEntityName } from '@slices/BreadcrumbSlice';
import { isId, capitalizeFirst, truncateText } from '@common/utils/StringUtils';
import { LABEL_MAP, IGNORED_SEGMENTS_MAP } from '@core/configs/Configs';

const getIgnoredSegments = (pathname: string): string[] => {
  const matched = Object.keys(IGNORED_SEGMENTS_MAP).find((prefix) => pathname.startsWith(prefix));
  return matched ? IGNORED_SEGMENTS_MAP[matched] : [];
};

const buildBreadcrumb = (pathname: string, action: string | null, name?: string) => {
  // Return an empty array for a root path
  if (pathname === '/') {
    return [];
  }

  const segments = pathname.split('/').filter(Boolean);
  const ignoredSegments = getIgnoredSegments(pathname);

  // Initialize crumbs with the home icon
  const crumbs: { label: React.ReactNode; to: string }[] = [
    {
      label: <IconHome />,
      to: '/',
    },
  ];

  // Build path segments
  let currentPath = '';
  segments.forEach((segment, index) => {
    currentPath += `/${segment}`;

    // Skip ignored segments and last ID
    if (ignoredSegments.includes(segment) || (isId(segment) && index === segments.length - 1)) {
      return;
    }

    crumbs.push({
      label: LABEL_MAP[segment] || (isId(segment) ? segment : capitalizeFirst(segment)),
      to: currentPath,
    });
  });

  // Add action crumb if needed
  if (action) {
    const nameSuffix = name && (
      <Tooltip label={name} zIndex={9999} withArrow>
        <Text fw={500} span>
          {truncateText(name)}
        </Text>
      </Tooltip>
    );

    crumbs.push({
      label: nameSuffix ? (
        <>
          {capitalizeFirst(action)} {nameSuffix}
        </>
      ) : (
        capitalizeFirst(action)
      ),
      to: pathname,
    });
  }

  return crumbs;
};

const BreadcrumbComponent = () => {
  const { pathname, search } = useLocation();
  const action = new URLSearchParams(search).get('action');
  const name = useAppSelector(getBreadcrumbEntityName).entityName;

  const crumbs = useMemo(() => buildBreadcrumb(pathname, action, name), [action, name, pathname]);

  interface Crumb {
    label: React.ReactNode;
    to: string;
  }

  const LastCrumb = ({ label, to }: Crumb) => (
    <Text key={to} fw={500}>
      {label}
    </Text>
  );

  const LinkedCrumb = ({ label, to }: Crumb) => (
    <Anchor component={Link} to={to} key={to}>
      {label}
    </Anchor>
  );

  return (
    <Breadcrumbs separator='›'>
      {crumbs.map((crumb, index) => (index === crumbs.length - 1 ? <LastCrumb {...crumb} key={index} /> : <LinkedCrumb {...crumb} key={index} />))}
    </Breadcrumbs>
  );
};

export default BreadcrumbComponent;
