import _ from 'lodash';

export type OmitAriaProps<T> = {
  [K in keyof T as Exclude<K, `aria-${string}`>]: T[K];
};
export type OmitDashProps<T> = {
  [K in keyof T as K extends `${infer _First}-${infer _Rest}` ? never : K]: T[K];
};

export type ExtractProps<TComponentOrTProps> = TComponentOrTProps extends React.ComponentType<infer TProps> ? TProps : TComponentOrTProps;

export type OmitCommonPropsFromMantine<T> = OmitDashProps<T>;

export function getQueryParams() {
  const urlParams = new URLSearchParams(window.location.search);
  const keys = urlParams.keys();
  let key = keys.next().value;
  const queryParams: Record<string, string> = {};
  while (key) {
    queryParams[key] = urlParams.get(key) as string;
    key = keys.next().value;
  }
  return queryParams;
}

export function convertObjectToQueryParams(object?: Record<string, any>): string {
  if (!_.isNil(object)) {
    const paramArray: string[] = _.map(_.keys(object), (key: string) => {
      return `${encodeURIComponent(key)}=${encodeURIComponent(object[key])}`;
    });
    return `?${_.join(paramArray, '&')}`;
  } else {
    return '';
  }
}

export function isValidURL(url: string): boolean {
  return (
    url.match(
      // eslint-disable-next-line no-useless-escape
      /\(?(?:(http|https|ftp|mailto|tel):\/\/)?(?:((?:[^\W\s]|\.|-|[:]{1})+)@{1})?((?:www.)?(?:[^\W\s]|\.|-)+[\.][^\W\s]{2,4}|localhost(?=\/)|\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})(?::(\d*))?([\/]?[^\s\?]*[\/]{1})*(?:\/?([^\s\n\?\[\]\{\}\#]*(?:(?=\.)){1}|[^\s\n\?\[\]\{\}\.\#]*)?([\.]{1}[^\s\?\#]*)?)?(?:\?{1}([^\s\n\#\[\]]*))?([\#][^\s\n]*)?\)?/g,
    ) !== null
  );
}

export function camelToHyphenCase(str: string) {
  return str.replace(/[A-Z]/g, (match) => `-${match.toLowerCase()}`).replace(/^-/, '');
}

export function safetyParseObjectFromJson(json: string): unknown | undefined {
  try {
    return JSON.parse(json);
  } catch {
    return undefined;
  }
}

export function trimStringFields<T extends Record<string, any>>(obj: T, convertNullToEmpty = true): T {
  return Object.fromEntries(
    Object.entries(obj).map(([key, value]) => {
      if (typeof value === 'string') {
        return [key, value.trim()];
      }
      if (convertNullToEmpty && (value === null || value === undefined)) {
        return [key, ''];
      }
      return [key, value];
    }),
  ) as T;
}
