import { CustomFieldTypeEnum } from '@common/constants/CustomFieldType';
import { z } from 'zod';
import { QueryRuleGroupTypeSchema } from './RuleGroupCondition';
import { AuditSchema } from './Common';

export const FieldConstraintSchema = z.object({
  defaultValue: z.string().nullish(),
  min: z.number().int().nullish(),
  max: z.number().int().nullish(),
});
export type FieldConstraintModel = z.infer<typeof FieldConstraintSchema>;

export const PicklistOptionSchema = z.object({
  value: z.string(),
  isDefault: z.boolean(),
  position: z.number().int(),
});

export const CustomFieldConditionSchema = z.object({
  tableReference: z.string(),
  columnReference: z.string(),
  ruleGroup: QueryRuleGroupTypeSchema,
});
export type CustomFieldCondition = z.infer<typeof CustomFieldConditionSchema>;

export const CustomFieldSchema = z
  .object({
    id: z.number(),
    name: z.string(),
    isMultiple: z.boolean().nullish(),
    isReference: z.boolean().nullish(),
    description: z.string().nullish(),
    type: CustomFieldTypeEnum,
    condition: CustomFieldConditionSchema.nullish(),
    fieldConstraint: FieldConstraintSchema.nullish(),
    picklistOptions: z.array(PicklistOptionSchema).nullish(),
  })
  .merge(AuditSchema);
export type CustomField = z.infer<typeof CustomFieldSchema>;

export const CustomFieldMentionSchema = z.object({
  id: z.string(),
  name: z.string(),
});
export const CustomFieldMentionListSchema = z.array(CustomFieldMentionSchema);
