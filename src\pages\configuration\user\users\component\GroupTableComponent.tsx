import { KanbanCheckbox, KanbanText } from 'kanban-design-system';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanTable, KanbanTableProps, TableAffactedSafeType } from 'kanban-design-system';
import { Box, Flex, Group, Pill } from '@mantine/core';
import equal from 'fast-deep-equal';
import React, { useState, useMemo, useEffect, useCallback } from 'react';

import useFetch from '@core/hooks/useFetch';
import customStyled from '@resources/styles/Common.module.scss';

import { FilterTextInput } from '@components/AmtFilterTextInput';
import { GroupUserModel } from '@models/GroupRoleUserModel';
import { GroupUser } from '@core/schema/Group';
import { DEFAULT_ITEM_CHECK, ItemCheck, ItemCheckComponentWithUsernameProps } from '@core/schema/Common';
import { initOrUpdatedFilterSearch } from '@common/utils/TableUtils';
import { UserApi } from '@api/UserApi';

const getKey = (group: number) => {
  return group;
};

export const GroupUserTableComponent: React.FC<ItemCheckComponentWithUsernameProps<GroupUserModel>> = ({
  selecteds,
  setSelecteds,
  setToDeletes,
  setToInserts,
  toDeletes,
  toInserts,
  username,
}) => {
  const [totalRecords, setTotalRecords] = useState(0);
  const [listData, setListData] = useState<GroupUser[]>([]);

  const [tableAffected, setTableAffectedChange] = useState<TableAffactedSafeType>();

  const [searchValue, setSearchValue] = useState<string>('');
  const [inputValue, setInputValue] = useState<string>(searchValue || '');
  const [oldSelecteds, setOldSelecteds] = useState<ItemCheck<GroupUserModel>>(DEFAULT_ITEM_CHECK);

  const { data: tableDataResponse } = useFetch(
    UserApi.findAllGroupsWithSelectionByUsername(username || '0', initOrUpdatedFilterSearch(tableAffected, searchValue)),
    { enabled: !!tableAffected },
  );

  const sortedInserts = useMemo(() => {
    return Object.values(toInserts).sort((a, b) => a.groupName.toLowerCase().localeCompare(b.groupName.toLowerCase()));
  }, [toInserts]);

  const sortedDeletes = useMemo(() => {
    return Object.values(toDeletes).sort((a, b) => a.groupName.toLowerCase().localeCompare(b.groupName.toLowerCase()));
  }, [toDeletes]);

  useEffect(() => {
    if (tableDataResponse?.data?.content) {
      const results = tableDataResponse.data.content;
      setListData(results);
      setTotalRecords(tableDataResponse.data.totalElements);

      setSelecteds((prev) => {
        const updateMap = { ...prev };
        [...results.filter((it) => it.userName && !(getKey(it.groupId) in updateMap) && !(getKey(it.groupId) in toDeletes))].forEach((item) => {
          updateMap[getKey(item.groupId)] = item;
        });
        return updateMap;
      });

      setOldSelecteds((prev) => {
        const updateMap = { ...prev };
        [...results.filter((it) => it.userName)].forEach((item) => {
          updateMap[getKey(item.groupId)] = item;
        });
        return updateMap;
      });
    }
  }, [tableDataResponse?.data, setSelecteds, toDeletes]);

  useEffect(() => {
    setInputValue(searchValue);
  }, [searchValue]);

  const handleSearch = useCallback(
    (value?: string) => {
      const finalValue = value ?? inputValue;
      setSearchValue(finalValue);
      setTableAffectedChange((prev) => initOrUpdatedFilterSearch(prev, finalValue));
    },
    [inputValue, setSearchValue],
  );

  const handleCheckAndUnCheck = useCallback(
    (rowData: GroupUser, checked: boolean) => {
      const key = getKey(rowData.groupId);
      if (key in oldSelecteds) {
        setToDeletes((prev) => {
          const updateMap = { ...prev };
          checked ? delete updateMap[key] : (updateMap[key] = rowData);
          return updateMap;
        });
      } else {
        setToInserts((prev) => {
          const updateMap = { ...prev };
          checked ? (updateMap[key] = rowData) : delete updateMap[key];
          return updateMap;
        });
      }
    },
    [oldSelecteds, setToDeletes, setToInserts],
  );

  const tableProps: KanbanTableProps<GroupUser> = useMemo(() => {
    const tblProps: KanbanTableProps<GroupUser> = {
      title: '',
      columns: [
        {
          name: 'groupSelected',
          width: '5%',
          title: '',
          customRender: (_, row: GroupUser) => {
            const key = getKey(row.groupId);
            const isChecked = (key in selecteds && !(key in toDeletes)) || key in toInserts; // ✅ Dùng selecteds thay vì row.userName
            return <KanbanCheckbox checked={isChecked} onChange={(e) => handleCheckAndUnCheck(row, e.target.checked)} />;
          },
        },
        {
          name: 'groupName',
          title: 'Group Name',
        },
        {
          name: 'description',
          title: 'Description',
        },
      ],
      data: listData,
      serverside: {
        totalRows: totalRecords,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffected, dataSet)) {
            setTableAffectedChange((prev) => (prev ? { ...dataSet, advancedFilterMapping: prev.advancedFilterMapping } : { ...dataSet }));
          }
        },
      },
      pagination: {
        enable: true,
      },
    };
    return tblProps;
  }, [listData, totalRecords, selecteds, toDeletes, toInserts, handleCheckAndUnCheck, tableAffected]);

  return (
    <Box>
      <HeaderTitleComponent
        title=''
        leftSection={
          <Flex dir='row' gap='xs' align={'center'}>
            <FilterTextInput
              onClear={() => {
                setInputValue('');
                handleSearch('');
              }}
              placeholder='Group Name'
              value={inputValue}
              onBlur={() => handleSearch()}
              onChange={(val) => setInputValue(val.target.value)}
            />
          </Flex>
        }
      />

      {sortedInserts.length > 0 && (
        <Group mb='xs'>
          <KanbanText fw='bold'>Insert:</KanbanText>
          {sortedInserts.map((item) => (
            <Pill key={item.groupId} bg='var(--mantine-color-green-1)' size='md'>
              {item.groupName}
            </Pill>
          ))}
        </Group>
      )}

      {sortedDeletes.length > 0 && (
        <Group mb='xs'>
          <KanbanText fw='bold'>Delete:</KanbanText>
          {sortedDeletes.map((item) => (
            <Pill key={item.groupId} bg='var(--mantine-color-red-1)' size='md'>
              {item.groupName}
            </Pill>
          ))}
        </Group>
      )}

      <Box mt='xs' className={customStyled.elementBorder}>
        <KanbanTable {...tableProps} title='' />
      </Box>
    </Box>
  );
};

export default GroupUserTableComponent;
