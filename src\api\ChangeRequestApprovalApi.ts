import { BaseURL } from '@common/constants/BaseUrl';
import { createResponseSchema, ResponseData } from '@core/schema/Common';
import { z } from 'zod';
import { ChangeRequestApprovalResult, ChangeRequestApprovalResultSchema } from '@core/schema/ChangeRequestApproval';
import { RequestConfig } from '@core/api';

export class ChangeRequestApprovalApi {
  static findAllResults(changeRequestApprovalId: number): RequestConfig<ResponseData<ChangeRequestApprovalResult[]>> {
    return {
      url: `${BaseURL.changeRequestApproval}/${changeRequestApprovalId}`,
      method: 'GET',
      schema: createResponseSchema(z.array(ChangeRequestApprovalResultSchema)),
    };
  }
}
