import React, { useMemo, useState } from 'react';
import { Controller, useFieldArray, useFormContext } from 'react-hook-form';
import { ChangeRequestRole, ChangeRequestRoleList } from '@core/schema/ChangeRequestRole';
import { KanbanAccordion, KanbanTabs, KanbanTabsType, KanbanText } from 'kanban-design-system';
import { EntityAction } from '@common/constants/EntityActionConstants';
import { ChangeRequestApprovalMode } from '@common/constants/ChangeRequestApprovalConstants';
import NodeCabGroupList from './NodeCabGroupList';
import { ChangeStageType } from '@common/constants/ChageStageType';

interface NodeCabWorkflowTabsProps {
  role: ChangeRequestRole;
  roleIdx: number;
  mode: EntityAction;
  modeApproval: ChangeRequestApprovalMode;
  refetchChangeRequestRoles: () => void;
  stage?: ChangeStageType;
}

const NodeCabWorkflowTabs: React.FC<NodeCabWorkflowTabsProps> = ({ mode, modeApproval, refetchChangeRequestRoles, role, roleIdx, stage }) => {
  const { control } = useFormContext<ChangeRequestRoleList>();
  const { fields: workflows } = useFieldArray({
    control,
    name: `roles.${roleIdx}.workflows`,
  });
  const [activeWorkFlowIdx, setActiveWorkFlowIdx] = useState<string>('0');
  const workflowTabs: KanbanTabsType = useMemo(() => {
    const tabs: KanbanTabsType = {};

    workflows.forEach((wl, workflowIdx) => {
      tabs[workflowIdx] = {
        content: (
          <NodeCabGroupList
            roleIdx={roleIdx}
            mode={mode}
            modeApproval={modeApproval}
            role={role}
            workflowIdx={workflowIdx}
            refetchChangeRequestRoles={refetchChangeRequestRoles}
            stage={stage}
          />
        ),
        title: wl.changeWorkflowName,
      };
    });

    return tabs;
  }, [mode, modeApproval, refetchChangeRequestRoles, role, roleIdx, stage, workflows]);

  return (
    <Controller
      control={control}
      name={`activeRoles.${roleIdx}`}
      render={({ field }) => (
        <KanbanAccordion
          value={field.value}
          onChange={(value) => {
            if (typeof value === 'string' || value === null) {
              field.onChange(value);
            }
          }}
          data={[
            {
              content: (
                <KanbanTabs
                  configs={{
                    value: activeWorkFlowIdx,
                    onChange: (value) => {
                      if (value) {
                        setActiveWorkFlowIdx(value);
                      }
                    },
                  }}
                  tabs={workflowTabs}
                />
              ),
              title: <KanbanText fw={500}>{`Level ${roleIdx + 1}: ${role.changeFlowNode?.name || ''}`}</KanbanText>,
              key: role.changeFlowNode?.name || '',
            },
          ]}
        />
      )}
    />
  );
};

export default NodeCabWorkflowTabs;
