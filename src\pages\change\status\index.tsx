import {
  AdvancedFilterMappingType,
  ColumnType,
  getDefaultTableAffected,
  KanbanIconButton,
  KanbanSelect,
  KanbanTitle,
  renderDateTime,
} from 'kanban-design-system';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanButton } from 'kanban-design-system';
import { KanbanTable, KanbanTableProps, TableAffactedSafeType } from 'kanban-design-system';
import { Box, Flex, Tooltip } from '@mantine/core';
import { IconEdit, IconPlus, IconTrash } from '@tabler/icons-react';
import equal from 'fast-deep-equal';
import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import { DateRangeFilter, dateRangeKeys } from '@components/AmtDateRangeInput';
import { LocalStorageKey } from '@common/constants/LocalStorageKeyConstants';
import { buildDetailUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import { EntityAction } from '@common/constants/EntityActionConstants';
import { FilterTextInput } from '@components/AmtFilterTextInput';
import { deleteConfirm } from '@components/utils/ConfirmMessageUtils';
import { parseFilterWithDate } from '@common/utils/DateUtils';
import useSavedFilterCycle from '@core/hooks/useSavedFilterCycle';
import { useSavedColumns } from '@core/hooks/useSavedColumns';
import { AmtShortTextContent, AmtShortTextLink } from '@components/AmtShortTextContent';
import { ChangeStatus } from '@core/schema/ChangeStatus';
import { CHANGE_STAGE_LABEL, ChangeStageType, ChangeStageTypeEnum } from '@common/constants/ChageStageType';
import { ChangeStatusApi } from '@api/ChangeStatus';
import { useDisclosure } from '@mantine/hooks';
import AmtModal from '@components/AmtModal';

export type ChangeStatusPageFilter = DateRangeFilter & {
  name?: string;
  stage?: ChangeStageType;
};

const initOrUpdatedFilterPayloads = (prev?: TableAffactedSafeType<ChangeStatus>, initFilters?: ChangeStatusPageFilter) => {
  const prevFilter = prev || getDefaultTableAffected();
  if (initFilters) {
    const filterObj: AdvancedFilterMappingType<ChangeStatus> = {
      ['name']: {
        filterOption: 'contains',
        value: {
          fromValue: initFilters.name,
        },
      },
      ['stage']: {
        filterOption: 'equals',
        value: {
          fromValue: initFilters.stage,
        },
      },
    };
    const advancedFilterMapping = { ...prevFilter.advancedFilterMapping, ...filterObj };
    return { ...prevFilter, advancedFilterMapping: { ...advancedFilterMapping } };
  }
  return prevFilter;
};

const columns: ColumnType<ChangeStatus>[] = [
  {
    name: 'name',
    title: 'Status Name',
    customRender: (_data, rowData) => {
      return (
        <AmtShortTextLink
          routePath={ROUTE_PATH.CONFIGURATION_CHANGE_STATUS_DETAIL}
          entityId={rowData.id}
          data={rowData.name}
          disableShorten
          fw={500}
        />
      );
    },
  },
  {
    name: 'description',
    title: 'Description',
    customRender: (_data, rowData) => <AmtShortTextContent data={rowData.description || ''} />,
  },
  {
    name: 'action',
    title: 'Action',
  },
  {
    name: 'stage',
    title: 'Stage',
    customRender: (_data, rowData) => {
      return CHANGE_STAGE_LABEL[rowData.stage];
    },
  },
  {
    name: 'createdDate',
    title: 'Created Date',
    hidden: true,
    customRender: renderDateTime,
  },
  {
    name: 'createdBy',
    title: 'Created By',
    hidden: true,
  },
  {
    name: 'modifiedDate',
    title: 'Modified Date',
    hidden: true,
    customRender: renderDateTime,
  },
  {
    name: 'modifiedBy',
    title: 'Modified By',
    hidden: true,
  },
];

export const ChangeStatusManagementPage = () => {
  const [totalRecords, setTotalRecords] = useState(0);

  const navigate = useNavigate();

  const [tableAffectedChange, setTableAffectedChange] = useState<TableAffactedSafeType>();
  const [columnOrders, setColumnOrders] = useState<string[]>();
  const [selectedForDelete, setSelectedForDelete] = useState<ChangeStatus[]>([]);
  const [confirmDeleteModalOpened, { close: closeConfirmDeleteModal, open: openConfirmDeleteModal }] = useDisclosure(false);
  const [inputFilters, setInputFilters] = useState<ChangeStatusPageFilter>({});
  const [savedFilters, setSavedFilters] = useSavedFilterCycle({
    key: LocalStorageKey.SEARCH_VALUE_CHANGE_STATUS_PAGE,
    deserialize: (str) => parseFilterWithDate<ChangeStatusPageFilter>(str, dateRangeKeys),
    setInputFilters,
  });

  const savedColumns = useSavedColumns<ChangeStatus>(LocalStorageKey.COLUMN_DISPLAY_CHANGE_STATUS_PAGE, columns, columnOrders);

  const { data, refetch: refetchList } = useFetch(ChangeStatusApi.findAll(initOrUpdatedFilterPayloads(tableAffectedChange, savedFilters)), {
    enabled: !!tableAffectedChange,
  });

  const { mutate: deleteMultiMutate } = useMutate(ChangeStatusApi.deleteByIdIn, {
    successNotification: 'Status(es) deleted successfully',
    onSuccess: () => {
      refetchList();
    },
  });

  useEffect(() => {
    if (data?.data?.content) {
      setTotalRecords(data.data.totalElements);
    }
  }, [data?.data]);

  const handleDeleteMulti = useCallback(
    (datas: ChangeStatus[]) => {
      deleteMultiMutate(datas.map((it) => it.id));
      setSelectedForDelete([]);
      closeConfirmDeleteModal();
    },
    [closeConfirmDeleteModal, deleteMultiMutate],
  );

  const handleSearch = useCallback(
    (filters?: ChangeStatusPageFilter) => {
      const appliedFilters = filters ?? inputFilters;
      setSavedFilters((prev) => ({ ...prev, ...appliedFilters }));
      setTableAffectedChange((prev) => {
        return initOrUpdatedFilterPayloads(prev, appliedFilters);
      });
    },
    [inputFilters, setSavedFilters],
  );

  const handleClearNameFilter = () => {
    const updatedFilters = { ...inputFilters, name: '' };
    handleSearch(updatedFilters);
    setInputFilters(updatedFilters);
  };

  const handleStageChange = (val: string | null) => {
    const updatedFilters = {
      ...inputFilters,
      stage: (val as ChangeStageType) ?? undefined,
    };
    handleSearch(updatedFilters);
    setInputFilters(updatedFilters);
  };

  const tableProps: KanbanTableProps<ChangeStatus> = useMemo(() => {
    const tblProps: KanbanTableProps<ChangeStatus> = {
      columns: savedColumns,
      data: data?.data?.content ?? [],
      customRowProps: (data) => ({
        onDoubleClick: () => {
          navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_CHANGE_STATUS_DETAIL, data.id, EntityAction.EDIT));
        },
      }),

      serverside: {
        totalRows: totalRecords,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffectedChange, dataSet)) {
            setTableAffectedChange(dataSet);
          }
        },
      },
      columnOrderable: {
        onOrder: (data) => {
          setColumnOrders(data.map((it) => it.name));
        },
      },
      actions: {
        customAction: (data) => {
          return <ActionColumn changeStatus={data} refetch={refetchList} />;
        },
      },
      selectableRows: {
        enable: true,
        customAction: (_, __) => (
          <KanbanButton leftSection={<IconTrash />} size='xs' bg='red' color='white' onClick={() => openConfirmDeleteModal()}>
            Delete(s)
          </KanbanButton>
        ),
        crossPageSelected: {
          rowKey: 'id',
          setSelectedRows: setSelectedForDelete,
          selectedRows: selectedForDelete,
        },
      },
      pagination: {
        enable: true,
      },
      showNumericalOrderColumn: true,
    };
    return tblProps;
  }, [savedColumns, data?.data?.content, totalRecords, selectedForDelete, navigate, tableAffectedChange, refetchList, openConfirmDeleteModal]);

  return (
    <Box>
      <HeaderTitleComponent
        title=''
        rightSection={
          <Flex>
            <KanbanButton
              size='xs'
              leftSection={<IconPlus />}
              onClick={() => {
                navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_CHANGE_STATUS_DETAIL, 0, EntityAction.CREATE));
              }}>
              Add Status
            </KanbanButton>
          </Flex>
        }
        leftSection={<KanbanTitle fz={'h4'}>Status List</KanbanTitle>}
      />
      <Flex dir='row' gap='xs' align={'center'}>
        <FilterTextInput
          onClear={handleClearNameFilter}
          placeholder='Status Name'
          value={inputFilters.name ?? ''}
          onChange={(val) => {
            setInputFilters({ ...inputFilters, name: val.target.value });
          }}
          onBlur={() => {
            handleSearch(inputFilters);
          }}
        />

        <Tooltip label='Stage' position='top-start'>
          <KanbanSelect
            mt='xs'
            size='xs'
            data={ChangeStageTypeEnum.options.map((value) => ({
              value,
              label: CHANGE_STAGE_LABEL[value],
            }))}
            placeholder='Stage'
            clearable
            value={inputFilters.stage ?? null}
            onChange={handleStageChange}
          />
        </Tooltip>
      </Flex>
      <KanbanTable {...tableProps} title='' />
      <AmtModal
        opened={confirmDeleteModalOpened}
        title={'Confirm delete user(s)'}
        onClose={() => {
          closeConfirmDeleteModal();
        }}
        actions={
          <KanbanButton variant='filled' onClick={() => handleDeleteMulti(selectedForDelete)}>
            Confirm
          </KanbanButton>
        }>
        {deleteConfirm(selectedForDelete.map((it) => it.name)).children}
      </AmtModal>
    </Box>
  );
};

const ActionColumn = ({ changeStatus, refetch }: { changeStatus: ChangeStatus; refetch: () => void }) => {
  const navigate = useNavigate();

  const { mutate: deleteMutate } = useMutate(ChangeStatusApi.deleteById, {
    successNotification: 'Field deleted successfully',
    confirm: deleteConfirm(),
    onSuccess: () => refetch(),
  });

  return (
    <Flex gap='xs' align='center'>
      <Tooltip label='Edit'>
        <KanbanIconButton
          variant='transparent'
          size={'sm'}
          onClick={() => {
            navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_CHANGE_STATUS_DETAIL, changeStatus.id, EntityAction.EDIT));
          }}>
          <IconEdit />
        </KanbanIconButton>
      </Tooltip>

      <Tooltip label='Delete'>
        <KanbanIconButton
          variant='transparent'
          color='red'
          size={'sm'}
          onClick={() =>
            deleteMutate(changeStatus.id, {
              confirm: deleteConfirm([changeStatus.name]),
            })
          }>
          <IconTrash />
        </KanbanIconButton>
      </Tooltip>
    </Flex>
  );
};

export default ChangeStatusManagementPage;
