import { z } from 'zod';
import { AuditSchema } from './Common';
import { CustomFieldSchema } from './CustomField';
import { INPUT_REQUIRE } from '@core/message/MesageConstant';
import { CustomFieldTypeEnum } from '@common/constants/CustomFieldType';
import { CustomFieldModelSchema } from '@models/CustomField';

export const ComboboxDataSchema = z.array(z.string()); // Assuming ComboboxData is an array of strings

export const TemplateCustomFieldTypeEnum = z.enum([...CustomFieldTypeEnum.options, 'BREAK']);
export type TemplateCustomFieldType = z.infer<typeof TemplateCustomFieldTypeEnum>;

export const TempateFieldItemSchema = z
  .object({
    customFieldId: z.number(),
    iconName: z.string().nullish(),
    required: z.boolean().nullish(),
    customFieldType: TemplateCustomFieldTypeEnum.nullish(),
    horizontalCoordinate: z.number().nullish(),
    verticalCoordinate: z.number().nullish(),
    width: z.number().nullish(),
    height: z.number().nullish(),
    isReusable: z.optional(z.boolean()).nullable(),
    isNew: z.optional(z.boolean()).nullable(),
  })
  .merge(CustomFieldSchema.omit({ id: true }));
export type TemplateFieldItem = z.infer<typeof TempateFieldItemSchema>;

export const DraggingTypeSchema = z.union([z.literal('FIELD'), z.literal('COLUMN'), z.null()]);

export type DraggingType = z.infer<typeof DraggingTypeSchema>;

export const SelectOptionSchema = z.object({
  value: z.string(),
  label: z.string(),
});

export type SelectOption = z.infer<typeof SelectOptionSchema>;

//change template
export const ChangeTemplateSchema = z
  .object({
    id: z.number(),
    name: z.string().trim().min(1, { message: INPUT_REQUIRE }),
    description: z.string().nullish(),
    fields: z.array(TempateFieldItemSchema).nullish(),
    changeFlowId: z.number().nullish(),
    changeFlowName: z.string().nullish(),

    isActive: z.boolean().nullish(),
    notice: z.string().nullish(),
  })
  .merge(AuditSchema);

//change template for inherit validation from customfields
export const ChangeTemplateModelSchema = z
  .object({
    id: z.number(),
    name: z.string().trim().min(1, { message: INPUT_REQUIRE }),
    description: z.string().nullish(),
    fields: z.array(CustomFieldModelSchema).nullish(),
    changeFlowId: z.number().nullish(),
    changeFlowName: z.string().nullish(),
    isActive: z.boolean().nullish(),
    notice: z.string().nullish(),
  })
  .merge(AuditSchema);
export type ChangeTemplate = z.infer<typeof ChangeTemplateSchema>;
