import { Group<PERSON>pi } from '@api/GroupApi';
import ComboboxLoadMore, { ComboboxLoadMoreProps } from '@components/ComboboxLoadMore';
import useInfiniteFetch from '@core/hooks/useInfiniteFetch';
import { GROUP_TYPE_OPTIONS } from '@core/schema/Group';
import { Box } from '@mantine/core';
import { ChangeFlowNodeGroupModel, ChangeFlowNodeType } from '@models/ChangeFlowModel';
import { TableAffactedSafeType, getDefaultTableAffected } from 'kanban-design-system';
import React, { useState, useCallback } from 'react';

interface Props {
  value: ChangeFlowNodeGroupModel[];
  onChange: (val: ChangeFlowNodeGroupModel[]) => void;
  isViewMode?: boolean;
  type: ChangeFlowNodeType;
  error?: string;
}

const ReferenceSession = ({ error, isViewMode, onChange, type, value }: Props) => {
  const [serviceSearchParams, setServiceSearchParams] = useState<TableAffactedSafeType>({
    ...getDefaultTableAffected(),
    sortedBy: 'name',
    ...(type === 'CAB' && {
      advancedFilterMapping: {
        ['type']: {
          filterOption: 'equals',
          value: {
            fromValue: GROUP_TYPE_OPTIONS.CAB,
          },
        },
      },
    }),
  });

  const { fetchNextPage: fetchNextPageApplication, flatData: applicationData } = useInfiniteFetch(
    GroupApi.findAllGroups({
      ...serviceSearchParams,
    }),
    {
      showLoading: false,
      enabled: true,
    },
  );

  const renderApplicationPill = useCallback<ComboboxLoadMoreProps<ChangeFlowNodeGroupModel>['renderPillLabel']>(
    (application) => application.name,
    [],
  );

  return (
    <Box>
      <ComboboxLoadMore
        options={applicationData.map((item) => ({
          id: String(item.id),
          name: item.name,
        }))}
        onChange={(vals) => onChange(vals)}
        required
        label='Groups User'
        onSearch={(val) => setServiceSearchParams((prev) => ({ ...(prev ?? {}), name: val }))}
        onScroll={fetchNextPageApplication}
        renderPillLabel={renderApplicationPill}
        renderOptionLabel={(data) => data.name}
        values={value?.map((v) => ({ id: String(v.id), name: v.name })) ?? []}
        scrollableForValue
        clearable={!isViewMode}
        error={error}
      />
    </Box>
  );
};

export default ReferenceSession;
