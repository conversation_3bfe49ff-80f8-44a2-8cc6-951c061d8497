import { Box, Text, Textarea, TextInput } from '@mantine/core';
import React, { useState, useCallback, useRef, useEffect } from 'react';
import { IconEdit, IconGripHorizontal } from '@tabler/icons-react';
import { NodeResizer } from '@xyflow/react';
import stylesCss from './StickyNoteNode.module.scss';
import { CustomNodeData } from './CustomNode';
import { KanbanButton } from 'kanban-design-system';
import { COMMON_DESCRIPTION_MAX_LENGTH, COMMON_MAX_LENGTH } from '@common/constants/ValidationConstant';

type StickNoteProps = {
  id: string;
  data: CustomNodeData;
  selected?: boolean;
};

function StickNoteNode({ data, selected }: StickNoteProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [title, setTitle] = useState(data.title || 'New Note');
  const [content, setContent] = useState(data.content || '');
  const [isResizing, setIsResizing] = useState(false);
  const nodeRef = useRef<HTMLDivElement>(null);

  const handleResizeStart = useCallback(() => {
    setIsResizing(true);
    if (data.onResizeStart) {
      data.onResizeStart();
    }
  }, [data]);

  const handleResizeStop = useCallback(() => {
    setIsResizing(false);
    if (data.onResizeStop) {
      data.onResizeStop();
    }
  }, [data]);

  const preventEventBubbling = useCallback((e: React.MouseEvent | React.PointerEvent) => {
    e.stopPropagation();
  }, []);

  const handleEditClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    setIsEditing(true);
  }, []);

  const handleSaveClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    setIsEditing(false);
  }, []);

  // Prevent dragging when interacting with inputs
  const handleInputMouseDown = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
  }, []);

  const handleInputPointerDown = useCallback((e: React.PointerEvent) => {
    e.stopPropagation();
  }, []);

  // Handle escape key to exit edit mode
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isEditing) {
        setIsEditing(false);
      }
    };

    if (isEditing) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isEditing]);

  return (
    <Box ref={nodeRef} className={`${stylesCss.stickyNote} ${selected ? stylesCss.selected : ''} ${isResizing ? stylesCss.resizing : ''}`}>
      {/* Node Resizer - chỉ hiển thị khi selected */}
      {selected && (
        <NodeResizer
          minWidth={150}
          minHeight={200}
          maxWidth={1000}
          maxHeight={1000}
          color='#3b82f6'
          handleStyle={{
            backgroundColor: '#3b82f6',
            width: '8px',
            height: '8px',
            border: '1px solid white',
            borderRadius: '1px',
            cursor: 'resize',
          }}
          lineStyle={{
            borderColor: '#3b82f6',
            borderWidth: '1px',
            pointerEvents: 'none',
          }}
          shouldResize={() => !isEditing && selected}
          keepAspectRatio={false}
          isVisible={selected}
          onResizeStart={handleResizeStart}
          onResizeEnd={handleResizeStop}
        />
      )}

      {/* Content */}
      <div
        className={stylesCss.contentWrapper}
        style={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'hidden',
        }}>
        {/* Resize indicator */}
        <IconGripHorizontal
          size={12}
          className={stylesCss.resizeIndicator}
          style={{
            opacity: selected ? 1 : 0.3,
            pointerEvents: 'none',
          }}
        />

        {isEditing ? (
          <div className={stylesCss.editMode}>
            <TextInput
              maxLength={COMMON_MAX_LENGTH}
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder='Enter title'
              className={stylesCss.titleInput}
              onMouseDown={handleInputMouseDown}
              onPointerDown={handleInputPointerDown}
              autoFocus
              size='sm'
            />
            <Textarea
              maxLength={COMMON_DESCRIPTION_MAX_LENGTH}
              value={content}
              onChange={(e) => setContent(e.target.value)}
              minRows={3}
              h={'100%'}
              placeholder='Enter content...'
              className={stylesCss.contentTextarea}
              autosize
              onMouseDown={handleInputMouseDown}
              onPointerDown={handleInputPointerDown}
              size='sm'
            />
            <div className={stylesCss.editActions}>
              <KanbanButton
                size='xs'
                color='dimmed'
                onClick={handleSaveClick}
                className={stylesCss.saveButton}
                onMouseDown={preventEventBubbling}
                onPointerDown={preventEventBubbling}>
                Save
              </KanbanButton>
              <KanbanButton
                size='xs'
                color='dimmed'
                onClick={() => setIsEditing(false)}
                className={stylesCss.cancelButton}
                onMouseDown={preventEventBubbling}
                onPointerDown={preventEventBubbling}>
                Cancel
              </KanbanButton>
            </div>
          </div>
        ) : (
          <div className={stylesCss.displayMode}>
            <div className={stylesCss.header}>
              <Text fw={700} size='sm' className={stylesCss.title} lineClamp={2}>
                {title}
              </Text>
              <IconEdit
                size={14}
                className={stylesCss.editIcon}
                onClick={handleEditClick}
                onMouseDown={preventEventBubbling}
                onPointerDown={preventEventBubbling}
              />
            </div>
            <Text size='xs' className={stylesCss.content}>
              {content}
            </Text>
          </div>
        )}
      </div>
    </Box>
  );
}

export default StickNoteNode;
