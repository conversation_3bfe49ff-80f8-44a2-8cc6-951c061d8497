import { isAuthorized } from '@common/utils/AclPermissionUtils';
import { AclPermissionModel } from '@core/schema/AclPermission';
import React from 'react';

export type ProtectedRouteProps = {
  children: React.ReactNode;
  //One of those role
  requirePermissions: AclPermissionModel[];
  userPermissions: AclPermissionModel[];
  errorElement: React.ReactNode;
  hiddenOnUnSatisfy?: boolean;
  allMatchPermissions?: boolean;
};
//ft/role component protect
export const ProtectedComponent: React.FC<ProtectedRouteProps> = ({
  allMatchPermissions,
  children,
  errorElement,
  hiddenOnUnSatisfy,
  requirePermissions,
  userPermissions,
}) => {
  //Not required permissions then authorized else  check user
  const isAuthorizedX = isAuthorized(userPermissions, requirePermissions, allMatchPermissions);
  return <>{isAuthorizedX ? children : hiddenOnUnSatisfy ? null : errorElement}</>;
};
