import { Stack } from '@mantine/core';
import { KanbanInput, KanbanSelect } from 'kanban-design-system';
import React from 'react';
import { CustomNodeData } from '@pages/change/request/workflow/reactFlow/CustomNode';

type ParametersTabProps = {
  customNodeData: CustomNodeData | undefined;
  setCustomNodeData: (data: React.SetStateAction<CustomNodeData | undefined>) => void;
};

export const ParametersTab: React.FC<ParametersTabProps> = ({ customNodeData, setCustomNodeData }) => {
  return (
    <Stack gap='md'>
      <KanbanSelect
        key={Math.random()}
        required
        label='Operation'
        placeholder='Select operation'
        data={[
          { value: 'build', label: 'Build' },
          { value: 'create', label: 'Create' },
          { value: 'deploy', label: 'Deploy' },
          { value: 'test', label: 'Test' },
        ]}
        value={customNodeData?.parameters?.operation || ''}
        onChange={(value) =>
          setCustomNodeData((prev) => {
            if (!prev) {
              return prev;
            }
            return {
              ...prev,
              parameters: {
                ...prev.parameters,
                operation: value || '',
              },
            };
          })
        }
      />
      <KanbanInput
        required
        label='Parameter 1'
        value={customNodeData?.parameters?.parameter1 || ''}
        onChange={(data) =>
          setCustomNodeData((prev) => {
            if (!prev) {
              return prev;
            }
            return {
              ...prev,
              parameters: {
                ...prev.parameters,
                parameter1: data.target.value,
              },
            };
          })
        }
        maxLength={255}
      />
      <KanbanInput
        required
        label='Parameter 2'
        value={customNodeData?.parameters?.parameter2 || ''}
        onChange={(data) =>
          setCustomNodeData((prev) => {
            if (!prev) {
              return prev;
            }
            return {
              ...prev,
              parameters: {
                ...prev.parameters,
                parameter2: data.target.value,
              },
            };
          })
        }
        maxLength={255}
      />
    </Stack>
  );
};

export default ParametersTab;
