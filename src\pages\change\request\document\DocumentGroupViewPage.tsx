import React, { useEffect, useState } from 'react';
import { Box, Flex } from '@mantine/core';
import { KanbanButton, KanbanIconButton, KanbanTitle } from 'kanban-design-system';
import { IconArrowLeft } from '@tabler/icons-react';
import { CommonTabProps } from '@pages/change/request/document/index';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import DocumentAccordionView from './components/DocumentAccordionView';
import useMutate from '@core/hooks/useMutate';
import { ChangeRequestDocumentGroupApi } from '@api/ChangeRequestDocumentGroupApi';
import { ChangeRequestDocumentEnums, ChangeRequestSendToOwnerRequest } from '@models/ChangeRequestDocumentGroupModel';

const DocumentGroupViewPage = ({
  changeRequestId,
  documentGroups,
  handleEdit,
  handleNavigateToList,
  hasEditPermission,
  hasMatchingRole,
  onRefetchDocumentGroups,
}: CommonTabProps) => {
  const [openedItems, setOpenedItems] = useState<string[]>([]);
  const [selectedItems, setSelectedItems] = useState<ChangeRequestSendToOwnerRequest[]>([]);
  useEffect(() => {
    if (documentGroups) {
      setOpenedItems(documentGroups.map((g) => g.id.toString()));
    }
  }, [documentGroups]);

  const { mutate: sendToOwner } = useMutate(
    ({ changeRequestId, data }: { changeRequestId: number; data: ChangeRequestSendToOwnerRequest[] }) =>
      ChangeRequestDocumentGroupApi.sendToOwner(changeRequestId, data),
    {
      successNotification: 'Send to owner successfully',
      onSuccess: () => {
        onRefetchDocumentGroups();
      },
    },
  );

  const handleToggle = (referenceId: number, username: string, checked: boolean) => {
    setSelectedItems((prev) => {
      const index = prev.findIndex((item) => item.referenceId === referenceId);

      if (checked) {
        if (index !== -1) {
          const item = prev[index];
          if (!item.usernames.includes(username)) {
            const updated = [...prev];
            updated[index] = { ...item, usernames: [...item.usernames, username] };
            return updated;
          }
          return prev;
        } else {
          return [...prev, { referenceId, usernames: [username] }];
        }
      }

      if (index !== -1) {
        const item = prev[index];
        const newUsernames = item.usernames.filter((u) => u !== username);

        if (newUsernames.length === 0) {
          return prev.filter((_, i) => i !== index);
        }

        const updated = [...prev];
        updated[index] = { ...item, usernames: newUsernames };
        return updated;
      }

      return prev;
    });
  };

  const handleSendToOwner = () => {
    sendToOwner({
      changeRequestId,
      data: selectedItems,
    });
  };

  return (
    <Flex direction='column' justify='flex-end'>
      <HeaderTitleComponent
        title=''
        leftSection={
          <Flex align='center' gap='xs'>
            <KanbanIconButton size='sm' variant='subtle' onClick={handleNavigateToList}>
              <IconArrowLeft />
            </KanbanIconButton>
            <KanbanTitle fz='h4'>Change documents</KanbanTitle>
          </Flex>
        }
        rightSection={
          <Flex align='center' gap='xs'>
            {hasEditPermission && (
              <>
                <KanbanButton size='xs' variant='filled' onClick={handleEdit}>
                  Edit
                </KanbanButton>
                {hasMatchingRole(ChangeRequestDocumentEnums.Role.Enum.COORDINATOR) && (
                  <KanbanButton size='xs' variant='filled' disabled={selectedItems.length === 0} onClick={handleSendToOwner}>
                    Send to owner
                  </KanbanButton>
                )}
              </>
            )}
          </Flex>
        }
      />
      <Box p='md'>
        <KanbanTitle mb='lg'>Document</KanbanTitle>
        <DocumentAccordionView
          changeRequestId={changeRequestId}
          documentGroups={documentGroups}
          openedItems={openedItems}
          onChange={setOpenedItems}
          onToggle={handleToggle}
          selectedItems={selectedItems}
        />
      </Box>
    </Flex>
  );
};

export default DocumentGroupViewPage;
