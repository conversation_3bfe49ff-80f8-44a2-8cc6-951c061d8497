import {
  AdvancedFilterMappingType,
  ColumnType,
  getDefaultTableAffected,
  KanbanButton,
  KanbanIconButton,
  KanbanSelect,
  KanbanTable,
  KanbanTableProps,
  KanbanTitle,
  renderDateTime,
  TableAffactedSafeType,
} from 'kanban-design-system';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { Box, Flex, Tooltip } from '@mantine/core';
import { IconEdit, IconNote, IconPlus, IconTrash } from '@tabler/icons-react';
import equal from 'fast-deep-equal';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import useFetch from '@core/hooks/useFetch';
import { useDisclosure } from '@mantine/hooks';
import AmtModal from '@components/AmtModal';
import { ChangeRequest } from '@core/schema/ChangeRequest';
import { ChangeRequestApi } from '@api/ChangeRequestApi';
import { ChangeModel } from '@models/ChangeModel';
import commonStyled from '@resources/styles/Common.module.scss';
import { useCheckPermissons } from '@core/hooks/useCheckPermissons';
import { PermissionAction, PermissionActionModule } from '@common/constants/AclPermissionConstants';
import ChangeNotePage, { ChangeNoteRef } from '@pages/change/changeList/ChangeNotePage';
import { KeyConfigDisplayColumn } from '@common/constants/UserPreferenceKeyContants';
import { useSavedColumns } from '@core/hooks/useSavedColumns';
import { SaveColumnType } from '@common/constants/UserPreferenceType';
import { CHANGE_STAGE_LABEL, ChangeStageType, ChangeStageTypeEnum } from '@common/constants/ChageStageType';
import useMutate from '@core/hooks/useMutate';
import { deleteConfirm } from '@components/utils/ConfirmMessageUtils';
import { FilterTextInput } from '@components/AmtFilterTextInput';
import useSavedFilterCycle from '@core/hooks/useSavedFilterCycle';
import { LocalStorageKey } from '@common/constants/LocalStorageKeyConstants';
import { parseFilterWithDate } from '@common/utils/DateUtils';
import { AmtDateRangeInput, DateRangeFilter, dateRangeKeys } from '@components/AmtDateRangeInput';
import { COMMON_MAX_LENGTH, COMMON_MAX_NUMBER_LENGTH } from '@common/constants/ValidationConstant';
import dayjs from 'dayjs';
import { buildDetailUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import { EntityAction } from '@common/constants/EntityActionConstants';
import { useNavigate } from 'react-router-dom';
import { AmtShortTextLink } from '@components/AmtShortTextContent';

export type ChangeRequestPageFilter = DateRangeFilter & {
  changeId?: number;
  coordinator?: string;
  stage?: ChangeStageType;
};

const initOrUpdatedFilterPayloads = (tableAffectedChange?: TableAffactedSafeType, initFilters?: ChangeRequestPageFilter) => {
  const prevFilter = tableAffectedChange || getDefaultTableAffected();
  const normalizeFilterMapping = (filterMapping?: AdvancedFilterMappingType<ChangeRequest>) => {
    const updated = { ...filterMapping };
    ['modifiedDate', 'createdDate'].forEach((key) => {
      const filter = updated?.[key];
      if (
        (filter?.filterOption === 'lessThan' || filter?.filterOption === 'lessThanOrEqualTo') &&
        filter.value?.fromValue &&
        !filter.value?.toValue
      ) {
        updated[key] = {
          ...filter,
          value: {
            toValue: filter.value.fromValue,
          },
        };
      }
    });
    return updated;
  };
  if (initFilters) {
    const changeFilter: AdvancedFilterMappingType<ChangeRequest> = {
      ['Id']: {
        filterOption: 'equals',
        value: {
          fromValue: initFilters.changeId,
        },
      },
      ['Coordinator']: {
        filterOption: 'contains',
        value: {
          fromValue: initFilters.coordinator,
        },
      },
      ['Stage']: {
        filterOption: 'equals',
        value: {
          fromValue: initFilters.stage,
        },
      },
      ['CreatedDate']: {
        filterOption:
          initFilters.startDate && initFilters.endDate ? 'betweenInclusive' : initFilters.startDate ? 'greaterThanOrEqualTo' : 'lessThanOrEqualTo',
        value: {
          fromValue: initFilters.startDate ? dayjs(initFilters.startDate).format() : '',
          toValue: initFilters.endDate ? dayjs(initFilters.endDate).format() : '',
        },
      },
    };

    return {
      ...prevFilter,
      advancedFilterMapping: normalizeFilterMapping({
        ...prevFilter.advancedFilterMapping,
        ...changeFilter,
      }),
    };
  }
  return prevFilter;
};

export const changePermissionConfigs = {
  create: {
    module: PermissionActionModule.CHANGE_SDP,
    permission: PermissionAction.CHANGE_ADD,
  },
  edit: {
    module: PermissionActionModule.CHANGE_SDP,
    permission: PermissionAction.CHANGE_UPDATE,
  },
  delete: {
    module: PermissionActionModule.CHANGE_SDP,
    permission: PermissionAction.CHANGE_DELETE,
  },
};

const ActionColumn = ({ changeRequest, refetch }: { changeRequest: ChangeRequest; refetch: () => void }) => {
  const navigate = useNavigate();
  const canEditChange = useCheckPermissons([changePermissionConfigs.edit]);
  const canDeleteChange = useCheckPermissons([changePermissionConfigs.delete]);

  const { mutate: deleteMutate } = useMutate(ChangeRequestApi.deleteById, {
    successNotification: 'Change deleted successfully',
    confirm: deleteConfirm(),
    onSuccess: () => refetch(),
  });

  return (
    <Flex gap='xs' align='center'>
      {canEditChange && (
        <Tooltip label='Edit'>
          <KanbanIconButton
            variant='transparent'
            size='sm'
            onClick={() => {
              navigate(buildDetailUrl(ROUTE_PATH.CHANGE_REQUEST_DETAIL, changeRequest.id, EntityAction.EDIT));
            }}>
            <IconEdit />
          </KanbanIconButton>
        </Tooltip>
      )}
      {canDeleteChange && (
        <Tooltip label='Delete'>
          <KanbanIconButton
            variant='transparent'
            color='red'
            size='sm'
            onClick={() =>
              deleteMutate(changeRequest.id, {
                confirm: deleteConfirm([changeRequest.title]),
              })
            }>
            <IconTrash />
          </KanbanIconButton>
        </Tooltip>
      )}
    </Flex>
  );
};

export const ChangeManagementPage = () => {
  const [totalRecords, setTotalRecords] = useState(0);
  const [listData, setListData] = useState<ChangeModel[]>([]);
  const [columnOrders, setColumnOrders] = useState<string[]>();
  const [tableAffectedChange, setTableAffectedChange] = useState<TableAffactedSafeType>();
  const [selectedChangeId, setSelectedChangeId] = useState(0);
  const [selectedStage, setSelectedStage] = useState('');
  const [openedModalNotes, { close: closedModalNotes, open: openModalNote }] = useDisclosure(false);
  const changeNoteRef = useRef<ChangeNoteRef | null>(null);
  const [saveDisabled, setSaveDisabled] = useState(true);
  const [selectedForDelete, setSelectedForDelete] = useState<ChangeRequest[]>([]);
  const [confirmDeleteModalOpened, { close: closeConfirmDeleteModal, open: openConfirmDeleteModal }] = useDisclosure(false);
  const [inputFilters, setInputFilters] = useState<ChangeRequestPageFilter>({});
  const [savedFilters, setSavedFilters] = useSavedFilterCycle({
    key: LocalStorageKey.SEARCH_VALUE_CHANGE_REQUEST_PAGE,
    deserialize: (str) => parseFilterWithDate<ChangeRequestPageFilter>(str, dateRangeKeys),
    setInputFilters,
  });
  const canCreateChange = useCheckPermissons([changePermissionConfigs.create]);
  const canEditChange = useCheckPermissons([changePermissionConfigs.edit]);
  const canDeleteChange = useCheckPermissons([changePermissionConfigs.delete]);
  const navigate = useNavigate();
  const columns: ColumnType<ChangeRequest>[] = [
    {
      name: 'note',
      title: 'Note',
      width: '1%',
      sortable: false,
      advancedFilter: {
        enable: false,
      },
      customRender: (_, rowData) => {
        const hasNote = rowData.hasNote;
        return (
          <span
            style={{ cursor: 'pointer' }}
            onClick={() => {
              openModalNote();
              setSelectedChangeId(rowData.id);
              setSelectedStage(rowData.stage);
            }}>
            <IconNote size={16} color={hasNote ? 'gold' : 'gray'} />
          </span>
        );
      },
    },
    {
      name: 'id',
      title: 'ID',
      customRender: (_, rowData) => {
        return (
          <AmtShortTextLink
            routePath={ROUTE_PATH.CHANGE_REQUEST_DETAIL}
            entityId={rowData.id}
            entityAction={EntityAction.VIEW}
            data={rowData.id.toString()}
            disableShorten
            fw={500}
          />
        );
      },
      advancedFilter: {
        variant: 'number',
        filterModes: ['equals'],
        customProps: {
          maxLength: COMMON_MAX_NUMBER_LENGTH,
          allowDecimal: false,
        },
      },
    },
    {
      name: 'title',
      title: 'Title',
      advancedFilter: {
        variant: 'text',
        customProps: {
          maxLength: COMMON_MAX_LENGTH,
        },
      },
    },
    {
      name: 'templateName',
      title: 'Template name',
      advancedFilter: {
        variant: 'text',
        customProps: {
          maxLength: COMMON_MAX_LENGTH,
        },
      },
    },
    {
      name: 'stage',
      title: 'Stage',
      customRender: (_data, rowData) => {
        return CHANGE_STAGE_LABEL[rowData.stage];
      },
      advancedFilter: {
        variant: 'select',
        filterModes: ['equals'],
        customProps: {
          data: Object.entries(CHANGE_STAGE_LABEL).map(([value, label]) => ({
            value,
            label,
          })),
          clearable: true,
          placeholder: 'Select stage',
          size: 'xs',
        },
      },
    },
    {
      name: 'coordinator',
      title: 'Change Coordinator',
      advancedFilter: {
        variant: 'text',
        customProps: {
          maxLength: COMMON_MAX_LENGTH,
        },
      },
    },
    {
      name: 'createdBy',
      title: 'Created By',
      advancedFilter: {
        variant: 'text',
        customProps: {
          maxLength: COMMON_MAX_LENGTH,
        },
      },
    },
    {
      name: 'createdDate',
      title: 'Created Time',
      customRender: renderDateTime,
      advancedFilter: {
        variant: 'date',
        filterModes: ['greaterThan', 'lessThan', 'greaterThanOrEqualTo', 'lessThanOrEqualTo', 'equals'],
        customProps: {
          maxLength: COMMON_MAX_LENGTH,
          clearable: true,
        },
      },
    },
    {
      name: 'modifiedBy',
      title: 'Modified By',
      advancedFilter: {
        variant: 'text',
        customProps: {
          maxLength: COMMON_MAX_LENGTH,
        },
      },
    },
    {
      name: 'modifiedDate',
      title: 'Modified Time',
      customRender: renderDateTime,
      advancedFilter: {
        variant: 'date',
        filterModes: ['greaterThan', 'lessThan', 'greaterThanOrEqualTo', 'lessThanOrEqualTo', 'equals'],
        customProps: {
          maxLength: COMMON_MAX_LENGTH,
          clearable: true,
        },
      },
    },
  ];
  const savedColumns = useSavedColumns<ChangeRequest>(
    KeyConfigDisplayColumn.COLUMN_DISPLAY_CHANGES_PAGE,
    columns,
    columnOrders,
    SaveColumnType.SAVE_DATABASE,
  );
  const {
    data: changesResponse,
    isFetching,
    refetch: refetchList,
  } = useFetch(ChangeRequestApi.findAll(initOrUpdatedFilterPayloads(tableAffectedChange, savedFilters)), {
    enabled: !!tableAffectedChange,
    showLoading: false,
  });

  const { mutate: deleteMultiMutate } = useMutate(ChangeRequestApi.deleteByIdIn, {
    successNotification: 'Change(s) deleted successfully',
    onSuccess: () => {
      refetchList();
    },
  });

  useEffect(() => {
    if (changesResponse?.data?.content) {
      const results = changesResponse.data.content;
      setListData(results);
      setTotalRecords(changesResponse.data.totalElements);
    }
  }, [changesResponse]);

  const handleDeleteMulti = useCallback(
    (datas: ChangeRequest[]) => {
      deleteMultiMutate(datas.map((it) => it.id));
      setSelectedForDelete([]);
      closeConfirmDeleteModal();
    },
    [closeConfirmDeleteModal, deleteMultiMutate],
  );

  useEffect(() => {
    if (openedModalNotes) {
      setSaveDisabled(true);
    }
  }, [openedModalNotes, selectedChangeId]);

  const tableProps: KanbanTableProps<ChangeRequest> = useMemo(() => {
    const tblProps: KanbanTableProps<ChangeRequest> = {
      columns: savedColumns,
      data: listData,
      isLoading: isFetching,
      customRowProps: (data) => ({
        onDoubleClick: () => {
          navigate(buildDetailUrl(ROUTE_PATH.CHANGE_REQUEST_DETAIL, data.id, EntityAction.VIEW));
        },
      }),
      serverside: {
        totalRows: totalRecords,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffectedChange, dataSet)) {
            setTableAffectedChange(dataSet);
          }
        },
      },
      actions: {
        customAction: (data) => (canEditChange || canDeleteChange) && <ActionColumn changeRequest={data} refetch={refetchList} />,
      },
      sortable: {
        enable: true,
      },
      advancedFilterable: {
        enable: true,
        debounceTime: 200,
        resetOnClose: true,
      },
      selectableRows: {
        enable: canDeleteChange,
        customAction: (_, __) => (
          <KanbanButton leftSection={<IconTrash />} size='xs' bg='red' color='white' onClick={() => openConfirmDeleteModal()}>
            Delete(s)
          </KanbanButton>
        ),
        crossPageSelected: {
          rowKey: 'id',
          setSelectedRows: setSelectedForDelete,
          selectedRows: selectedForDelete,
        },
      },
      pagination: {
        enable: true,
      },
      columnOrderable: {
        onOrder: (data) => {
          setColumnOrders(data.map((it) => it.name));
        },
      },
      showNumericalOrderColumn: true,
    };
    return tblProps;
  }, [
    savedColumns,
    listData,
    totalRecords,
    canDeleteChange,
    selectedForDelete,
    navigate,
    tableAffectedChange,
    canEditChange,
    refetchList,
    openConfirmDeleteModal,
    isFetching,
  ]);

  const updateNoteFlag = (changeId: number) => {
    setListData((prevList) => prevList.map((item) => (item.id === changeId ? { ...item, hasNote: true } : item)));
  };

  const handleSearch = useCallback(
    (filters?: ChangeRequestPageFilter) => {
      const appliedFilters = filters ?? inputFilters;
      setSavedFilters((prev) => ({ ...prev, ...appliedFilters }));
      setTableAffectedChange((prev) => {
        return initOrUpdatedFilterPayloads(prev, appliedFilters);
      });
    },
    [inputFilters, setSavedFilters],
  );
  const handleClearInputFilter = (key: keyof typeof inputFilters, research: boolean = true) => {
    const updatedFilters = { ...inputFilters, [key]: '' };
    setInputFilters(updatedFilters);
    if (research) {
      handleSearch(updatedFilters);
    }
  };
  const handleClearDateFilter = useCallback(() => {
    const toUpdate: DateRangeFilter = { startDate: undefined, endDate: undefined };

    setInputFilters((prev) => ({ ...prev, ...toUpdate }));
    setSavedFilters((prev) => ({ ...prev, ...toUpdate }));
  }, [setSavedFilters]);
  const handleStageChange = (val: string | null) => {
    const updatedFilters = {
      ...inputFilters,
      stage: (val as ChangeStageType) ?? undefined,
    };
    handleSearch(updatedFilters);
    setInputFilters(updatedFilters);
  };

  return (
    <Box>
      <HeaderTitleComponent
        title=''
        rightSection={
          <Flex>
            {canCreateChange && (
              <KanbanButton
                size='xs'
                leftSection={<IconPlus />}
                onClick={() => {
                  navigate(buildDetailUrl(ROUTE_PATH.CHANGE_REQUEST_DETAIL, 0, EntityAction.CREATE));
                }}>
                New Change
              </KanbanButton>
            )}
          </Flex>
        }
        leftSection={<KanbanTitle fz={'h4'}>Change List</KanbanTitle>}
      />
      <Flex dir='row' gap='xs' align={'center'}>
        <FilterTextInput
          onClear={() => handleClearInputFilter('changeId')}
          placeholder='Change ID'
          value={inputFilters.changeId !== undefined ? String(inputFilters.changeId) : ''}
          onChange={(val) => {
            if (/^\d*$/.test(val.target.value)) {
              let raw = val.target.value;
              if (raw.length > COMMON_MAX_NUMBER_LENGTH) {
                raw = raw.slice(0, COMMON_MAX_NUMBER_LENGTH);
              }
              const num = Number(raw);
              setInputFilters({
                ...inputFilters,
                changeId: raw === '' || isNaN(num) ? undefined : num,
              });
            }
          }}
          onBlur={() => {
            handleSearch(inputFilters);
          }}
        />
        <FilterTextInput
          onClear={() => handleClearInputFilter('coordinator')}
          placeholder='Change Coordinator'
          value={inputFilters.coordinator ?? ''}
          onChange={(val) => {
            setInputFilters({ ...inputFilters, coordinator: val.target.value });
          }}
          onBlur={() => {
            handleSearch(inputFilters);
          }}
          maxLength={COMMON_MAX_LENGTH}
        />
        <Tooltip label='Stage' position='top-start'>
          <KanbanSelect
            mt='xs'
            size='xs'
            data={ChangeStageTypeEnum.options.map((value) => ({
              value,
              label: CHANGE_STAGE_LABEL[value],
            }))}
            placeholder='Stage'
            clearable
            value={inputFilters.stage ?? null}
            onChange={handleStageChange}
          />
        </Tooltip>
        <AmtDateRangeInput
          tooltipLabel='Created Time'
          startDate={inputFilters.startDate}
          endDate={inputFilters.endDate}
          updateFilters={setInputFilters}
          onBlur={() => {
            handleSearch();
          }}
          onClear={handleClearDateFilter}
        />
      </Flex>
      <KanbanTable {...tableProps} title='' />
      <AmtModal
        opened={openedModalNotes}
        showCloseIcon={true}
        onClose={() => changeNoteRef.current?.handleCancel()}
        title={`#${selectedChangeId} - Add Notes`}
        size='lg'
        actions={
          !saveDisabled && (
            <KanbanButton
              disabled={saveDisabled}
              onClick={() => {
                changeNoteRef.current?.handleSave();
                updateNoteFlag(selectedChangeId);
              }}
              className={commonStyled.buttonSuccess}>
              Save
            </KanbanButton>
          )
        }>
        <ChangeNotePage
          ref={changeNoteRef}
          changeId={selectedChangeId}
          stage={selectedStage}
          onCancelModal={() => closedModalNotes()}
          onContentChange={(isEmpty) => setSaveDisabled(isEmpty)}
        />
      </AmtModal>
      <AmtModal
        opened={confirmDeleteModalOpened}
        title={'Confirm delete change(s)'}
        onClose={() => {
          closeConfirmDeleteModal();
        }}
        actions={
          <KanbanButton variant='filled' onClick={() => handleDeleteMulti(selectedForDelete)}>
            Confirm
          </KanbanButton>
        }>
        {deleteConfirm(selectedForDelete.map((it) => it.title)).children}
      </AmtModal>
    </Box>
  );
};
export default ChangeManagementPage;
