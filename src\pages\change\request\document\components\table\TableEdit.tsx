import { ActionIcon, Box, Flex, Table } from '@mantine/core';
import classes from './CommonTable.module.scss';
import { KanbanButton, KanbanText } from 'kanban-design-system';
import { IconCircleMinus, IconCirclePlus, IconPlus } from '@tabler/icons-react';
import { useFieldArray, useFormContext, useWatch } from 'react-hook-form';
import React, { useCallback, useMemo, useState } from 'react';
import {
  ChangeRequestDocumentEnums,
  ChangeRequestDocumentGroupFormWrapper,
  ChangeRequestDocumentRole,
} from '@models/ChangeRequestDocumentGroupModel';
import { OwnerRow } from '@pages/change/request/document/components/table/OwnerRow';
import { useSelector } from 'react-redux';
import { getCurrentUser } from '@slices/CurrentUserSlice';

interface TableEditProps {
  itemName: `items.${number}`;
  hasMatchingRole: (targetRole: ChangeRequestDocumentRole) => boolean;
}

const TableEdit = React.memo(function TableEdit({ hasMatchingRole, itemName }: TableEditProps) {
  const [openedPopoverId, setOpenedPopoverId] = useState<string | null>(null);
  const { control } = useFormContext<ChangeRequestDocumentGroupFormWrapper>();
  const userName = useSelector(getCurrentUser)?.userInfo?.userName ?? '';

  const { append, fields, remove } = useFieldArray({
    control,
    name: `${itemName}.owners`,
  });

  const watchedOwners = useWatch({
    control,
    name: `${itemName}.owners`,
  });

  const canEditCab = useMemo(
    () =>
      hasMatchingRole(ChangeRequestDocumentEnums.Role.Enum.OWNER) &&
      !!watchedOwners?.some((owner) => ChangeRequestDocumentEnums.ActionStatus.Enum.SENT_TO_OWNER === owner?.status && owner?.username === userName),
    [watchedOwners, hasMatchingRole, userName],
  );

  const totalDocumentRows = useMemo(
    () => watchedOwners?.reduce((sum, owner) => sum + Math.max(owner?.documents?.length ?? 0, 1), 0) ?? 0,
    [watchedOwners],
  );

  const [isLeaderLevel2Visible, setLeaderLevel2Visible] = useState(false);

  const handleAddOwner = useCallback(() => append({ username: '' }), [append]);

  return (
    <Flex gap='md' direction='column'>
      <Table withColumnBorders striped highlightOnHover verticalSpacing='sm' horizontalSpacing='md' className={classes.table}>
        <Table.Thead>
          <Table.Tr>
            <Table.Th className={classes.headerCell}>Owner</Table.Th>
            <Table.Th className={classes.headerCell}>Document</Table.Th>
            <Table.Th className={classes.headerCell}>
              <Flex justify='space-between' align='center'>
                <KanbanText size='sm' fw='bold'>
                  Leader level 1
                </KanbanText>
                {!isLeaderLevel2Visible && (
                  <ActionIcon title='Add Leader Column' variant='subtle' onClick={() => setLeaderLevel2Visible(true)}>
                    <IconCirclePlus size={18} color='black' />
                  </ActionIcon>
                )}
              </Flex>
            </Table.Th>
            {isLeaderLevel2Visible && (
              <Table.Th className={classes.headerCell}>
                <Flex justify='space-between' align='center'>
                  <KanbanText size='sm' fw='bold'>
                    Leader level 2
                  </KanbanText>
                  <ActionIcon title='Remove Leader Column' variant='subtle' color='black' onClick={() => setLeaderLevel2Visible(false)}>
                    <IconCircleMinus size={18} />
                  </ActionIcon>
                </Flex>
              </Table.Th>
            )}
            <Table.Th className={classes.headerCell}>CAB</Table.Th>
            <Table.Th className={classes.headerCell}>Status</Table.Th>
            <Table.Th className={classes.headerCell}>Comment</Table.Th>
            <Table.Th className={classes.headerCell}></Table.Th>
          </Table.Tr>
        </Table.Thead>
        <Table.Tbody>
          {fields.map((owner, ownerIndex) => (
            <OwnerRow
              hasMatchingRole={hasMatchingRole}
              canEditCab={canEditCab}
              isLeaderLevel2Visible={isLeaderLevel2Visible}
              userName={userName}
              key={owner.id}
              cabName={`${itemName}.cabs`}
              ownerName={`${itemName}.owners.${ownerIndex}`}
              ownerIndex={ownerIndex}
              removeOwner={() => remove(ownerIndex)}
              totalDocumentRows={totalDocumentRows}
              openedPopoverId={openedPopoverId}
              ownerLength={fields.length}
              setOpenedPopoverId={setOpenedPopoverId}
            />
          ))}
        </Table.Tbody>
      </Table>
      <Box>
        <KanbanButton size='xs' variant='filled' leftSection={<IconPlus size={14} />} onClick={handleAddOwner} title='Add owner'>
          Add owner
        </KanbanButton>
      </Box>
    </Flex>
  );
});

export default TableEdit;
