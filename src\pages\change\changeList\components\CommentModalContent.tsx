import { ChangeRequestApprovalApi } from '@api/ChangeRequestApprovalApi';
import { dateToString } from '@common/utils/DateUtils';
import useFetch from '@core/hooks/useFetch';
import { Box, Flex } from '@mantine/core';
import { KanbanText, KanbanTitle } from 'kanban-design-system';
import React from 'react';

type CommentRequestApprovalProps = {
  changeRequestApprovalId: number;
};

const CommentModalContent: React.FC<CommentRequestApprovalProps> = ({ changeRequestApprovalId }) => {
  const { data: results } = useFetch(ChangeRequestApprovalApi.findAllResults(changeRequestApprovalId), {
    enabled: !!changeRequestApprovalId,
  });
  const listData = results?.data || [];

  return (
    <>
      <KanbanTitle>Comment</KanbanTitle>
      {listData.map((comment) => (
        <Box key={comment.id} style={{ marginBottom: 10, marginTop: 10 }}>
          <Flex align={'center'} gap='sm'>
            <KanbanText size='md' c={'var(--mantine-color-violet-7)'} fw={500}>
              {comment.createdBy}
            </KanbanText>
            <KanbanText size='sm'>{comment.createdDate ? dateToString(new Date(comment.createdDate)) : 'N/A'}</KanbanText>
          </Flex>
          <KanbanText size='sm'>{comment.approvalComment}</KanbanText>
        </Box>
      ))}
    </>
  );
};

export default CommentModalContent;
