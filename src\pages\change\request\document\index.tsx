import React, { useCallback, useMemo } from 'react';
import { EntityAction } from '@common/constants/EntityActionConstants';
import { useDisclosure } from '@mantine/hooks';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { buildDetailUrl, buildUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import useFetch from '@core/hooks/useFetch';
import { ChangeRequestDocumentGroupApi } from '@api/ChangeRequestDocumentGroupApi';
import { ChangeRequestDocumentEnums, ChangeRequestDocumentGroupModel, ChangeRequestDocumentRole } from '@models/ChangeRequestDocumentGroupModel';
import { buildNavigateState } from '@core/hooks/useSavedFilterCycle';
import DocumentGroupViewPage from '@pages/change/request/document/DocumentGroupViewPage';
import DocumentGroupEditPage from '@pages/change/request/document/DocumentGroupEditPage';
import { useCheckPermissons } from '@core/hooks/useCheckPermissons';
import { ACL_PERMISSIONS } from '@common/constants/AclPermissionConstants';
import { KanbanText } from 'kanban-design-system';
import { Center } from '@mantine/core';
import UnsaveConfirmModal from '@components/utils/ConfirmMessageUtils';

// Props for View/Edit components
export interface CommonTabProps {
  changeRequestId: number;
  hasEditPermission: boolean;
  canSaveChange: boolean;
  documentGroups: ChangeRequestDocumentGroupModel[];
  handleExistWithoutSave: () => void;
  handleEdit: () => void;
  handleSave: () => void;
  handleNavigateToList: () => void;
  onRefetchDocumentGroups: () => void;
  hasMatchingRole: (targetRole: ChangeRequestDocumentRole) => boolean;
}

interface ChangeRequestDocumentTabProps {
  changeTemplateId: number; // currently unused
}

const ChangeRequestDocumentTab: React.FC<ChangeRequestDocumentTabProps> = () => {
  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);
  const [searchParams] = useSearchParams();
  const action = (searchParams.get('action') as EntityAction) || EntityAction.VIEW;

  const { id } = useParams();
  const changeRequestId = Number(id);
  const navigate = useNavigate();

  const { data, refetch } = useFetch(ChangeRequestDocumentGroupApi.findAll(changeRequestId), {
    enabled: !!changeRequestId,
  });

  const roles = useMemo(() => data?.data?.roles ?? [], [data]);
  const documentGroups = useMemo(() => data?.data?.groups ?? [], [data]);

  const canEditChange = useCheckPermissons([ACL_PERMISSIONS.CHANGE_UPDATE]);
  const canSaveChange = useCheckPermissons(changeRequestId ? [ACL_PERMISSIONS.CHANGE_UPDATE] : [ACL_PERMISSIONS.CHANGE_ADD]);

  const hasMatchingRole = useCallback((role: ChangeRequestDocumentRole) => roles.includes(role), [roles]);

  const hasEditPermission = useMemo(() => {
    return (
      (hasMatchingRole(ChangeRequestDocumentEnums.Role.enum.COORDINATOR) || hasMatchingRole(ChangeRequestDocumentEnums.Role.enum.OWNER)) &&
      canEditChange
    );
  }, [canEditChange, hasMatchingRole]);

  const ActiveComponent = useMemo(() => {
    if (action === EntityAction.VIEW) {
      return DocumentGroupViewPage;
    }

    if ([EntityAction.EDIT, EntityAction.CREATE].includes(action)) {
      if (hasEditPermission) {
        return DocumentGroupEditPage;
      }
      return null;
    }

    return null;
  }, [action, hasEditPermission]);

  const handleEdit = useCallback(() => {
    navigate(buildDetailUrl(ROUTE_PATH.CHANGE_REQUEST_DETAIL, changeRequestId, EntityAction.EDIT));
  }, [navigate, changeRequestId]);

  const handleSave = useCallback(() => {
    navigate(buildDetailUrl(ROUTE_PATH.CHANGE_REQUEST_DETAIL, changeRequestId, EntityAction.VIEW));
  }, [navigate, changeRequestId]);

  const handleNavigateToList = useCallback(() => {
    navigate(buildUrl(ROUTE_PATH.CHANGE_REQUEST), { state: buildNavigateState({ fromDetail: true }) });
  }, [navigate]);

  const handleExistWithoutSave = useCallback(openModal, [openModal]);

  return (
    <>
      {ActiveComponent ? (
        <>
          <UnsaveConfirmModal opened={openedModal} onClose={closeModal} onConfirm={handleNavigateToList} />
          <ActiveComponent
            hasMatchingRole={hasMatchingRole}
            hasEditPermission={hasEditPermission}
            canSaveChange={canSaveChange}
            changeRequestId={changeRequestId}
            documentGroups={documentGroups}
            onRefetchDocumentGroups={refetch}
            handleEdit={handleEdit}
            handleSave={handleSave}
            handleExistWithoutSave={handleExistWithoutSave}
            handleNavigateToList={handleNavigateToList}
          />
        </>
      ) : (
        <Center mt='xl'>
          <KanbanText c='dimmed' fw={500} fz='lg'>
            No permission
          </KanbanText>
        </Center>
      )}
    </>
  );
};

export default ChangeRequestDocumentTab;
