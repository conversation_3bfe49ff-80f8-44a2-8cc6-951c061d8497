import { BaseURL } from '@common/constants/BaseUrl';
import { RequestConfig } from '@core/api';
import { PaginationRequest } from './Type';
import { tableAffectedToMultiColumnFilterPaginationRequestModel } from '@common/utils/TableUtils';
import { TableAffactedSafeType } from 'kanban-design-system';
import { ResponseData, createResponseSchema, Page, createPageSchema, createListSchema } from '@core/schema/Common';
import { ChangeWorkflowNode, ChangeWorkflowNodeSchema } from '@core/schema/ChangeWorkflowNode';
import { createRequest } from './Utils';
import { ChangeWorkflowNodeModel } from '@models/ChangeWorkflowNodeModel';

export class ChangeWorkflowNodeApi {
  static findAllWithPage(pagination: TableAffactedSafeType): RequestConfig<ResponseData<Page<ChangeWorkflowNode>>, PaginationRequest> {
    return {
      url: `${BaseURL.workflowNodes}/all`,
      method: 'POST',
      schema: createResponseSchema(createPageSchema(ChangeWorkflowNodeSchema)),
      data: tableAffectedToMultiColumnFilterPaginationRequestModel(pagination),
    };
  }

  static findAll(): RequestConfig<ResponseData<ChangeWorkflowNode[]>> {
    return {
      url: `${BaseURL.workflowNodes}/all`,
      method: 'GET',
      schema: createResponseSchema(createListSchema(ChangeWorkflowNodeSchema)),
    };
  }

  static deleteById(id: number): RequestConfig<ResponseData<void>> {
    return {
      url: `${BaseURL.workflowNodes}/${id}`,
      method: 'DELETE',
    };
  }

  static findById(id: number) {
    return createRequest({
      url: `${BaseURL.workflowNodes}/${id}`,
      method: 'GET',
      schema: createResponseSchema(ChangeWorkflowNodeSchema),
      pathVariable: {
        id,
      },
    });
  }

  static save(node: ChangeWorkflowNodeModel): RequestConfig<ResponseData<ChangeWorkflowNode>> {
    return {
      url: `${BaseURL.workflowNodes}`,
      method: 'POST',
      schema: createResponseSchema(ChangeWorkflowNodeSchema),
      data: node,
    };
  }
}
