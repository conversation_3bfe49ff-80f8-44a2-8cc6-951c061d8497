
.elementBorder{
  border: 1px solid var(--mantine-color-gray-4);
  border-radius: 5px;    
  box-shadow: 4px 4px 10px rgba(0, 0, 0, 0.1); 
  padding: var(--mantine-spacing-xs);
  margin-bottom: 'xs';
}


.elementInnerBorder{
  border: 1px solid var(--mantine-color-gray-4);
  border-radius: 5px;    
  padding: var(--mantine-spacing-xs);
  margin-bottom: var(--mantine-spacing-xs);
}

.dateRangeInputWrap {
  input{
    border: none;
    font-size: 'xs';
    width: '45%';
  }
}
   
.dateRangeWrapper {
  border: calc(0.0625rem * var(--mantine-scale)) solid var(--mantine-color-gray-4);
  border-radius: var(--mantine-radius-default);
}

.clipText {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.buttonSuccess {
  background-color: var(--mantine-color-violet-7);
  color: white;
  border: none;
  cursor: pointer;
  min-width: 70px;
}



