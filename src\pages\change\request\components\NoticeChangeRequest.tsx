import React, { useEffect, useRef } from 'react';
import { useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { Group, Stack } from '@mantine/core';
import { RichTextEditor } from '@mantine/tiptap';
import styles from '@resources/styles/ChangeTemplateBuilder.module.scss';
import { KanbanText } from 'kanban-design-system';
import { Control, Controller, useWatch } from 'react-hook-form';
import { ROW_HEIGHT } from '@common/constants/ChangeTemplateConstants';
import Underline from '@tiptap/extension-underline';
import { ChangeRequestModel } from '@core/schema/ChangeRequest';

type NoticeChangeRequestProps = {
  control: Control<ChangeRequestModel>;
};

export const NoticeChangeRequest: React.FC<NoticeChangeRequestProps> = ({ control }) => {
  const onChangeRef = useRef<(value: string) => void>();
  const value = useWatch({ control, name: 'notice' });

  const noticeEditor = useEditor({
    extensions: [StarterKit, Underline],
    content: value || '',
    editable: false,
    onUpdate: ({ editor }) => {
      onChangeRef.current?.(editor.getHTML());
    },
  });

  useEffect(() => {
    if (noticeEditor && noticeEditor.getHTML() !== control._formValues.notice) {
      noticeEditor.commands.setContent(control._formValues.notice || '', false);
    }
  }, [control._formValues.notice, noticeEditor]);

  return (
    <Stack gap='0' w='100%' pt='xs' h={ROW_HEIGHT * 6} mt='sm'>
      <Group justify='space-between'>
        <KanbanText size='sm' opacity={0.8} fw={500} mb='xs'>
          Change notice
        </KanbanText>
      </Group>
      <Controller
        name={`notice`}
        control={control}
        render={({ field: controllerField }) => {
          onChangeRef.current = controllerField.onChange;
          return (
            <RichTextEditor
              editor={noticeEditor}
              className={styles.richTextEditor}
              withTypographyStyles={false}
              styles={{
                root: {
                  height: '100%',
                  overflowY: 'auto',
                },
                content: {
                  height: '100%',
                },
                typographyStylesProvider: {
                  height: '100%',
                },
              }}>
              <RichTextEditor.Content />
            </RichTextEditor>
          );
        }}
      />
    </Stack>
  );
};
