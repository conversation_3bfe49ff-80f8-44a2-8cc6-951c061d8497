import { z } from 'zod';

export const UserPreferenceSchema = z.object({
  userId: z.number().nullish(),
  keyConfig: z.string().nullish(),
  content: z.string().nullish(),
  createdDate: z.string().nullish(),
  createdBy: z.string().nullish(),
  modifiedBy: z.string().nullish(),
  modifiedDate: z.string().nullish(),
});

export type UserPreference = z.infer<typeof UserPreferenceSchema>;
export const UserPreferenceListSchema = z.array(UserPreferenceSchema);
