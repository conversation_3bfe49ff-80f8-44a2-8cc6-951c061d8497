import { KanbanButton, KanbanIconButton, KanbanInput, KanbanSelect, KanbanTextarea, KanbanTitle } from 'kanban-design-system';

import React, { useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { useDisclosure } from '@mantine/hooks';
import { Box, Flex, SimpleGrid } from '@mantine/core';
import { zodResolver } from '@hookform/resolvers/zod';

import customStyled from '@resources/styles/Common.module.scss';
import { IconArrowLeft } from '@tabler/icons-react';
import { Controller, useForm, UseFormReturn } from 'react-hook-form';
import UnsaveConfirmModal from '@components/utils/ConfirmMessageUtils';

import { buildUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import { COMMON_DESCRIPTION_MAX_LENGTH, COMMON_MAX_LENGTH } from '@common/constants/ValidationConstant';
import equal from 'fast-deep-equal';
import { trimStringFields } from '@common/utils/Helpers';
import { ChangeStatusApi } from '@api/ChangeStatus';
import { ChangeStatusModel, ChangeStatusModelSchema } from '@models/ChangeStatusModel';
import { CHANGE_STAGE_LABEL, ChangeStageTypeEnum } from '@common/constants/ChageStageType';
import { buildNavigateState } from '@core/hooks/useSavedFilterCycle';
import { useBreadcrumbEntityName } from '@core/hooks/useBreadcrumbEntityName';
type DetailPageProps = {
  idProp?: number;
};

const DEFAULT_FORM_VALUE: ChangeStatusModel = {
  id: null,
  name: '',
  description: '',
  action: '',
  stage: undefined,
};

const SaveButton = ({ form }: { form: UseFormReturn<ChangeStatusModel> }) => {
  const navigate = useNavigate();
  const { mutate: saveUserMutate } = useMutate(ChangeStatusApi.save, {
    successNotification: 'Status saved successfully',
    onSuccess: () => {
      navigate(buildUrl(ROUTE_PATH.CONFIGURATION_CHANGE_STATUS), { state: buildNavigateState({ fromDetail: true }) });
    },
  });

  const onSubmit = useCallback(() => {
    form.trigger();

    const parsedData = ChangeStatusModelSchema.safeParse(form.getValues());
    if (parsedData.success) {
      const trimmed = trimStringFields(parsedData.data, false);
      saveUserMutate(trimmed);
    }
  }, [form, saveUserMutate]);

  return (
    <KanbanButton size='xs' onClick={onSubmit}>
      Save
    </KanbanButton>
  );
};

export type StringMapEntry<T> = {
  [key: string]: T;
};
export const ChangeStatusDetailtPage: React.FC<DetailPageProps> = () => {
  const id = Number(useParams().id);
  const { data } = useFetch(ChangeStatusApi.findById(id), { enabled: !!id });
  const [changeStatus, setChangeStatus] = useState<ChangeStatusModel>(DEFAULT_FORM_VALUE);
  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);
  useBreadcrumbEntityName(changeStatus.name);

  const navigate = useNavigate();

  const form = useForm<ChangeStatusModel>({
    defaultValues: DEFAULT_FORM_VALUE,
    resolver: zodResolver(ChangeStatusModelSchema),
    mode: 'onChange',
  });

  const { control } = form;

  useEffect(() => {
    if (id && data?.data) {
      setChangeStatus(data.data);
      form.reset({ ...data.data });
    }
  }, [form, id, data]);

  const handleExistWithoutSave = () => {
    const currentValues = form.getValues();

    const isInitialData = equal(currentValues, DEFAULT_FORM_VALUE);

    if (equal(currentValues, changeStatus) || isInitialData) {
      navigate(buildUrl(ROUTE_PATH.CONFIGURATION_CHANGE_STATUS), { state: buildNavigateState({ fromDetail: true }) });
    } else {
      openModal();
    }
  };

  return (
    <Box>
      <HeaderTitleComponent
        title=''
        rightSection={
          <Flex direction={'row'} align={'center'} gap={'xs'}>
            <KanbanButton size='xs' variant='light' onClick={handleExistWithoutSave}>
              Cancel
            </KanbanButton>
            <SaveButton form={form} />
          </Flex>
        }
        leftSection={
          <Flex direction={'row'} align={'center'} gap={'xs'}>
            <KanbanIconButton size='sm' variant='subtle' onClick={handleExistWithoutSave}>
              <IconArrowLeft />
            </KanbanIconButton>
            <KanbanTitle fz={'h4'}>{id ? 'Edit Status' : 'Add Status'}</KanbanTitle>
          </Flex>
        }
      />
      <Box className={customStyled.elementBorder}>
        <SimpleGrid cols={2} spacing='md'>
          <Controller
            name='name'
            control={control}
            render={({ field, fieldState }) => (
              <KanbanInput maxLength={COMMON_MAX_LENGTH} label='Status Name' {...field} withAsterisk error={fieldState.error?.message} />
            )}
          />
          <KanbanInput maxLength={COMMON_MAX_LENGTH} label='Action' {...form.register('action')} />
        </SimpleGrid>

        <SimpleGrid cols={2} spacing='md'>
          <KanbanTextarea minRows={4} maxLength={COMMON_DESCRIPTION_MAX_LENGTH} label='Description' {...form.register('description')} />
          <Controller
            name='stage'
            control={control}
            render={({ field, fieldState }) => (
              <KanbanSelect
                label='Stage'
                allowDeselect={false}
                required
                data={ChangeStageTypeEnum.options.map((key) => ({
                  value: key,
                  label: CHANGE_STAGE_LABEL[key],
                }))}
                value={field.value}
                onChange={(value) => {
                  field.onChange(value);
                }}
                error={fieldState.error?.message}
              />
            )}
          />
        </SimpleGrid>
      </Box>
      <UnsaveConfirmModal
        opened={openedModal}
        onClose={closeModal}
        onConfirm={() => navigate(buildUrl(ROUTE_PATH.CONFIGURATION_CHANGE_STATUS), { state: buildNavigateState({ fromDetail: true }) })}
      />
    </Box>
  );
};
export default ChangeStatusDetailtPage;
