import { PaginationRequest } from '@api/Type';
import { PaginationRequestModel } from '@models/EntityModelBase';
import { getDefaultTableAffected, SortOrder, type TableAffactedSafeType } from 'kanban-design-system';

export const DEFAULT_TABLE_AFFECTED: TableAffactedSafeType = {
  page: 1,
  sortOrder: SortOrder.ASC,
  rowsPerPage: 10,
  search: '',
  advancedFilterMapping: {},
  sortedBy: undefined,
};

export function tableAffectedToPaginationRequest<T>(tableAffected: TableAffactedSafeType<T>): PaginationRequest {
  return {
    page: tableAffected.page - 1,
    size: tableAffected.rowsPerPage || 10,
    search: tableAffected.search,
    sortBy: tableAffected.sortedBy?.toString(),
    sortOrder: tableAffected.sortOrder,
  };
}

export function tableAffectedToPaginationRequestModel<T>(tableAffected: TableAffactedSafeType<T>): PaginationRequestModel<T> {
  return {
    page: tableAffected.page - 1,
    size: tableAffected.rowsPerPage || 10,
    search: tableAffected.search,
    sortBy: tableAffected.sortedBy || ('createdDate' as keyof T),
    sortOrder: tableAffected.sortOrder,
  };
}

export function tableAffectedToMultiColumnFilterPaginationRequestModel<T>(tableAffected: TableAffactedSafeType<T>): PaginationRequestModel<T> {
  return {
    page: tableAffected.page - 1,
    size: tableAffected.rowsPerPage || 10,
    search: tableAffected.search,
    sortBy: tableAffected.sortedBy,
    sortOrder: tableAffected.sortOrder,
    advancedFilterMapping: tableAffected.advancedFilterMapping,
  };
}

export const initOrUpdatedFilterSearch = (prev?: TableAffactedSafeType, searchValue?: string) => {
  const prevFilter = prev || getDefaultTableAffected();
  return {
    ...prevFilter,
    search: searchValue ?? '',
  };
};
