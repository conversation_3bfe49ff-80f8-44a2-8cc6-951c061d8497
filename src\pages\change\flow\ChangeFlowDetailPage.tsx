import { KanbanButton, KanbanIconButton, KanbanTextarea, KanbanTitle } from 'kanban-design-system';

import React, { useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { useDisclosure } from '@mantine/hooks';
import { Box, Flex, SimpleGrid } from '@mantine/core';
import { zodResolver } from '@hookform/resolvers/zod';

import customStyled from '@resources/styles/Common.module.scss';
import { IconArrowLeft } from '@tabler/icons-react';
import { Controller, useForm, UseFormReturn } from 'react-hook-form';
import UnsaveConfirmModal from '@components/utils/ConfirmMessageUtils';

import { buildUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import { COMMON_DESCRIPTION_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { ChangeFlowModel, ChangeFlowModelSchema } from '@models/ChangeFlowModel';
import ChangeFlowDesigner from './FlowDiagram';
import useMutate from '@core/hooks/useMutate';
import { ChangeFlowApi } from '@api/ChangeFlowApi';
import { trimStringFields } from '@common/utils/Helpers';
import useFetch from '@core/hooks/useFetch';
import { buildNavigateState } from '@core/hooks/useSavedFilterCycle';
import { useBreadcrumbEntityName } from '@core/hooks/useBreadcrumbEntityName';
import { NameValidationInput } from './NameValidationInput';
import { Edge, Node } from 'reactflow';

type DetailPageProps = {
  idProp?: number;
};

const DEFAULT_FORM_VALUE: ChangeFlowModel = {
  id: null,
  name: '',
  flowNodes: '',
  flowEdges: '',
};
const SaveButton = ({ form }: { form: UseFormReturn<ChangeFlowModel> }) => {
  const navigate = useNavigate();
  const { mutate: saveUserMutate } = useMutate(ChangeFlowApi.save, {
    successNotification: 'Change Flow saved successfully',
    onSuccess: () => {
      navigate(buildUrl(ROUTE_PATH.CONFIGURATION_CHANGE_FLOW), { state: buildNavigateState({ fromDetail: true }) });
    },
  });

  const onSubmit = useCallback(() => {
    form.trigger();
    const parsedData = ChangeFlowModelSchema.safeParse(form.getValues());
    if (parsedData.success) {
      const trimmed = trimStringFields(parsedData.data, false);
      saveUserMutate(trimmed);
    }
  }, [form, saveUserMutate]);

  return (
    <KanbanButton size='xs' onClick={onSubmit}>
      Save
    </KanbanButton>
  );
};

export const ChangeFlowDetailtPage: React.FC<DetailPageProps> = () => {
  const id = Number(useParams().id);
  const { data } = useFetch(ChangeFlowApi.findById(id), { enabled: !!id });
  const [changeFlow, setChangeFlow] = useState<ChangeFlowModel>(DEFAULT_FORM_VALUE);
  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);

  useBreadcrumbEntityName(changeFlow.name);
  const navigate = useNavigate();

  const form = useForm<ChangeFlowModel>({
    defaultValues: DEFAULT_FORM_VALUE,
    resolver: zodResolver(ChangeFlowModelSchema),
    mode: 'onChange',
  });

  const { control } = form;

  useEffect(() => {
    if (id && data?.data) {
      setChangeFlow(data.data);
      form.reset({ ...data.data });
    }
  }, [form, id, data]);

  const handleExistWithoutSave = () => {
    const currentValues = form.getValues();

    // For new flows, check if only default nodes exist
    if (!id) {
      if (currentValues.name !== changeFlow.name || currentValues.description !== changeFlow.description) {
        openModal();
        return;
      }
      // Check if it's just the initial setup with start, end and submission nodes
      const nodes = currentValues.flowNodes ? JSON.parse(currentValues.flowNodes) : [];
      const edges = currentValues.flowEdges ? JSON.parse(currentValues.flowEdges) : [];

      const isDefaultSetup = nodes.length <= 3 && edges.length <= 1;

      if (isDefaultSetup) {
        navigate(buildUrl(ROUTE_PATH.CONFIGURATION_CHANGE_FLOW), { state: buildNavigateState({ fromDetail: true }) });
        return;
      }
    }

    // Compare only essential properties instead of deep comparison
    const hasChanges = () => {
      if (currentValues.name !== changeFlow.name || currentValues.description !== changeFlow.description) {
        return true;
      }

      // Compare node IDs
      const currentNodes = currentValues.flowNodes ? JSON.parse(currentValues.flowNodes) : [];
      const originalNodes = changeFlow.flowNodes ? JSON.parse(changeFlow.flowNodes) : [];
      if (currentNodes.length !== originalNodes.length) {
        return true;
      }

      // Check if node IDs have changed
      const currentNodeIds = new Set(currentNodes.map((node: Node) => node.id));
      const originalNodeIds = new Set(originalNodes.map((node: Node) => node.id));
      if (currentNodeIds.size !== originalNodeIds.size) {
        return true;
      }
      for (const nodeId of currentNodeIds) {
        if (!originalNodeIds.has(nodeId)) {
          return true;
        }
      }

      // Compare edge IDs
      const currentEdges = currentValues.flowEdges ? JSON.parse(currentValues.flowEdges) : [];
      const originalEdges = changeFlow.flowEdges ? JSON.parse(changeFlow.flowEdges) : [];
      if (currentEdges.length !== originalEdges.length) {
        return true;
      }

      // Check if edge IDs have changed
      const currentEdgeIds = new Set(currentEdges.map((edge: Edge) => edge.id));
      const originalEdgeIds = new Set(originalEdges.map((edge: Edge) => edge.id));
      if (currentEdgeIds.size !== originalEdgeIds.size) {
        return true;
      }
      for (const edgeId of currentEdgeIds) {
        if (!originalEdgeIds.has(edgeId)) {
          return true;
        }
      }
      return false;
    };

    if (hasChanges()) {
      openModal();
    } else {
      navigate(buildUrl(ROUTE_PATH.CONFIGURATION_CHANGE_FLOW), { state: buildNavigateState({ fromDetail: true }) });
    }
  };
  return (
    <Box>
      <HeaderTitleComponent
        title=''
        rightSection={
          <Flex direction={'row'} align={'center'} gap={'xs'}>
            <KanbanButton size='xs' variant='light' onClick={handleExistWithoutSave}>
              Cancel
            </KanbanButton>
            <SaveButton form={form} />
          </Flex>
        }
        leftSection={
          <Flex direction={'row'} align={'center'} gap={'xs'}>
            <KanbanIconButton size='sm' variant='subtle' onClick={handleExistWithoutSave}>
              <IconArrowLeft />
            </KanbanIconButton>
            <KanbanTitle fz={'h4'}>{id ? 'Edit Change Flow' : 'Add Change Flow'}</KanbanTitle>
          </Flex>
        }
      />
      <Box className={customStyled.elementBorder} mb='lg'>
        <SimpleGrid cols={2} spacing='md'>
          <NameValidationInput form={form} id={id} name={data?.data?.name} />
          <Controller
            name='description'
            control={control}
            render={({ field, fieldState }) => (
              <KanbanTextarea
                minRows={4}
                maxLength={COMMON_DESCRIPTION_MAX_LENGTH}
                label='Description'
                {...field}
                value={field.value || ''}
                error={fieldState?.error?.message}
              />
            )}
          />
        </SimpleGrid>
      </Box>
      <ChangeFlowDesigner form={form} />
      <UnsaveConfirmModal
        opened={openedModal}
        onClose={closeModal}
        onConfirm={() => navigate(buildUrl(ROUTE_PATH.CONFIGURATION_CHANGE_FLOW), { state: buildNavigateState({ fromDetail: true }) })}
      />
    </Box>
  );
};
export default ChangeFlowDetailtPage;
