import { ChangeRequestApprovalStatus, ChangeRequestApprovalStatusEnum } from '@core/schema/ChangeRequestApproval';

export enum ChangeRequestApprovalMode {
  SUBMISSION = 'SUBMISSION',
  APPROVAL = 'APPROVAL',
}

export enum ChangeRequestApprovalActions {
  ACCEPT = 'ACCEPT',
  REJECT = 'REJECT',
}

type ApprovalActionOption = {
  label: string;
  value: ChangeRequestApprovalStatus;
};

export const APPROVAL_ACTION_OPTIONS: ApprovalActionOption[] = [
  { label: 'Accept', value: ChangeRequestApprovalStatusEnum.Enum.ACCEPT },
  { label: 'Reject', value: ChangeRequestApprovalStatusEnum.Enum.REJECT },
];
