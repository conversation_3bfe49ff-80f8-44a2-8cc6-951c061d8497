import React, { useMemo, useState } from 'react';
import { Control, Controller } from 'react-hook-form';

import ApprovalUserSelect from './ApprovalUserSelect';
import { ChangeRequestRole, ChangeRequestRoleList } from '@core/schema/ChangeRequestRole';
import { KanbanAccordion, KanbanText } from 'kanban-design-system';
import { EntityAction } from '@common/constants/EntityActionConstants';
import { ChangeRequestApprovalMode } from '@common/constants/ChangeRequestApprovalConstants';
import { ChangeRequestApprovalComponent } from './approval/ChangeRequestApprovalComponent';
import { Stack } from '@mantine/core';
import { ApprovalNotificationModal } from './notify/ApprovalNotificationModal';
import { MappedUser } from './ChangeRequestDetailRole';
import { ChangeStageType } from '@common/constants/ChageStageType';

interface NodeApprovalComponentProps {
  role: ChangeRequestRole;
  control: Control<ChangeRequestRoleList>;
  index: number;
  mode: EntityAction;
  modeApproval: ChangeRequestApprovalMode;
  refetchChangeRequestRoles: () => void;
  stage?: ChangeStageType;
}
const NodeApproval: React.FC<NodeApprovalComponentProps> = ({ control, index, mode, modeApproval, refetchChangeRequestRoles, role, stage }) => {
  const mappedUsers: MappedUser[] = useMemo(() => {
    return (
      role?.workflows
        ?.flatMap(
          (workflow) =>
            workflow.groups?.flatMap((group) =>
              (group.users ?? []).map((user) => ({
                username: user.username ?? '',
                displayUsername: user.displayUsername ?? '',
                usernameActive: user.usernameActive ?? false,
              })),
            ) ?? [],
        )
        .filter((u) => u.usernameActive) ?? []
    );
  }, [role?.workflows]);
  const [selectedUsers, setSelectedUsers] = useState<MappedUser[]>([]);
  const handleCheckboxChange = (user: MappedUser, checked: boolean) => {
    const updateSelected = checked ? [...selectedUsers, user] : selectedUsers.filter((u) => u.username !== user.username);
    setSelectedUsers(updateSelected.filter((value) => value.usernameActive));
  };

  return (
    <Controller
      control={control}
      name={`activeRoles.${index}`}
      render={({ field }) => (
        <KanbanAccordion
          w='100%'
          value={field.value}
          onChange={(value) => {
            if (typeof value === 'string' || value === null) {
              field.onChange(value);
            }
          }}
          data={[
            {
              content: (
                <Stack align='flex-start' gap='xs' w='100%'>
                  {ChangeRequestApprovalMode.SUBMISSION === modeApproval ? (
                    <ApprovalUserSelect
                      workflowIdx={0}
                      cabGroupIdx={0}
                      cabGroupUserIdx={0}
                      control={control}
                      roleIdx={index}
                      mode={mode}
                      changeFlowNodeId={role.changeFlowNodeId ?? 0}
                      modeApproval={modeApproval}
                    />
                  ) : (
                    <>
                      <ApprovalNotificationModal
                        changeRequestId={role.changeRequestId ?? 0}
                        changeFlowNodeId={role?.changeFlowNode?.id || 0}
                        selectedUsers={mappedUsers}
                        stage={stage}
                      />
                      <ChangeRequestApprovalComponent
                        workflowIdx={0}
                        cabGroupIdx={0}
                        cabGroupUserIdx={0}
                        control={control}
                        onDelete={undefined}
                        roleIdx={index}
                        mode={mode}
                        changeFlowNodeId={role.changeFlowNodeId ?? 0}
                        modeApproval={modeApproval}
                        handleCheckboxChange={handleCheckboxChange}
                        refetchChangeRequestRoles={refetchChangeRequestRoles}
                      />
                    </>
                  )}
                </Stack>
              ),
              title: <KanbanText fw={500}>{`Level ${index + 1}: ${role.changeFlowNode?.name || ''}`}</KanbanText>,
              key: role.changeFlowNode?.name || '',
            },
          ]}
        />
      )}
    />
  );
};
export default NodeApproval;
