import { z } from 'zod';
import { CustomFieldTypeEnum } from '@common/constants/CustomFieldType';
import { COMMON_DESCRIPTION_MAX_LENGTH, COMMON_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { DUPLICATE_VALUE, INPUT_REQUIRE, INVALID_VALUE } from '@core/message/MesageConstant';
import { QueryRuleGroupTypeSchema } from '@core/schema/RuleGroupCondition';

export const PICK_LIST_OPTION_LENGTH = 1000;

export const FieldConstraintModelSchema = z.object({
  defaultValue: z.preprocess((val) => {
    if (val === '' || val === null || val === undefined) {
      return undefined;
    }
    return typeof val === 'number' ? val.toString() : val;
  }, z.string().optional().nullable()),

  min: z.preprocess((val) => {
    if (val === '' || val === null || val === undefined) {
      return undefined;
    }
    return Number(val);
  }, z.number().int().optional().nullable()),

  max: z.preprocess((val) => {
    if (val === '' || val === null || val === undefined) {
      return undefined;
    }
    return Number(val);
  }, z.number().int().optional().nullable()),
});

export type FieldConstraintModel = z.infer<typeof FieldConstraintModelSchema>;

export const CustomFieldConditionModelSchema = z.object({
  tableReference: z.string(),
  columnReference: z.string(),
  ruleGroup: QueryRuleGroupTypeSchema.optional(),
});
export type CustomFieldConditionModel = z.infer<typeof CustomFieldConditionModelSchema>;

export const PicklistOptionModelSchema = z.object({
  value: z.string(),
  isDefault: z.boolean(),
  position: z.number().int(),
});
export type PicklistOption = z.infer<typeof PicklistOptionModelSchema>;

export const CustomFieldModelSchema = z
  .object({
    id: z.number().nullish(),
    name: z.string().trim().min(1, { message: INPUT_REQUIRE }).max(COMMON_MAX_LENGTH),
    isMultiple: z.boolean().nullish(),
    isReference: z.boolean().nullish(),
    description: z.string().max(COMMON_DESCRIPTION_MAX_LENGTH).nullish(),
    type: CustomFieldTypeEnum,
    fieldConstraint: FieldConstraintModelSchema.nullish(),
    condition: CustomFieldConditionModelSchema.nullish(),
    picklistOptions: z.array(PicklistOptionModelSchema).nullish(),
  })
  .superRefine((data, ctx) => {
    const { condition, fieldConstraint, isReference, picklistOptions, type } = data;

    if (type === CustomFieldTypeEnum.Enum.PICKLIST && isReference === true) {
      if (!condition || typeof condition !== 'object' || !condition.tableReference) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: INPUT_REQUIRE,
          path: ['condition', 'tableReference'],
        });
      }
    }

    if (type === CustomFieldTypeEnum.Enum.PICKLIST && isReference === false && Array.isArray(picklistOptions)) {
      const seen = new Set<string>();

      picklistOptions.forEach((option, index) => {
        const value = option.value.trim();

        if (!value) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: INPUT_REQUIRE,
            path: ['picklistOptions', index, 'value'],
          });
        } else if (seen.has(value)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: DUPLICATE_VALUE,
            path: ['picklistOptions', index, 'value'],
          });
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: DUPLICATE_VALUE,
            path: ['isReference'],
          });
        }

        seen.add(value);
      });
    }

    if (!fieldConstraint) {
      return;
    }

    const { defaultValue, max, min } = fieldConstraint;

    if (typeof min === 'number' && typeof max === 'number' && min >= max) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: INVALID_VALUE,
        path: ['fieldConstraint', 'min'],
      });
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: INVALID_VALUE,
        path: ['fieldConstraint', 'max'],
      });
    }

    if (!defaultValue) {
      return;
    }

    if (type === CustomFieldTypeEnum.Enum.NUMBER) {
      const num = Number(defaultValue);
      if (isNaN(num) || (typeof min === 'number' && num < min) || (typeof max === 'number' && num > max)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: INVALID_VALUE,
          path: ['fieldConstraint', 'defaultValue'],
        });
      }
    }

    if (type === CustomFieldTypeEnum.Enum.SINGLE_LINE || type === CustomFieldTypeEnum.Enum.MULTI_LINE) {
      const length = defaultValue.length;
      if ((typeof min === 'number' && length < min) || (typeof max === 'number' && length > max)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: INVALID_VALUE,
          path: ['fieldConstraint', 'defaultValue'],
        });
      }
    }
  });

export type CustomFieldModel = z.infer<typeof CustomFieldModelSchema>;
