import React from 'react';
import { IconCalendar, IconCheckbox, IconLetterCase, IconNumbers, IconPilcrow, IconSelect, IconTextSize } from '@tabler/icons-react';
import { KanbanIconButton } from 'kanban-design-system';

const icons: { [key: string]: React.FC<any> } = {
  IconLetterCase,
  IconTextSize,
  IconCalendar,
  IconCheckbox,
  IconPilcrow,
  IconNumbers,
  IconSelect,
};

export const GetIcon = ({ color, name }: { name: string; color: string }) => {
  const IconComponent = icons[name];
  return IconComponent ? (
    <KanbanIconButton variant={'transparent'} size={'xs'} c={color}>
      <IconComponent stroke={2} size={16} />
    </KanbanIconButton>
  ) : null;
};
