import AmtModal from '@components/AmtModal';
import { ConfirmConfig } from '@core/hooks/AppConfigTypes';
import { KanbanButton, KanbanText } from 'kanban-design-system';
import React from 'react';

export function deleteConfirm(nameList?: string[]): ConfirmConfig {
  return {
    title: 'Confirm delete',
    children: <DeleteConfirmMessage nameList={nameList ?? []} />,
  };
}

const DeleteConfirmMessage: React.FC<{ nameList: string[] }> = ({ nameList }) => {
  const sortedList = nameList.sort((a, b) => a.localeCompare(b));
  return (
    <KanbanText>
      This action cannot be undone. Do you still want to delete{' '}
      {sortedList.length === 1 ? (
        <>
          this item <strong>{sortedList[0]}</strong>?
        </>
      ) : (
        <>
          these items?
          <ul>
            {sortedList.map((item, index) => (
              <li key={index}>
                <strong>{item}</strong>
              </li>
            ))}
          </ul>
        </>
      )}
    </KanbanText>
  );
};

interface UnsaveConfirmModalProps {
  opened: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

const UnsaveConfirmModal: React.FC<UnsaveConfirmModalProps> = ({ onClose, onConfirm, opened }) => {
  return (
    <AmtModal
      opened={opened}
      title={'Unsaved changes'}
      onClose={onClose}
      actions={
        <KanbanButton variant='filled' onClick={onConfirm}>
          Confirm
        </KanbanButton>
      }>
      <KanbanText>You haven&apos;t saved changes. Do you still want to leave?</KanbanText>
    </AmtModal>
  );
};

export default UnsaveConfirmModal;
