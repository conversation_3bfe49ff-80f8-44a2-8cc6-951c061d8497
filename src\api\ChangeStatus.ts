import { BaseURL } from '@common/constants/BaseUrl';
import { RequestConfig } from '@core/api';
import { PaginationRequest } from './Type';
import { createRequest } from './Utils';
import { tableAffectedToMultiColumnFilterPaginationRequestModel } from '@common/utils/TableUtils';
import { TableAffactedSafeType } from 'kanban-design-system';
import { ResponseData, createResponseSchema, Page, createPageSchema, createListSchema } from '@core/schema/Common';
import { ChangeStatus, ChangeStatusSchema } from '@core/schema/ChangeStatus';
import { z } from 'zod';
import { ChangeStatusModel, ChangeStatusModelSchema } from '@models/ChangeStatusModel';
import qs from 'qs';

export class ChangeStatusApi {
  static findAll(pagination: TableAffactedSafeType): RequestConfig<ResponseData<Page<ChangeStatus>>, PaginationRequest> {
    return {
      url: `${BaseURL.changeStatus}/all`,
      method: 'POST',
      schema: createResponseSchema(createPageSchema(ChangeStatusSchema)),
      data: tableAffectedToMultiColumnFilterPaginationRequestModel(pagination),
    };
  }

  static findById(id: number) {
    return createRequest({
      url: `${BaseURL.changeStatus}/:id`,
      method: 'GET',
      schema: createResponseSchema(ChangeStatusSchema),
      pathVariable: {
        id,
      },
    });
  }

  static save(user: ChangeStatusModel): RequestConfig<ResponseData<ChangeStatus>> {
    return {
      url: `${BaseURL.changeStatus}`,
      method: 'POST',
      schema: createResponseSchema(ChangeStatusSchema),
      data: user,
    };
  }

  static deleteById(id: number): RequestConfig<ResponseData<void>> {
    return {
      url: `${BaseURL.changeStatus}/${id}`,
      method: 'DELETE',
    };
  }

  static deleteByIdIn(ids: number[]): RequestConfig<ResponseData<void>> {
    return {
      url: `${BaseURL.changeStatus}`,
      method: 'DELETE',
      params: { ids },
      paramsSerializer: (params) => qs.stringify(params, { arrayFormat: 'repeat' }),
    };
  }

  static existsByName({ id, name }: { id: number | undefined; name: string }) {
    return createRequest({
      url: `${BaseURL.changeStatus}/${id}/exists?name=${name}`,
      method: 'GET',
      schema: createResponseSchema(z.boolean()),
    });
  }

  static findAllStatusInSameStageByStatusId(statusId: number) {
    return createRequest({
      url: `${BaseURL.changeStatus}/${statusId}/related-statuses`,
      method: 'GET',
      schema: createResponseSchema(createListSchema(ChangeStatusModelSchema)),
    });
  }
}
