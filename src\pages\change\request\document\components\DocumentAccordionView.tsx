import React, { useMemo } from 'react';
import { KanbanAccordion, KanbanAccordionData, KanbanText } from 'kanban-design-system';
import { ScrollArea } from '@mantine/core';
import { AccordionPanelView } from './AccordionPanelView';
import { ChangeRequestDocumentGroupModel, ChangeRequestSendToOwnerRequest } from '@models/ChangeRequestDocumentGroupModel';

interface Props {
  changeRequestId: number;
  documentGroups: ChangeRequestDocumentGroupModel[];
  openedItems: string[];
  onChange: (value: string[]) => void;
  selectedItems: ChangeRequestSendToOwnerRequest[];
  onToggle: (referenceId: number, username: string, checked: boolean) => void;
}

const DocumentAccordionView: React.FC<Props> = ({ changeRequestId, documentGroups, onChange, onToggle, openedItems, selectedItems }) => {
  const docsAccordionData = useMemo<KanbanAccordionData[]>(() => {
    return documentGroups.map((documentGroup) => ({
      key: documentGroup.id.toString(),
      title: <KanbanText fw={500}>{documentGroup.name}</KanbanText>,
      content: (
        <ScrollArea>
          <AccordionPanelView changeRequestId={changeRequestId} documentGroup={documentGroup} selectedItems={selectedItems} onToggle={onToggle} />
        </ScrollArea>
      ),
    }));
  }, [changeRequestId, documentGroups, onToggle, selectedItems]);

  return (
    <KanbanAccordion
      multiple
      chevronPosition='left'
      value={openedItems}
      onChange={(value) => {
        const newValue = Array.isArray(value) ? value : [];
        onChange(newValue);
      }}
      data={docsAccordionData}
    />
  );
};

export default DocumentAccordionView;
