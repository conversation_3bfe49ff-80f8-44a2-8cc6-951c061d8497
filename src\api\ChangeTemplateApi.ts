import { BaseURL } from '@common/constants/BaseUrl';
import { createRequest } from './Utils';
import { createPageSchema, createResponseSchema } from '@core/schema/Common';
import { TableAffactedSafeType } from 'kanban-design-system';
import { tableAffectedToMultiColumnFilterPaginationRequestModel } from '@common/utils/KanbanTableUtils';
import { ChangeTemplate, ChangeTemplateSchema } from '@core/schema/ChangeTemplate';

export class ChangeTemplateApi {
  static findAll(data: TableAffactedSafeType) {
    return createRequest({
      url: `${BaseURL.changeTemplate}/all`,
      method: 'POST',
      schema: createResponseSchema(createPageSchema(ChangeTemplateSchema)),
      data: tableAffectedToMultiColumnFilterPaginationRequestModel(data),
      params: {},
    });
  }

  static getDetail(id: number) {
    return createRequest({
      url: `${BaseURL.changeTemplate}/${id}`,
      method: 'GET',
      schema: createResponseSchema(ChangeTemplateSchema),
    });
  }

  static getSameNameDetail(id: number, name?: string) {
    return createRequest({
      url: `${BaseURL.changeTemplate}/${id}/check-name?name=${name}`,
      method: 'GET',
      schema: createResponseSchema(ChangeTemplateSchema || null),
    });
  }

  static saveOrUpdate(data: ChangeTemplate) {
    return createRequest({
      url: `${BaseURL.changeTemplate}/${data.id}`,
      method: 'PUT',
      schema: createResponseSchema(ChangeTemplateSchema),
      data,
    });
  }

  static deleteByIds(data: number[]) {
    return createRequest({
      url: BaseURL.changeTemplate,
      method: 'DELETE',
      schema: createResponseSchema(createPageSchema(ChangeTemplateSchema)),
      data,
    });
  }
}
