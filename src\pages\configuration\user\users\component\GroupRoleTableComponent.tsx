import { useState, useEffect, useMemo } from 'react';
import { Box } from '@mantine/core';
import { KanbanTableProps, KanbanTable, TableAffactedSafeType, KanbanText, getDefaultTableAffected } from 'kanban-design-system';
import React from 'react';
import { GroupRole, GroupUser } from '@core/schema/Group';
import useFetch from '@core/hooks/useFetch';
import { GroupRoleModel } from '@models/GroupRoleUserModel';
import equal from 'fast-deep-equal';
import { UserApi } from '@api/UserApi';
import { LOAD_ITEM_ROLE_GROUP_MAX_LENGTH } from '@common/constants/ValidationConstant';

export const GroupRoleTableComponent: React.FC<{
  toDeletes: GroupUser[];
  toInserts: GroupUser[];
  username?: string;
}> = ({ toDeletes, toInserts, username }) => {
  const [listData, setListData] = useState<GroupRole[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const [tableAffected, setTableAffectedChange] = useState<TableAffactedSafeType>();

  const { data: userRoles } = useFetch(UserApi.findAllGroupRolesByUsername(username || '', tableAffected || getDefaultTableAffected()), {
    enabled: !!username && !!tableAffected,
  });

  const { data: insertedRoles } = useFetch(
    UserApi.findAllRolesByGroupIdIn(toInserts?.map((e) => e.groupId).filter((id): id is number => id !== null && id !== undefined) || [], {
      ...getDefaultTableAffected(),
      page: 1,
      rowsPerPage: LOAD_ITEM_ROLE_GROUP_MAX_LENGTH,
    }),
    { enabled: toInserts.length > 0 },
  );

  const { data: deletedRoles } = useFetch(
    UserApi.findAllRolesByGroupIdIn(toDeletes?.map((e) => e.groupId).filter((id): id is number => id !== null && id !== undefined) || [], {
      ...getDefaultTableAffected(),
      page: 1,
      rowsPerPage: LOAD_ITEM_ROLE_GROUP_MAX_LENGTH,
    }),
    { enabled: toDeletes.length > 0 },
  );

  useEffect(() => {
    if (userRoles?.data?.content) {
      const results = userRoles.data.content;
      setListData(results);
      setTotalRecords(userRoles.data.totalElements);
    }
  }, [userRoles]);

  const createTableProps = (title: string, data: GroupRoleModel[]): KanbanTableProps<GroupRoleModel> => ({
    columns: [
      { name: 'roleName', title: 'Role Name' },
      { name: 'groupName', title: 'Group Name' },
    ],
    data,
    title,
    showNumericalOrderColumn: true,
  });

  const toInsertTable = useMemo(() => createTableProps('', insertedRoles?.data?.content || []), [insertedRoles]);
  const toDeleteTable = useMemo(() => createTableProps('', deletedRoles?.data?.content || []), [deletedRoles]);
  const userRoleTable: KanbanTableProps<GroupRole> = useMemo(() => {
    const tblProps: KanbanTableProps<GroupRole> = {
      title: '',
      columns: [
        {
          name: 'roleName',
          title: 'Role Name',
        },
        {
          name: 'groupName',
          title: 'Group Name',
        },
      ],
      data: listData,
      serverside: {
        totalRows: totalRecords,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffected, dataSet)) {
            setTableAffectedChange((prev) => (prev ? { ...dataSet, advancedFilterMapping: prev.advancedFilterMapping } : { ...dataSet }));
          }
        },
      },
      pagination: {
        enable: true,
      },
      showNumericalOrderColumn: true,
    };
    return tblProps;
  }, [listData, totalRecords, tableAffected]);

  return (
    <Box>
      <>
        {insertedRoles?.data?.content && (
          <>
            <KanbanText fw='bold' size='md' c={'green'}>
              Roles granted Group to be added
            </KanbanText>
            <KanbanTable {...toInsertTable} />
          </>
        )}

        {deletedRoles?.data?.content && (
          <>
            <KanbanText mt='sm' fw='bold' size='md' c={'red'}>
              Roles granted Group to be deleted
            </KanbanText>
            <KanbanTable {...toDeleteTable} />
          </>
        )}

        {username && (
          <>
            <KanbanText mt='sm' fw='bold' size='md'>
              Roles granted Group
            </KanbanText>
            <KanbanTable {...userRoleTable} />{' '}
          </>
        )}
      </>
    </Box>
  );
};
