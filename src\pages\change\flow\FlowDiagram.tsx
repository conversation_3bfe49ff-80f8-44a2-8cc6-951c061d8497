import React, { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import React<PERSON>low, {
  useNodesState,
  useEdgesState,
  ReactFlowProvider,
  Panel,
  MarkerType,
  addEdge,
  Connection,
  XYPosition,
  applyEdgeChanges,
  applyNodeChanges,
  OnEdgesChange,
  OnNodesChange,
  Edge,
  Background,
  ReactFlowInstance,
} from 'reactflow';

import StageNode from './nodes/StageNode';
import OtherNode from './nodes/OtherNode';
import { Box, ComboboxItem } from '@mantine/core';
import { ChangeFlowModel, ChangeFlowNodeModel, ChangeFlowNodeType, ChangeFlowNotificationModel } from '@models/ChangeFlowModel';
import {
  CHANGE_STAGE_LABEL,
  ChangeStageType,
  ChangeStageTypeEnum,
  ExtendedChangeStageType,
  ExtendedChangeStageTypeEnum,
} from '@common/constants/ChageStageType';
import { NodeSidebar } from './NodeSidebar';
import { Node } from 'reactflow';
import OtherNodePopup from './popups/OtherNodePopup';
import { UseFormReturn, useWatch } from 'react-hook-form';
import useFetch from '@core/hooks/useFetch';
import { getDefaultTableAffected, KanbanIconButton } from 'kanban-design-system';
import { useParams } from 'react-router-dom';
import { ChangeStatus, ChangeStatusMinimal } from '@core/schema/ChangeStatus';
import { ChangeStatusApi } from '@api/ChangeStatus';
import fixStyles from './FlowDiagram.module.scss';
import {
  DRAFT_NODE_POSITION,
  FLOW_DEFAULTS,
  HANDLE_TYPES,
  KEYBOARD_SHORTCUTS,
  KeyboardShortcutEvent,
  ZOOM_CONFIG,
} from '@common/constants/ReactFlowConstant';
import { IconZoomIn, IconZoomOut, IconScan } from '@tabler/icons-react';

type ChangeFlowDesignerProps = {
  form: UseFormReturn<ChangeFlowModel>;
};

export const initialNodes: Node[] = [
  {
    id: FLOW_DEFAULTS.START_NODE_ID,
    type: HANDLE_TYPES.START,
    position: { x: 100, y: 100 },
    data: { label: 'Start' },
    deletable: false,
  },
  {
    id: FLOW_DEFAULTS.END_NODE_ID,
    type: HANDLE_TYPES.END,
    position: { x: 900, y: 100 },
    data: { label: 'End' },
    deletable: false,
  },
];

const nodeTypes = Object.fromEntries(
  ExtendedChangeStageTypeEnum.options.map((value) => [
    value,
    (ChangeStageTypeEnum.options as readonly string[]).includes(value) ? StageNode : OtherNode,
  ]),
);

const groupChangeStatusMinimalByStage = (list: ChangeStatus[]): Partial<Record<ChangeStageType, ChangeStatusMinimal[]>> => {
  const grouped = list.reduce<Partial<Record<ChangeStageType, ChangeStatusMinimal[]>>>((acc, item) => {
    if (!acc[item.stage]) {
      acc[item.stage] = [];
    }
    acc[item.stage]?.push(item);
    return acc;
  }, {});

  const draftStatus: ChangeStatusMinimal = {
    id: 0,
    name: 'Draft',
    stage: ChangeStageTypeEnum.Enum.SUBMISSION_PLANNING,
  };

  grouped[ChangeStageTypeEnum.Enum.SUBMISSION_PLANNING] = [...(grouped[ChangeStageTypeEnum.Enum.SUBMISSION_PLANNING] || []), draftStatus];

  return grouped;
};

const MAX_CHANGE_STATUS = 200;
const ChangeFlowDesigner: React.FC<ChangeFlowDesignerProps> = ({ form }) => {
  const id = Number(useParams().id);

  const flowNodes = useWatch({ control: form.control, name: 'flowNodes' });
  const flowEdges = useWatch({ control: form.control, name: 'flowEdges' });
  const formNodes = useWatch({ control: form.control, name: 'nodes' });
  const notifications = useWatch({ control: form.control, name: 'notifications' });

  const [nodes, setNodes] = useNodesState([]);
  const [edges, setEdges] = useEdgesState([]);

  const [showNodePopup, setShowNodePopup] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
  const [activePopup, setActivePopup] = useState<ChangeFlowNodeType>();

  const [usedStages, setUsedStages] = useState<Set<ExtendedChangeStageType>>(new Set());
  const [isDiagramFocused, setIsDiagramFocused] = useState<boolean>(false);

  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const reactFlowInstance = useRef<ReactFlowInstance | null>(null);

  const zoomIn = useCallback((): void => {
    reactFlowInstance.current?.zoomIn({ duration: ZOOM_CONFIG.duration });
  }, []);

  const zoomOut = useCallback((): void => {
    reactFlowInstance.current?.zoomOut({ duration: ZOOM_CONFIG.duration });
  }, []);

  const zoomToFit = useCallback((): void => {
    reactFlowInstance.current?.fitView({
      duration: ZOOM_CONFIG.duration,
      maxZoom: ZOOM_CONFIG.maxZoom,
      minZoom: ZOOM_CONFIG.minZoom,
      padding: ZOOM_CONFIG.padding,
    });
  }, []);

  const { data: ChangeStatusMinimales, refetch: refetchChangeStatusMinimales } = useFetch(
    ChangeStatusApi.findAll({ ...getDefaultTableAffected(), page: 1, rowsPerPage: MAX_CHANGE_STATUS }),
    { showLoading: false },
  );

  const groupedStatus = useMemo(() => {
    if (!ChangeStatusMinimales?.data?.content) {
      return {};
    }
    return groupChangeStatusMinimalByStage(ChangeStatusMinimales.data.content);
  }, [ChangeStatusMinimales?.data?.content]);

  const otherNodeOptions: ComboboxItem[] = useMemo(() => {
    const nodeModels: ChangeFlowNodeModel[] = formNodes ?? [];
    return nodeModels.map((node) => ({
      value: node.nodeId,
      label: node.name,
    }));
  }, [formNodes]);

  const handleNodeUpdate = useCallback(
    (nodeId: string, notifications: ChangeFlowNotificationModel[]) => {
      const newNotifications = notifications ?? [];
      const mergedMap = new Map<string, ChangeFlowNotificationModel>();

      for (const noti of newNotifications) {
        if (noti.statusId && noti.emailTemplateId) {
          mergedMap.set(noti.statusId.toString(), noti);
        }
      }

      const updatedNotifications = Array.from(mergedMap.values());

      setNodes((nds) => nds.map((n) => (n.id === nodeId ? { ...n, data: { ...n.data, notifications } } : n)));
      form.setValue('notifications', updatedNotifications);
    },
    [form, setNodes],
  );

  const onContainerFocus = useCallback((): void => {
    setIsDiagramFocused(true);
  }, []);

  const onContainerBlur = useCallback((): void => {
    setIsDiagramFocused(false);
  }, []);

  const handleKeyboardShortcuts = useCallback(
    (event: KeyboardEvent): void => {
      const { ctrlKey, key } = event as KeyboardShortcutEvent;
      if (!isDiagramFocused || !ctrlKey) {
        return;
      }

      const keyLower = key.toLowerCase();

      switch (keyLower) {
        case KEYBOARD_SHORTCUTS.ZOOM_IN[0]:
        case KEYBOARD_SHORTCUTS.ZOOM_IN[1]:
          event.preventDefault();
          zoomIn();
          break;
        case KEYBOARD_SHORTCUTS.ZOOM_OUT:
          event.preventDefault();
          zoomOut();
          break;
        case KEYBOARD_SHORTCUTS.ZOOM_FIT:
          event.preventDefault();
          zoomToFit();
          break;
        default:
          break;
      }
    },
    [isDiagramFocused, zoomIn, zoomOut, zoomToFit],
  );

  // Sync flowNodes & flowEdges from form state into local React Flow state
  useEffect(() => {
    if (!flowNodes) {
      return;
    }
    try {
      const parsedNodes: Node[] = JSON.parse(flowNodes);
      const updatedNodes = parsedNodes.map((node) => {
        const type = node.type as ChangeStageType;
        const updatedStatusList = groupedStatus[type];
        setUsedStages((prev) => new Set([...prev, type]));
        return {
          ...node,
          data: {
            ...node.data,
            otherNodeOptions,
            statusList: updatedStatusList,
            notifications,
            updateNode: handleNodeUpdate,
          },
        };
      });
      setNodes(updatedNodes);
      if (flowEdges) {
        const parsedEdges: Edge[] = JSON.parse(flowEdges);
        setEdges(parsedEdges);
      }
    } catch (error) {
      console.error('Failed to parse data:', error);
    }
  }, [flowEdges, flowNodes, groupedStatus, handleNodeUpdate, id, notifications, otherNodeOptions, setEdges, setNodes]);

  useEffect(() => {
    const stageType = ChangeStageTypeEnum.Enum.SUBMISSION_PLANNING;
    const draftNodeId = stageType;

    if (!id && groupedStatus[stageType]) {
      const draftNode: Node = {
        id: draftNodeId,
        deletable: false,
        type: stageType,
        position: DRAFT_NODE_POSITION,
        data: {
          label: `Stage ${CHANGE_STAGE_LABEL[stageType]}`,
          stageType,
          statusList: groupedStatus[stageType],
          updateNode: handleNodeUpdate,
        },
      };

      const connectionEdge: Edge = {
        id: `${FLOW_DEFAULTS.START_NODE_ID}-to-${draftNodeId}`,
        source: FLOW_DEFAULTS.START_NODE_ID,
        target: draftNodeId,
        targetHandle: `0`,
        type: 'default',
        deletable: false,
        markerEnd: {
          type: MarkerType.ArrowClosed,
        },
      };

      setNodes([initialNodes[0], draftNode, initialNodes[1]]);
      setEdges([connectionEdge]);
      setUsedStages(new Set([stageType]));
    }
  }, [groupedStatus, handleNodeUpdate, id, setEdges, setNodes]);

  // Event Listeners Effect
  useEffect(() => {
    document.addEventListener('keydown', handleKeyboardShortcuts);

    return (): void => {
      document.removeEventListener('keydown', handleKeyboardShortcuts);
    };
  }, [handleKeyboardShortcuts]);

  const onConnect = useCallback(
    (params: Connection) => {
      const newEdges = addEdge(params, edges);
      setEdges(newEdges);
      form.setValue('flowEdges', JSON.stringify(newEdges));
    },
    [edges, form, setEdges],
  );

  const onDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();

      const reactFlowBounds = reactFlowWrapper?.current?.getBoundingClientRect();
      const type = event.dataTransfer.getData('application/reactflow');
      let position: XYPosition = {
        x: event.clientX,
        y: event.clientY,
      };
      if (reactFlowBounds) {
        position = {
          x: event.clientX - reactFlowBounds.left,
          y: event.clientY - reactFlowBounds.top,
        };
      }
      if (type === ExtendedChangeStageTypeEnum.Enum.CAB_NODE || type === ExtendedChangeStageTypeEnum.Enum.APPROVAL_NODE) {
        setSelectedIndex(null);
        setShowNodePopup(true);
        setActivePopup(type === ExtendedChangeStageTypeEnum.Enum.CAB_NODE ? 'CAB' : 'APPROVAL');
      } else {
        refetchChangeStatusMinimales();
        const newNode: Node = {
          id: type,
          type: type,
          position,
          data: {
            label: `Stage ${CHANGE_STAGE_LABEL[type as keyof typeof CHANGE_STAGE_LABEL]}`,
            stageType: type,
            statusList: groupedStatus[type as keyof typeof groupedStatus],
            otherNodeOptions,
            updateNode: handleNodeUpdate,
          },
        };
        setNodes((nds) => nds.concat(newNode));
        setUsedStages((prev) => new Set([...prev, type as ExtendedChangeStageType]));
      }
    },
    [groupedStatus, handleNodeUpdate, otherNodeOptions, refetchChangeStatusMinimales, setNodes],
  );

  const onNodesDelete = useCallback(
    (deletedNodes: Node[]) => {
      const currentNotifications = form.getValues('notifications') || [];
      const currentFormNodes = form.getValues('nodes') || [];

      const deletedNodeIds = new Set<string>();
      const deletedNodeTypes = new Set();
      const statusIdsToDelete = new Set<string>();

      deletedNodes.forEach((node: Node) => {
        deletedNodeIds.add(node.id);
        deletedNodeTypes.add(node.type);

        const statusList: ChangeStatusMinimal[] = node.data.statusList ?? [];
        statusList.forEach((status) => {
          if (status.id !== undefined && status.id !== null) {
            statusIdsToDelete.add(status.id.toString());
          }
        });
      });

      const finalNotifications = currentNotifications.filter((noti) => !noti.statusId || !statusIdsToDelete.has(noti.statusId));
      const shouldUpdateFormNodes =
        deletedNodeTypes.has(ExtendedChangeStageTypeEnum.Enum.CAB_NODE) || deletedNodeTypes.has(ExtendedChangeStageTypeEnum.Enum.APPROVAL_NODE);

      const finalFormNodes = shouldUpdateFormNodes ? currentFormNodes.filter((node) => !deletedNodeIds.has(node.nodeId)) : currentFormNodes;

      const finalUsedStages = new Set(usedStages);
      deletedNodeTypes.forEach((type) => {
        if (type !== ExtendedChangeStageTypeEnum.Enum.SUBMISSION_PLANNING) {
          finalUsedStages.delete(type as ExtendedChangeStageType);
        }
      });

      form.setValue('notifications', finalNotifications);
      if (shouldUpdateFormNodes) {
        form.setValue('nodes', finalFormNodes);
      }

      setUsedStages(finalUsedStages);
    },
    [form, usedStages],
  );

  const onNodesChange: OnNodesChange = (changes) => {
    const updated = applyNodeChanges(changes, nodes);
    setNodes(updated);
    form.setValue('flowNodes', JSON.stringify(updated));
  };

  const onEdgesChange: OnEdgesChange = (changes) => {
    const updated = applyEdgeChanges(changes, edges);
    setEdges(updated);
    form.setValue('flowEdges', JSON.stringify(updated));
  };

  const handleNodeDoubleClick = (_: any, node: Node) => {
    const formNodes = form.getValues('nodes') || [];
    const index = formNodes.findIndex((n) => n.nodeId === node.id);
    if (index !== -1) {
      setSelectedIndex(index);
      setShowNodePopup(true);
    }
  };

  const handleConfirmNode = (dataFromPopup: ChangeFlowNodeModel) => {
    if (selectedIndex === null || selectedIndex === -1) {
      const type = activePopup === 'CAB' ? ExtendedChangeStageTypeEnum.Enum.CAB_NODE : ExtendedChangeStageTypeEnum.Enum.APPROVAL_NODE;
      const newId = `${type}-${dataFromPopup.nodeLevel}`;
      const newNode: Node = {
        id: newId,
        type,
        position: { x: 100, y: 100 },
        data: {
          name: dataFromPopup.name,
          type: activePopup,
          label: dataFromPopup.name,
          otherNodeOptions,
          updateNode: handleNodeUpdate,
        },
      };
      setNodes((nds) => [...nds, newNode]);
      const updatedFormNodes = [...(form.getValues('nodes') || []), { ...dataFromPopup, nodeId: newId, type: activePopup ?? 'APPROVAL' }];
      form.setValue('nodes', updatedFormNodes);
      setUsedStages((prev) => new Set([...prev, type]));
    } else {
      const nodeToUpdate = nodes.find((v) => v.id === dataFromPopup.nodeId);
      if (!nodeToUpdate) {
        return;
      }

      const updatedNode: Node = {
        ...nodeToUpdate,
        data: {
          ...nodeToUpdate.data,
          ...dataFromPopup,
          label: dataFromPopup.name,
        },
      };

      const updatedFormNodes = (form.getValues('nodes') || []).map((node) =>
        node.nodeId === dataFromPopup.nodeId ? { ...node, ...dataFromPopup } : node,
      );
      form.setValue('nodes', updatedFormNodes);

      const newNodes = nodes.map((n) => (n.id === dataFromPopup.nodeId ? updatedNode : n));
      setNodes(newNodes);
      form.setValue('flowNodes', JSON.stringify(newNodes));
    }

    setShowNodePopup(false);
  };

  return (
    <ReactFlowProvider>
      <Box
        w='94vw'
        h='75vh'
        display='flex'
        className={fixStyles.flowCustomEdge}
        tabIndex={0}
        onFocus={onContainerFocus}
        onBlur={onContainerBlur}
        style={{ outline: 'none' }}>
        <Box flex='1' ref={reactFlowWrapper}>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onNodesDelete={onNodesDelete}
            onNodeDoubleClick={handleNodeDoubleClick}
            onConnect={onConnect}
            onInit={(instance) => {
              reactFlowInstance.current = instance;
            }}
            deleteKeyCode={['Delete', 'Backspace']}
            onDrop={onDrop}
            onDragOver={onDragOver}
            nodeTypes={nodeTypes}
            fitViewOptions={ZOOM_CONFIG}
            minZoom={0.4}
            maxZoom={2.4}
            connectionLineStyle={{
              strokeWidth: 2,
              strokeDasharray: '0',
            }}
            defaultEdgeOptions={{
              style: {
                strokeWidth: 2,
                strokeDasharray: '0',
              },
              markerEnd: {
                type: MarkerType.ArrowClosed,
                width: 15,
                height: 15,
              },
            }}>
            <Background />
            <Panel position='top-left' style={{ display: 'flex', flexDirection: 'column' }}>
              <KanbanIconButton mb='xs' variant='default' title='Zoom In (Ctrl + +)' onClick={zoomIn}>
                <IconZoomIn size={18} />
              </KanbanIconButton>

              <KanbanIconButton mb='xs' variant='default' title='Zoom Out (Ctrl + -)' onClick={zoomOut}>
                <IconZoomOut size={18} />
              </KanbanIconButton>

              <KanbanIconButton mb='xs' variant='default' title='Zoom To Fit (Ctrl + X)' onClick={zoomToFit}>
                <IconScan size={18} />
              </KanbanIconButton>
            </Panel>
            <Panel position='top-right'>
              <NodeSidebar availableStages={ExtendedChangeStageTypeEnum.options.filter((stage) => !usedStages.has(stage))} />
            </Panel>
          </ReactFlow>
        </Box>

        {showNodePopup && (
          <OtherNodePopup
            type={activePopup || 'APPROVAL'}
            index={selectedIndex ?? -1}
            form={form}
            onConfirm={handleConfirmNode}
            onCancel={() => setShowNodePopup(false)}
          />
        )}
      </Box>
    </ReactFlowProvider>
  );
};

export default ChangeFlowDesigner;
