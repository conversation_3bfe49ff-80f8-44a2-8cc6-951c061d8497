import {
  ChangeRequestDocumentOwnerModel,
  ChangeRequestDocumentApproverForm,
  ChangeRequestDocumentActionStatusType,
} from '@models/ChangeRequestDocumentGroupModel';
import React, { ClipboardEvent } from 'react';
import { Pill } from '@mantine/core';
import classes from '@pages/change/request/document/components/table/CommonTable.module.scss';

const statusBgMap: Record<ChangeRequestDocumentActionStatusType, { bg: string; icon?: React.ReactNode }> = {
  IN_PROGRESS: { bg: 'var(--mantine-color-blue-1)' },
  SENT_TO_OWNER: { bg: 'var(--mantine-color-violet-1)' },
  PENDING_APPROVAL: { bg: 'var(--mantine-color-yellow-1)' },
  APPROVED: { bg: 'var(--mantine-color-green-1)' },
  REJECTED: { bg: 'var(--mantine-color-red-1)' },
};
const statusLabelMap: Record<ChangeRequestDocumentActionStatusType, string> = {
  IN_PROGRESS: 'In Progress',
  SENT_TO_OWNER: 'Sent to Owner',
  PENDING_APPROVAL: 'Pending Approval',
  APPROVED: 'Approved',
  REJECTED: 'Rejected',
};

export const getApproversByLevel = (approvers: ChangeRequestDocumentApproverForm[], documentApproverLevel: number) =>
  approvers?.filter((approver) => approver.documentApproverLevel === documentApproverLevel) ?? [];

export const updateApproversByLevel = (
  approvers: ChangeRequestDocumentApproverForm[],
  documentApproverLevel: number,
  newList: { username: string; displayName: string }[],
) => {
  const others = approvers.filter((a) => a.documentApproverLevel !== documentApproverLevel);
  return [...others, ...newList.map((u) => ({ username: u.username, displayName: u.displayName, documentApproverLevel }))];
};

export const parseClipboardApprovers = (
  e: ClipboardEvent,
  documentApproverLevel: number,
): { username: string; displayName: string; documentApproverLevel: number }[] | null => {
  try {
    const text = e.clipboardData.getData('text');
    const parsed = JSON.parse(text);
    if (Array.isArray(parsed)) {
      return parsed.map((u) => ({
        username: u.username,
        displayName: u.displayName,
        documentApproverLevel,
      }));
    }
  } catch {
    return null;
  }
  return null;
};

export const renderStatusPill = (status?: ChangeRequestDocumentActionStatusType) => {
  if (!status) {
    return null;
  }

  const { bg } = statusBgMap[status] ?? { bg: 'gray' };
  const label = statusLabelMap[status] ?? status;

  return (
    <Pill bg={bg} radius='xl' size='sm'>
      {label}
    </Pill>
  );
};

export const renderUserPill = (user: ChangeRequestDocumentOwnerModel) => {
  const { displayName = '', isActive, username } = user;
  return (
    <Pill className={isActive ? '' : classes.strike} key={username}>
      {displayName}
    </Pill>
  );
};
