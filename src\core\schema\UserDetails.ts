import { z } from 'zod';
import { UserSchema } from './User';
import { UserGroupRoleModel } from './UserGroupRole';
import { UserGroupSchema } from './Group';
import { UserRoleSchema } from './Role';

export const UserDetailSchema = UserSchema.extend({
  roles: z.array(z.object({ UserRoleSchema })).nullish(),
  groupRoles: z.array(z.object({ UserGroupRoleModel })).nullish(),
  groups: z.array(z.object({ UserGroupSchema })).nullish(),
});

export type UserDetail = z.infer<typeof UserDetailSchema>;
