.links{
  flex: 1;
  overflow-y: auto;
}
.link {
    border-left: 1px solid var(--mantine-color-gray-3);
    position: relative;
    &::after{
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      width: 5px;
      height: 1px;
      background-color: var(--mantine-color-gray-3);
    }
  }

.header-link {
  display: block;
  line-height: 1;
  padding: var(--mantine-spacing-xs);
  //border-radius: var(--mantine-radius-sm);
  text-decoration: none;
  color: light-dark(var(--mantine-color-gray-7), var(--mantine-color-dark-0));
  font-size: var(--mantine-font-size-xs);
  font-weight: 500;
  border-bottom: 2px solid transparent;
  @mixin hover {
    background-color: light-dark(var(--mantine-color-gray-0), var(--mantine-color-dark-6));
  }

}
.header-link-active{
  position: relative;
  &::after{
    content: '';
    position: absolute;
    bottom: -7px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--mantine-color-primary-5);
  }
}
.linkLabel {
  margin-right: var(--mantine-spacing-xs);
}
.header-link-icon{
  display: inline-block;
  transform: translateY(5px);
  margin-right: 5px;
}