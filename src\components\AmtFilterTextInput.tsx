import { IconX } from '@tabler/icons-react';
import { KanbanInput, KanbanInputProps, KanbanTooltip } from 'kanban-design-system';
import { ActionIcon } from '@mantine/core';
import React from 'react';

const MAX_LENGTH_SEARCH = 255;

interface FilterTextInputProps extends KanbanInputProps {
  onClear?: () => void;
}

export const FilterTextInput = ({ onClear, ...props }: FilterTextInputProps) => {
  const hasValue = typeof props.value === 'string' && props.value.length > 0;

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.currentTarget.blur();
    }
  };

  return (
    <KanbanTooltip label={props.placeholder} position='top-start'>
      <KanbanInput
        rightSection={
          hasValue &&
          onClear && (
            <ActionIcon
              size='xs'
              onMouseDown={(e) => {
                e.preventDefault();
              }}
              onClick={() => {
                onClear?.();
              }}>
              <IconX size={14} />
            </ActionIcon>
          )
        }
        c='white'
        size='xs'
        mb='0'
        type='text'
        onKeyDown={handleKeyDown}
        {...props}
        maxLength={MAX_LENGTH_SEARCH}
      />
    </KanbanTooltip>
  );
};
