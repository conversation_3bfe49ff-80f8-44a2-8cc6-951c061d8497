import { z } from 'zod';
import { AuditSchema } from './Common';
import { INPUT_REQUIRE } from '@core/message/MesageConstant';

export const UserGroupSchema = z
  .object({
    id: z.number().nullish(),
    userName: z.string(),
    department: z.string().nullish(),
    center: z.string().nullish(),
    groupId: z.number(),
    groupName: z.string().nullish(),
    name: z.string().nullish(),
    userId: z.number().nullish(),
    groupSelected: z.boolean().nullish(),
  })
  .merge(AuditSchema);

//for select user in group
export const UserGroupSchemaSelect = z
  .object({
    id: z.number().nullish(),
    userName: z.string(),
    department: z.string().nullish(),
    center: z.string().nullish(),
    groupId: z.number().nullish(),
    groupName: z.string().nullish(),
    name: z.string().nullish(),
    userId: z.number().nullish(),
    groupSelected: z.boolean().nullish(),
  })
  .merge(AuditSchema);

export const GroupUserSchema = z
  .object({
    id: z.number().nullish(),
    userName: z.string().nullish(),
    groupId: z.number(),
    groupName: z.string(),
    groupSelected: z.boolean().nullish(),
    description: z.string().nullish(),
  })
  .merge(AuditSchema);

export const GroupRoleSchema = z
  .object({
    id: z.number().nullish(),
    roleName: z.string(),
    description: z.string().nullish(),
    groupId: z.number().nullish(),
    groupName: z.string().nullish(),
    roleId: z.number(),
    groupSelected: z.boolean().nullish(),
  })
  .merge(AuditSchema);
export const GroupTypeSchema = z.enum(['NORMAL', 'CAB', 'CHANGE_ROLE']);
export type GroupType = z.infer<typeof GroupTypeSchema>;

export const GroupSchema = z
  .object({
    id: z.number(),
    name: z.string().nonempty(INPUT_REQUIRE),
    type: z.nativeEnum(GroupTypeSchema.Enum).nullish(),
    description: z.string().nullish(),

    userGroups: z.array(UserGroupSchemaSelect).nullish(),
    groupRoles: z.array(GroupRoleSchema).nullish(),
    toInsertGroupRoles: z.array(GroupRoleSchema).nullish(),
    toDeleteGroupRoles: z.array(GroupRoleSchema).nullish(),

    toInsertUserGroups: z.array(UserGroupSchemaSelect).nullish(),
    toDeleteUserGroups: z.array(UserGroupSchemaSelect).nullish(),
  })
  .merge(AuditSchema);

export type Group = z.infer<typeof GroupSchema>;
export type UserGroup = z.infer<typeof UserGroupSchema>;
export type GroupUser = z.infer<typeof GroupUserSchema>;
export type GroupRole = z.infer<typeof GroupRoleSchema>;

export const GROUP_TYPE_OPTIONS: Record<GroupType, string> = {
  NORMAL: 'Normal',
  CAB: 'CAB',
  CHANGE_ROLE: 'Change role',
};
