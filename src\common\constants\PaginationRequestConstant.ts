import { PaginationRequest } from '@api/Type';
import { SortOrder } from 'kanban-design-system';

export const LIMIT_SIZE_LENGTH = 100000;
export const DEFAULT_PAGINATION_REQUEST: PaginationRequest = {
  page: 0,
  size: 10,
  sortBy: 'modifiedDate',
  sortOrder: SortOrder.DESC,
  search: '',
};
export const DEFAULT_PAGINATION_REQUEST_SORT_BY_NAME: PaginationRequest = {
  ...DEFAULT_PAGINATION_REQUEST,
  sortBy: 'name',
  sortOrder: SortOrder.ASC,
};
