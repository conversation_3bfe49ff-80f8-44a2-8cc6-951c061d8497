import React from 'react';
import { Paper, Box, ThemeIcon } from '@mantine/core';
import { KanbanText } from 'kanban-design-system';
import { IconNote } from '@tabler/icons-react';
import { ChangeNoteModel } from '@models/ChangeNoteModel';
import { renderDateTime } from 'kanban-design-system';
import customStyled from './ChangeNote.module.scss';

interface NoteCardProps {
  note: ChangeNoteModel;
}

const NoteCard: React.FC<NoteCardProps> = ({ note }) => {
  return (
    <Paper className={customStyled.noteStyle} withBorder display='flex' shadow='xs' bg='white' maw={800} radius='sm'>
      <Box className={customStyled.headerStyle} display='flex' bg='primary.3' c='dark.6' fw='bold' fz='sm' px='sm' py='xs'>
        <ThemeIcon color='primary.3'>
          <IconNote color='white' size={16} fill='var(--mantine-color-yellow-5)' />
        </ThemeIcon>
        <KanbanText size='sm' c='white'>
          {note.createdBy}
        </KanbanText>
        <KanbanText fw='normal' size='xs' c='white' ml={8}>
          said on {renderDateTime(note.modifiedDate)}
        </KanbanText>
      </Box>
      {note.content && (
        <Box p='sm' fz='sm'>
          <KanbanText size='sm' style={{ whiteSpace: 'pre-wrap' }}>
            {note.content}
          </KanbanText>
        </Box>
      )}
    </Paper>
  );
};

export default NoteCard;
