import {
  AdvancedFilterMappingType,
  ColumnType,
  getDefaultTableAffected,
  KanbanIconButton,
  KanbanText,
  KanbanTitle,
  KanbanTooltip,
  renderDateTime,
  SortOrder,
} from 'kanban-design-system';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanButton } from 'kanban-design-system';
import { KanbanTable, KanbanTableProps, TableAffactedSafeType } from 'kanban-design-system';
import { Box, Flex } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IconEdit, IconPlus, IconSitemap, IconTrash } from '@tabler/icons-react';
import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { EntityAction } from '@common/constants/EntityActionConstants';
import useFetch from '@core/hooks/useFetch';
import customStyled from '@resources/styles/Common.module.scss';

import useMutate from '@core/hooks/useMutate';
import equal from 'fast-deep-equal';
import { LocalStorageKey } from '@common/constants/LocalStorageKeyConstants';
import { buildDetailUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import { FilterTextInput } from '@components/AmtFilterTextInput';
import { deleteConfirm } from '@components/utils/ConfirmMessageUtils';
import useSavedFilterCycle from '@core/hooks/useSavedFilterCycle';
import AmtModal from '@components/AmtModal';
import { useSavedColumns } from '@core/hooks/useSavedColumns';
import { AmtShortTextContent, AmtShortTextLink } from '@components/AmtShortTextContent';
import { ChangeTemplateApi } from '@api/ChangeTemplateApi';
import { ChangeTemplate } from '@core/schema/ChangeTemplate';
import { useCheckPermissons } from '@core/hooks/useCheckPermissons';
import { ACL_PERMISSIONS } from '@common/constants/AclPermissionConstants';

export type ChangeTemplatesPageFilter = {
  name?: string;
  description?: string;
  status?: string;
};

const initOrUpdatedFilterPayloads = (prev?: TableAffactedSafeType<ChangeTemplate>, initFilters?: ChangeTemplatesPageFilter) => {
  const prevFilter = prev || getDefaultTableAffected();
  if (initFilters) {
    const filterObj: AdvancedFilterMappingType<ChangeTemplate> = {
      ['name']: {
        filterOption: 'contains',
        value: {
          fromValue: initFilters.name,
        },
      },
    };
    const advancedFilterMapping = { ...prevFilter.advancedFilterMapping, ...filterObj };
    return {
      ...prevFilter,
      advancedFilterMapping: { ...advancedFilterMapping },
      sortedBy: 'modifiedDate',
      sortOrder: SortOrder.DESC,
    } as TableAffactedSafeType<ChangeTemplate>;
  }
  return prevFilter;
};

export const ChangeTemplatesPage = () => {
  const [totalRecords, setTotalRecords] = useState(0);

  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);

  const [changeTemplates, setChangeTemplates] = useState<ChangeTemplate[]>([]);

  const [curentToDeleteMultiChangeTemplates, setCurentToDeleteMultiChangeTemplates] = useState<ChangeTemplate[]>([]);
  const [currentToDeleteChangeTemplate, setCurrentToDeleteChangeTemplate] = useState<ChangeTemplate>();

  const navigate = useNavigate();

  const [inputFilters, setInputFilters] = useState<ChangeTemplatesPageFilter>({} as ChangeTemplatesPageFilter);

  const [savedFilters, setSavedFilters] = useSavedFilterCycle({
    key: LocalStorageKey.SEARCH_VALUE_CHANGE_TEMPLATES_PAGE,
    setInputFilters,
  });

  const [tableAffectedChange, setTableAffectedChange] = useState<TableAffactedSafeType>();

  const deleteMessageConfig = deleteConfirm(
    currentToDeleteChangeTemplate ? [currentToDeleteChangeTemplate.name] : curentToDeleteMultiChangeTemplates.map((it) => it.name),
  );
  const [orderColumn, setOrderColumn] = useState<string[]>();

  const { data: changeTemplatesResponse, refetch: refetchList } = useFetch(
    ChangeTemplateApi.findAll(initOrUpdatedFilterPayloads(tableAffectedChange, savedFilters)),
    {
      enabled: !!tableAffectedChange,
    },
  );

  const handleSearch = useCallback(() => {
    setSavedFilters((prev) => ({ ...prev, ...inputFilters }));
    setTableAffectedChange((prev) => {
      return initOrUpdatedFilterPayloads(prev, inputFilters);
    });
  }, [inputFilters, setSavedFilters]);

  useEffect(() => {
    if (changeTemplatesResponse?.data?.content) {
      setChangeTemplates(changeTemplatesResponse.data.content);
      setTotalRecords(changeTemplatesResponse.data.totalElements);
    }
  }, [changeTemplatesResponse?.data]);

  const { mutate: deleteMultiMutate } = useMutate(ChangeTemplateApi.deleteByIds, {
    successNotification: `Change Template(s) deleted successfully`,
    onSuccess: () => {
      refetchList();
    },
  });

  const handleDeleteMulti = useCallback(
    (datas: ChangeTemplate[]) => {
      deleteMultiMutate(datas.map((it) => it.id));
      setCurentToDeleteMultiChangeTemplates([]);
      setCurrentToDeleteChangeTemplate(undefined);
      closeModal();
    },
    [closeModal, deleteMultiMutate],
  );

  const columns = [
    {
      name: 'name',
      title: 'Template Name',
      width: '30%',
      customRender: (_, rowData) => {
        return (
          <AmtShortTextLink
            routePath={ROUTE_PATH.CONFIGURATION_CHANGE_CHANGE_TEMPLATE_DETAIL}
            entityId={rowData.id}
            data={rowData.name}
            disableShorten
            fw={500}
          />
        );
      },
    },
    {
      name: 'description',
      title: 'Description',
      width: '45%',
      customRender: (data) => <AmtShortTextContent data={data} />,
    },
    {
      name: 'isActive',
      title: 'Status',
      customRender: (data) => (data ? <KanbanText c='green'>Active</KanbanText> : <KanbanText c='red'>Inactive</KanbanText>),
    },
    {
      name: 'createdBy',
      title: 'Created By',
      hidden: true,
    },
    {
      name: 'createdDate',
      title: 'Created Date',
      customRender: renderDateTime,
      hidden: true,
      width: '10%',
    },
    {
      name: 'modifiedBy',
      title: 'Modified By',
      hidden: true,
    },
    {
      name: 'modifiedDate',
      title: 'Modified Date',
      width: '10%',
      customRender: renderDateTime,
    },
  ] as ColumnType<ChangeTemplate>[];

  const savedColumnDisplay = useSavedColumns<ChangeTemplate>(LocalStorageKey.COLUMN_DISPLAY_CHANGE_TEMPLATES_PAGE, columns, orderColumn);
  const superAdmin = useCheckPermissons([ACL_PERMISSIONS.ADMIN_MANAGE_SYSTEM]);
  const tableProps: KanbanTableProps<ChangeTemplate> = useMemo(() => {
    const tblProps: KanbanTableProps<ChangeTemplate> = {
      title: '',
      columns: savedColumnDisplay,
      data: changeTemplates,
      customRowProps: (data) => ({
        onDoubleClick: () => {
          navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_CHANGE_CHANGE_TEMPLATE_DETAIL, data.id, EntityAction.EDIT));
        },
      }),
      serverside: {
        totalRows: totalRecords,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffectedChange, dataSet)) {
            setTableAffectedChange((prev) => (prev ? { ...dataSet, advancedFilterMapping: prev.advancedFilterMapping } : { ...dataSet }));
          }
        },
      },
      pagination: {
        enable: true,
      },
      selectableRows: {
        enable: true,
        customAction: (_, __) => (
          <KanbanButton
            leftSection={<IconTrash />}
            size={'xs'}
            bg='red'
            onClick={() => {
              openModal();
            }}>
            Delete(s)
          </KanbanButton>
        ),
        crossPageSelected: {
          rowKey: 'id',
          setSelectedRows: setCurentToDeleteMultiChangeTemplates,
          selectedRows: curentToDeleteMultiChangeTemplates,
        },
      },
      actions: {
        customAction: (data) => {
          return (
            <Flex justify={'start'} direction={'row'} w={'100%'}>
              <KanbanTooltip label='Edit'>
                <KanbanIconButton
                  mr={'xs'}
                  variant='transparent'
                  size={'sm'}
                  onClick={() => {
                    navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_CHANGE_CHANGE_TEMPLATE_DETAIL, data.id, EntityAction.EDIT));
                  }}>
                  <IconEdit />
                </KanbanIconButton>
              </KanbanTooltip>
              <KanbanTooltip label='Delete'>
                <KanbanIconButton
                  mr={'xs'}
                  variant='transparent'
                  size={'sm'}
                  c={'red'}
                  onClick={() => {
                    setCurrentToDeleteChangeTemplate(data);
                    openModal();
                  }}>
                  <IconTrash />
                </KanbanIconButton>
              </KanbanTooltip>
              {superAdmin && !!data.changeFlowId && (
                <KanbanTooltip label='Changeflow'>
                  <KanbanIconButton
                    mr={'xs'}
                    variant='transparent'
                    size={'sm'}
                    onClick={() => navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_CHANGE_FLOW_DETAIL, data.changeFlowId || 0, EntityAction.EDIT))}>
                    <IconSitemap />
                  </KanbanIconButton>
                </KanbanTooltip>
              )}
            </Flex>
          );
        },
      },
      columnOrderable: {
        /** Default is `true` */
        onOrder: (data) => {
          setOrderColumn(data.map((it) => it.name));
        },
      },
      showNumericalOrderColumn: true,
    };
    return tblProps;
  }, [changeTemplates, curentToDeleteMultiChangeTemplates, navigate, openModal, savedColumnDisplay, superAdmin, tableAffectedChange, totalRecords]);
  const handleClearFilter = useCallback(
    (key: keyof ChangeTemplatesPageFilter) => {
      const toUpdate = { [key]: '' };
      setInputFilters((prev) => ({ ...prev, ...toUpdate }));
      setSavedFilters((prev) => ({ ...prev, ...toUpdate }));
    },
    [setSavedFilters],
  );
  return (
    <Box className={customStyled.tableCs}>
      <HeaderTitleComponent
        title=''
        rightSection={
          <Flex>
            <KanbanButton
              leftSection={<IconPlus />}
              size={'xs'}
              onClick={() => {
                navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_CHANGE_CHANGE_TEMPLATE_DETAIL, 0, EntityAction.CREATE));
              }}>
              Add Change Template
            </KanbanButton>
          </Flex>
        }
        leftSection={<KanbanTitle fz={'h4'}>Change Templates</KanbanTitle>}
      />

      <Flex dir='row' gap='xs' mb='xs'>
        <FilterTextInput
          placeholder='Template Name'
          value={inputFilters.name ?? ''}
          onChange={(val) => {
            setInputFilters((prev) => ({ ...prev, name: val.target.value }));
          }}
          onBlur={() => {
            handleSearch();
          }}
          onClear={() => handleClearFilter('name')}
        />
      </Flex>

      <Box className={customStyled.elementBorder}>
        <KanbanTable {...tableProps} title='' />
      </Box>

      <AmtModal
        opened={openedModal}
        title={deleteMessageConfig.title}
        onClose={() => {
          setCurrentToDeleteChangeTemplate(undefined);
          closeModal();
        }}
        actions={
          <KanbanButton
            variant='filled'
            onClick={() =>
              currentToDeleteChangeTemplate
                ? handleDeleteMulti([currentToDeleteChangeTemplate])
                : handleDeleteMulti(curentToDeleteMultiChangeTemplates)
            }>
            Confirm
          </KanbanButton>
        }>
        {deleteMessageConfig.children}
      </AmtModal>
    </Box>
  );
};

ChangeTemplatesPage.whyDidYouRender = true;
export default ChangeTemplatesPage;
