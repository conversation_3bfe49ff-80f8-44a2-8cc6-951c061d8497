import {
  AdvancedFilterMappingType,
  ColumnType,
  getDefaultTableAffected,
  KanbanConfirmModal,
  KanbanIconButton,
  KanbanTitle,
  KanbanTooltip,
  renderDateTime,
  SortOrder,
} from 'kanban-design-system';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanButton } from 'kanban-design-system';
import { KanbanTable, KanbanTableProps, TableAffactedSafeType } from 'kanban-design-system';
import { Box, Flex } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IconEdit, IconPlus, IconTrash } from '@tabler/icons-react';
import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { RoleDetailModel } from '@models/RoleModel';
import { RoleApi } from '@api/RoleApi';
import { useNavigate } from 'react-router-dom';
import { EntityAction } from '@common/constants/EntityActionConstants';
import useFetch from '@core/hooks/useFetch';
import customStyled from '@resources/styles/Common.module.scss';

import useMutate from '@core/hooks/useMutate';
import equal from 'fast-deep-equal';
import { LocalStorageKey } from '@common/constants/LocalStorageKeyConstants';
import { DateRangeFilter, dateRangeKeys } from '@components/AmtDateRangeInput';
import { dateToIsoString, parseFilterWithDate } from '@common/utils/DateUtils';
import { buildDetailUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import { FilterTextInput } from '@components/AmtFilterTextInput';
import { deleteConfirm } from '@components/utils/ConfirmMessageUtils';
import useSavedFilterCycle from '@core/hooks/useSavedFilterCycle';
import { AmtShortTextContent, AmtShortTextLink } from '@components/AmtShortTextContent';
import { useSavedColumns } from '@core/hooks/useSavedColumns';

export type RolesPageFilter = DateRangeFilter & {
  roleName?: string;
};

const initOrUpdatedFilterPayloads = (prev?: TableAffactedSafeType<RoleDetailModel>, initFilters?: RolesPageFilter) => {
  const prevFilter = prev || getDefaultTableAffected();
  if (initFilters) {
    const filterObj: AdvancedFilterMappingType<RoleDetailModel> = {
      ['name']: {
        filterOption: 'contains',
        value: {
          fromValue: initFilters.roleName,
        },
      },
      ['modifiedDate']: {
        filterOption:
          initFilters.startDate && initFilters.endDate ? 'betweenInclusive' : initFilters.startDate ? 'greaterThanOrEqualTo' : 'lessThanOrEqualTo',
        value: {
          fromValue: initFilters.startDate ? dateToIsoString(initFilters.startDate) : '',
          toValue: initFilters.endDate ? dateToIsoString(initFilters.endDate) : '',
        },
      },
    };
    const advancedFilterMapping = {
      ...prevFilter.advancedFilterMapping,
      ...filterObj,
    };
    return {
      ...prevFilter,
      advancedFilterMapping: { ...advancedFilterMapping },
      sortedBy: 'modifiedDate',
      sortOrder: SortOrder.DESC,
    } as TableAffactedSafeType<RoleDetailModel>;
  }
  return prevFilter;
};

export const RolesPage = () => {
  const [totalRecords, setTotalRecords] = useState(0);

  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);

  const [roles, setRoles] = useState<RoleDetailModel[]>([]);

  const [curentToDeleteMultiRoles, setCurentToDeleteMultiRoles] = useState<RoleDetailModel[]>([]);
  const [currentToDeleteRole, setCurrentToDeleteRole] = useState<RoleDetailModel>();

  const navigate = useNavigate();
  const [inputFilters, setInputFilters] = useState<RolesPageFilter>({} as RolesPageFilter);
  const [savedFilters, setSavedFilters] = useSavedFilterCycle({
    key: LocalStorageKey.SEARCH_VALUE_ROLES_PAGE,
    deserialize: (str) => parseFilterWithDate<RolesPageFilter>(str, dateRangeKeys),
    setInputFilters,
  });

  const [tableAffectedChange, setTableAffectedChange] = useState<TableAffactedSafeType>();

  const deleteMessageConfig = deleteConfirm(currentToDeleteRole ? [currentToDeleteRole.name] : curentToDeleteMultiRoles.map((it) => it.name));

  const { data: rolesResponse, refetch: refetchList } = useFetch(
    RoleApi.findAllRoles(initOrUpdatedFilterPayloads(tableAffectedChange, savedFilters)),
    {
      enabled: !!tableAffectedChange,
    },
  );

  const [orderColumn, setOrderColumn] = useState<string[]>();

  /**
   * Search by update input of API :tableAffectedChange
   * and handle update search history savedFilters
   */
  const handleSearch = useCallback(() => {
    setSavedFilters((prev) => ({ ...prev, ...inputFilters }));
    setTableAffectedChange((prev) => {
      return initOrUpdatedFilterPayloads(prev, inputFilters);
    });
  }, [inputFilters, setSavedFilters]);

  useEffect(() => {
    if (rolesResponse?.data?.content) {
      setRoles(rolesResponse.data.content);
      setTotalRecords(rolesResponse.data.totalElements);
    }
  }, [rolesResponse?.data]);

  const { mutate: deleteMultiMutate } = useMutate(RoleApi.deleteRoleByIds, {
    successNotification: 'Delete role(s) successfully',
    onSuccess: () => {
      refetchList();
    },
  });

  const handleDeleteMulti = useCallback(
    (datas: RoleDetailModel[]) => {
      deleteMultiMutate(datas.map((it) => it.id));
      setCurentToDeleteMultiRoles([]);
      setCurrentToDeleteRole(undefined);
      closeModal();
    },
    [closeModal, deleteMultiMutate],
  );

  const columns = useMemo(() => {
    return [
      {
        name: 'name',
        title: 'Role Name',
        width: '30%',
        customRender: (data, rowData) => {
          return (
            <AmtShortTextLink
              routePath={ROUTE_PATH.CONFIGURATION_USER_ROLE_DETAIL}
              entityId={rowData.id}
              data={rowData.name}
              disableShorten
              fw={500}
            />
          );
        },
      },
      {
        name: 'description',
        title: 'Description',
        width: '30%',

        customRender: (data) => <AmtShortTextContent data={data} />,
      },
      {
        name: 'modifiedBy',
        title: 'Modified By',
        hidden: true,
      },
      {
        name: 'modifiedDate',
        title: 'Modified Date',
        customRender: renderDateTime,
      },
      {
        name: 'createdBy',
        title: 'Created By',
      },
      {
        name: 'createdDate',
        title: 'Created Date',
        hidden: true,
        customRender: renderDateTime,
      },
    ] as ColumnType<RoleDetailModel>[];
  }, []);
  const savedColumnDisplay = useSavedColumns<RoleDetailModel>(LocalStorageKey.COLUMN_DISPLAY_ROLES_PAGE, columns, orderColumn);

  const tableProps: KanbanTableProps<RoleDetailModel> = useMemo(() => {
    const tblProps: KanbanTableProps<RoleDetailModel> = {
      title: '',
      columns: savedColumnDisplay,
      data: roles,
      customRowProps: (data) => ({
        onDoubleClick: () => {
          navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_USER_ROLE_DETAIL, data.id, EntityAction.EDIT));
        },
      }),
      columnOrderable: {
        /** Default is `true` */
        onOrder: (data) => {
          setOrderColumn(data.map((it) => it.name));
        },
      },
      serverside: {
        totalRows: totalRecords,

        onTableAffected: (dataSet) => {
          if (!equal(tableAffectedChange, dataSet)) {
            setTableAffectedChange((prev) => (prev ? { ...dataSet, advancedFilterMapping: prev.advancedFilterMapping } : { ...dataSet }));
          }
        },
      },
      pagination: {
        enable: true,
      },

      selectableRows: {
        enable: true,
        customAction: (_, __) => (
          <KanbanButton
            leftSection={<IconTrash />}
            size={'xs'}
            bg='red'
            onClick={() => {
              openModal();
            }}>
            Delete(s)
          </KanbanButton>
        ),
        crossPageSelected: {
          rowKey: 'id',
          setSelectedRows: setCurentToDeleteMultiRoles,
          selectedRows: curentToDeleteMultiRoles,
        },
      },
      actions: {
        customAction: (data) => {
          return (
            <>
              <KanbanTooltip label='Edit'>
                <KanbanIconButton
                  mr={'xs'}
                  variant='transparent'
                  size={'sm'}
                  onClick={() => {
                    navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_USER_ROLE_DETAIL, data.id, EntityAction.EDIT));
                  }}>
                  <IconEdit />
                </KanbanIconButton>
              </KanbanTooltip>
              <KanbanTooltip label='Delete'>
                <KanbanIconButton
                  mr={'xs'}
                  variant='transparent'
                  size={'sm'}
                  c={'red'}
                  onClick={() => {
                    setCurrentToDeleteRole(data);
                    openModal();
                  }}>
                  <IconTrash />
                </KanbanIconButton>
              </KanbanTooltip>
            </>
          );
        },
      },
      showNumericalOrderColumn: true,
    };
    return tblProps;
  }, [savedColumnDisplay, roles, totalRecords, curentToDeleteMultiRoles, navigate, tableAffectedChange, openModal]);

  const handleClearFilter = useCallback(
    (key: keyof RolesPageFilter) => {
      const toUpdate = { [key]: '' };
      setInputFilters((prev) => ({ ...prev, ...toUpdate }));
      setSavedFilters((prev) => ({ ...prev, ...toUpdate }));
    },
    [setSavedFilters],
  );

  return (
    <Box className={customStyled.tableCs}>
      <HeaderTitleComponent
        title=''
        rightSection={
          <Flex>
            <KanbanButton
              leftSection={<IconPlus />}
              size={'xs'}
              onClick={() => {
                navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_USER_ROLE_DETAIL, 0, EntityAction.CREATE));
              }}>
              Add Role
            </KanbanButton>
          </Flex>
        }
        leftSection={<KanbanTitle fz={'h4'}>Roles</KanbanTitle>}
      />
      <Flex dir='row' gap='xs' mb='xs'>
        <FilterTextInput
          placeholder='Role Name'
          value={inputFilters.roleName ?? ''}
          onChange={(val) => {
            setInputFilters((prev) => ({ ...prev, roleName: val.target.value }));
          }}
          onBlur={() => {
            handleSearch();
          }}
          onClear={() => handleClearFilter('roleName')}
        />
      </Flex>
      <Box className={customStyled.elementBorder}>
        <KanbanTable {...tableProps} title='' />
      </Box>
      <KanbanConfirmModal
        opened={openedModal}
        title={deleteMessageConfig.title}
        onClose={() => {
          setCurrentToDeleteRole(undefined);
          closeModal();
        }}
        onConfirm={() => (currentToDeleteRole ? handleDeleteMulti([currentToDeleteRole]) : handleDeleteMulti(curentToDeleteMultiRoles))}>
        {deleteMessageConfig.children}
      </KanbanConfirmModal>
    </Box>
  );
};

RolesPage.whyDidYouRender = true;
export default RolesPage;
