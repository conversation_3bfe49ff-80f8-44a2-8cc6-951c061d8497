import { ROUTE_PATH as RP } from '@common/utils/RouterUtils';

// Map segments → display labels
export const LABEL_MAP: Record<string, string> = {
  users: 'User',
  group: 'Group',
  roles: 'Role',
  'custom-field': 'Custom Field',
  status: 'Change Status',
  document: 'Change Document',
  templates: 'Change Template',
  'workflow-nodes': 'Workflow Node',
  'email-templates': 'Email Template',
  'change-request': 'Change',
  // Add more if new path segments are introduced
};

// Map prefix URL → list ignored segments
export const IGNORED_SEGMENTS_MAP: Record<string, string[]> = {
  [RP.CONFIGURATION_USER_USERS]: ['configuration', 'system'],
  [RP.CONFIGURATION_USER_ROLES]: ['configuration', 'system'],
  [RP.CONFIGURATION_USER_GROUPS]: ['configuration', 'system'],
  [RP.CONFIGURATION_CHANGE_CUSTOM_FIELD]: ['configuration', 'change'],
  [RP.CONFIGURATION_CHANGE_STATUS]: ['configuration', 'change'],
  [RP.CONFIGURATION_CHANGE_DOCUMENT]: ['configuration', 'change'],
  [RP.CONFIGURATION_CHANGE_CHANGE_TEMPLATE]: ['configuration', 'change'],
  [RP.CONFIGURATION_CHANGE_WORKFLOW_NODE]: ['configuration', 'change'],
  [RP.CONFIGURATION_NOTIFICATIONS_EMAIL_TEMPLATES]: ['configuration', 'notifications'],
  [RP.CONFIGURATION_CHANGE_FLOW]: ['configuration', 'change'],

  // Add more if new path segments are introduced
};
