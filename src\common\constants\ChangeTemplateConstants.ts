export const SOURCE_DROPPABLE_ID = 'droppable-source';

export const DROP_ZONE_DEFAULT_COLOR = 'var(--mantine-color-gray-1)';
export const DROP_ZONE_HIGHLIGHT_COLOR = 'var(--mantine-color-green-0)';

export enum CHANGE_TABS {
  DETAIL = 'DETAIL',
  ROLE = 'ROLE',
}

export const DRAG_TAG = 'drag-handle';
export const DEFAULT_START_BREAK_FIELD_ID = 0;
export const GRID_COLS = 2;
export const ROW_HEIGHT = 70;
export const FIELD_MAX_HEIGHT = 10;
export const BREAK_FIELD_MIN_HEIGHT = 2;
export const DROP_ITEM_PREFIX = 'dropping-preview-';
export const CHANGE_NOTICE_MAX_LENGTH = 4000;
