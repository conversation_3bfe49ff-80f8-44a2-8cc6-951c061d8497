import { GroupRoleSchema, GroupSchema, GroupUserSchema, UserGroupSchemaSelect } from '@core/schema/Group';
import { z } from 'zod';
import { PaginationRequestModel } from './EntityModelBase';

export type GroupModel = z.infer<typeof GroupSchema>;
export type GroupRoleModel = z.infer<typeof GroupRoleSchema>;
export type UserGroupModel = z.infer<typeof UserGroupSchemaSelect>;
export type GroupUserModel = z.infer<typeof GroupUserSchema>;

export type UserGroupDisplayFilterModel<T> = {
  paging: PaginationRequestModel<T>;
  userName?: string[];
};
