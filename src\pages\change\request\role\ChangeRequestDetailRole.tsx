import React, { forwardRef, useCallback, useImperativeHandle, useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { FormProvider, useFieldArray, useForm } from 'react-hook-form';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';

import { Flex, Stack } from '@mantine/core';
import { ChangeRequestRoleList, ChangeRequestApprovalRoleListSchema } from '@core/schema/ChangeRequestRole';
import { ChangeRquestTabsEnum } from '@common/constants/ChangeRequestConstants';
import useFetch from '@core/hooks/useFetch';
import { ChangeRequestApi } from '@api/ChangeRequestApi';
import { KanbanButton, KanbanIconButton, KanbanText, KanbanTitle } from 'kanban-design-system';
import useMutate from '@core/hooks/useMutate';
import { buildDetailUrl, buildUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import { buildNavigateState } from '@core/hooks/useSavedFilterCycle';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import UnsaveConfirmModal from '@components/utils/ConfirmMessageUtils';
import { useDisclosure } from '@mantine/hooks';
import { IconArrowLeft } from '@tabler/icons-react';
import { ChangeFlowNodeTypeEnum } from '@models/ChangeFlowModel';
import { EntityAction } from '@common/constants/EntityActionConstants';
import { useCheckPermissons } from '@core/hooks/useCheckPermissons';
import { ACL_PERMISSIONS } from '@common/constants/AclPermissionConstants';
import { ChangeRequestApprovalMode } from '@common/constants/ChangeRequestApprovalConstants';
import NodeCabWorkflowTabs from './NodeCabWorkflowTabs';
import { ChangeRequestModel } from '@core/schema/ChangeRequest';
import CurrentUserCabTasks from './CurrentUserCabTasks';
import { ChangeStageType, ChangeStageTypeEnum } from '@common/constants/ChageStageType';
import NodeApproval from './NodeApproval';

export type MappedUser = {
  username?: string | null;
  displayUsername?: string | null;
  usernameActive?: boolean | null;
};

interface ChangeRequestRoleDetailProps {
  changeTemplateId: number;
  activeTab: ChangeRquestTabsEnum;
  changeRequest?: ChangeRequestModel;
  reloadKey?: number;
  currentStage?: ChangeStageType;
}

export interface ChangeRequestDetailRoleRef {
  reload: () => void;
}

const DEFAULT_CHANGE_ROLES: ChangeRequestRoleList = {
  roles: [],
};

const ChangeRequestDetailRole = forwardRef<ChangeRequestDetailRoleRef, ChangeRequestRoleDetailProps>(function ChangeRequestDetailRoleComponent(
  { activeTab, currentStage },
  ref,
) {
  const [searchParams] = useSearchParams();
  const actionParam = (searchParams.get('action') as EntityAction) || EntityAction.VIEW;

  const [mode, setMode] = useState<EntityAction>(actionParam);

  const changeRequestId = Number(useParams().id);
  const detailQueryConfig = ChangeRequestApi.findAllChangeRequestApprovalRoles(changeRequestId);
  const { data: changeRequestRolesResponse, refetch: refetchChangeRequestRoles } = useFetch(detailQueryConfig, {
    enabled: !!changeRequestId && activeTab === ChangeRquestTabsEnum.CHANGE_ROLE,
  });

  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);
  const changeRolesData = changeRequestRolesResponse?.data || DEFAULT_CHANGE_ROLES;
  const methods = useForm<ChangeRequestRoleList>({
    resolver: zodResolver(ChangeRequestApprovalRoleListSchema),
    mode: 'onBlur',
    defaultValues: {
      roles: changeRolesData.roles,
    },
    values: {
      roles: changeRolesData.roles,
    },
  });
  const { control, getValues } = methods;
  const { fields: roles } = useFieldArray({ control, name: 'roles' });

  useImperativeHandle(
    ref,
    () => ({
      reload: () => {
        refetchChangeRequestRoles();
      },
    }),
    [refetchChangeRequestRoles],
  );

  const navigate = useNavigate();
  const handleNavigateToList = useCallback(() => {
    navigate(buildUrl(ROUTE_PATH.CHANGE_REQUEST), {
      state: buildNavigateState({ fromDetail: true }),
    });
  }, [navigate]);

  const { mutate: saveMutate } = useMutate(ChangeRequestApi.saveListChangeRoles, {
    successNotification: `Approval Levels saved successfully`,
    errorNotification: (data) => ({ message: data.message }),
    onSuccess: () => {
      setMode(EntityAction.VIEW);
      navigate(buildDetailUrl(ROUTE_PATH.CHANGE_REQUEST_DETAIL, changeRequestId, EntityAction.VIEW));
      refetchChangeRequestRoles();
    },
  });

  const handleSave = useCallback(() => {
    saveMutate({ changeRequestId, data: getValues('roles') });
  }, [changeRequestId, getValues, saveMutate]);

  const handleExistWithoutSave = useCallback(() => {
    openModal();
  }, [openModal]);

  const handleEdit = useCallback(() => {
    setMode(EntityAction.EDIT);
    navigate(buildDetailUrl(ROUTE_PATH.CHANGE_REQUEST_DETAIL, changeRequestId, EntityAction.EDIT));
  }, [changeRequestId, navigate]);
  const canEditChange = useCheckPermissons([ACL_PERMISSIONS.CHANGE_UPDATE]);
  const canSaveChange = useCheckPermissons(changeRequestId ? [ACL_PERMISSIONS.CHANGE_UPDATE] : [ACL_PERMISSIONS.CHANGE_ADD]);

  const modeApproval =
    currentStage === ChangeStageTypeEnum.Enum.SUBMISSION_PLANNING ? ChangeRequestApprovalMode.SUBMISSION : ChangeRequestApprovalMode.APPROVAL;
  return (
    <FormProvider {...methods}>
      <UnsaveConfirmModal opened={openedModal} onClose={closeModal} onConfirm={handleNavigateToList} />

      <Flex direction='column' justify='flex-end'>
        <HeaderTitleComponent
          title={''}
          rightSection={
            roles &&
            roles.length > 0 && (
              <Flex direction={'row'} align={'center'} gap={'xs'}>
                <KanbanButton size='xs' variant='subtle' onClick={handleNavigateToList}>
                  Cancel
                </KanbanButton>
                {EntityAction.VIEW === mode && canEditChange ? (
                  <KanbanButton size='xs' variant='filled' onClick={handleEdit}>
                    Edit
                  </KanbanButton>
                ) : (
                  canSaveChange && (
                    <KanbanButton size='xs' variant='filled' onClick={handleSave}>
                      Save
                    </KanbanButton>
                  )
                )}
              </Flex>
            )
          }
          leftSection={
            <Flex direction={'row'} align={'center'} gap={'xs'}>
              <KanbanIconButton size='sm' variant='subtle' onClick={handleExistWithoutSave}>
                <IconArrowLeft />
              </KanbanIconButton>
              <KanbanTitle fz={'h4'}>Approval Levels</KanbanTitle>
            </Flex>
          }
        />

        <Stack w='100%'>
          <CurrentUserCabTasks mode={mode} modeApproval={modeApproval} refetchChangeRequestRoles={refetchChangeRequestRoles} />

          {roles && roles.length > 0 ? (
            roles.map((role, index) => (
              <React.Fragment key={role.id}>
                {ChangeFlowNodeTypeEnum.Enum.CAB === role.changeFlowNode?.type ? (
                  <NodeCabWorkflowTabs
                    roleIdx={index}
                    mode={mode}
                    modeApproval={modeApproval}
                    role={role}
                    refetchChangeRequestRoles={refetchChangeRequestRoles}
                  />
                ) : (
                  <NodeApproval
                    control={control}
                    index={index}
                    mode={mode}
                    role={role}
                    modeApproval={modeApproval}
                    refetchChangeRequestRoles={refetchChangeRequestRoles}
                    stage={currentStage}
                    key={role.id}
                  />
                )}
              </React.Fragment>
            ))
          ) : (
            <KanbanText>None Change Flow in Change Template, none Approval/CAB node(s) in Change Flow</KanbanText>
          )}
        </Stack>
      </Flex>
    </FormProvider>
  );
});

export default ChangeRequestDetailRole;
