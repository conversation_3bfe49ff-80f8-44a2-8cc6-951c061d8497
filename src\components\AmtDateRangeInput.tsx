import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat'; // ES 2015
import React, { SetStateAction, useCallback, useEffect, useState } from 'react';
import styled from '../resources/styles/Common.module.scss';
import { IconCalendar, IconX } from '@tabler/icons-react';
import { IMask, IMaskInput } from 'react-imask';
import { ActionIcon, Box, Flex, Input, Popover, Text } from '@mantine/core';
import { Calendar, DayProps } from '@mantine/dates';
import { useForm } from '@mantine/form';
import {
  DATE_FORMAT_SIMPLE,
  dateToString,
  FILTER_DAY_END,
  FILTER_DAY_START,
  FILTER_MONTH_END,
  FILTER_MONTH_START,
  FILTER_YEAR_END,
  FILTER_YEAR_START,
  stringToDate,
  stringToDayJs,
} from '@common/utils/DateUtils';
import { KanbanText, KanbanTooltip } from 'kanban-design-system';

export type DateRangeFilter = {
  startDate?: Date;
  endDate?: Date;
};

export const DATE_MASK_BLOCKS_WITH_CONSTANTS = {
  YYYY: {
    mask: IMask.MaskedRange,
    from: FILTER_YEAR_START,
    to: FILTER_YEAR_END,
  },
  MM: {
    mask: IMask.MaskedRange,
    from: FILTER_MONTH_START,
    to: FILTER_MONTH_END,
    maxLength: 2,
  },
  DD: {
    mask: IMask.MaskedRange,
    from: FILTER_DAY_START,
    to: FILTER_DAY_END,
    maxLength: 2,
  },
};

export type AmtDateRangeInputProps = DateRangeFilter & {
  //state of this inputs date range
  updateFilters: React.Dispatch<SetStateAction<DateRangeFilter>>;
  //func called when inputs blurred.
  onBlur?: () => void;
  //state of form that has error or not.
  setValid?: React.Dispatch<SetStateAction<boolean>>;
  onClear?: () => void;
  tooltipLabel?: string;
};
export const dateRangeKeys: (keyof DateRangeFilter)[] = ['startDate', 'endDate'];
const isInRange = (date: Date, startDate?: Date, endDate?: Date, hoverEndDate?: Date | null) => {
  const selectedOrHoverEndDate = endDate || hoverEndDate;
  if (startDate && selectedOrHoverEndDate) {
    if (dayjs(selectedOrHoverEndDate).isBefore(startDate) && dayjs(date).isAfter(selectedOrHoverEndDate) && dayjs(date).isBefore(startDate)) {
      return true;
    } else if (dayjs(selectedOrHoverEndDate).isAfter(startDate) && dayjs(date).isAfter(startDate) && dayjs(date).isBefore(selectedOrHoverEndDate)) {
      return true;
    }
  }
};
dayjs.extend(customParseFormat);

export function AmtDateRangeInput({
  endDate,
  onBlur: onInputValid,
  onClear,
  setValid,
  startDate,
  tooltipLabel,
  updateFilters,
}: AmtDateRangeInputProps) {
  const [hoverEndDate, setHoverEndDate] = useState<Date | null>(null);

  const handleChange = useCallback(
    (values: { startDateInput: string; endDateInput: string }) => {
      updateFilters((prev) => {
        return {
          ...prev,
          endDate: stringToDate(values.endDateInput),
          startDate: stringToDate(values.startDateInput),
        };
      });
    },
    [updateFilters],
  );
  const form = useForm({
    initialValues: {
      startDateInput: dateToString(startDate),
      endDateInput: dateToString(endDate),
    },
    clearInputErrorOnChange: true,
    onValuesChange: handleChange,

    validateInputOnChange: true,
    validate: {
      startDateInput: (
        value: string,
        values: {
          endDateInput: string;
        },
      ) =>
        value
          ? value.length !== `${DATE_FORMAT_SIMPLE}`.length
            ? `Start date format dd/mm/yyyy`
            : stringToDayJs(values.endDateInput) && stringToDayJs(value)?.isAfter(stringToDayJs(values.endDateInput))
              ? 'Invalid range at start date'
              : null
          : null,
      endDateInput: (
        value: string,
        values: {
          startDateInput: string;
        },
      ) =>
        value
          ? value.length !== `${DATE_FORMAT_SIMPLE}`.length
            ? `End date format dd/mm/yyyy`
            : stringToDayJs(values.startDateInput) && stringToDayJs(value)?.isBefore(stringToDayJs(values.startDateInput))
              ? 'Invalid range at end date'
              : null
          : null,
    },
  });
  const hasValue = !!form.getValues().endDateInput || !!form.getValues().startDateInput;

  const handlePickDates = useCallback(
    (newDate: Date) => {
      if (!startDate) {
        form.setFieldValue('startDateInput', dateToString(newDate));
        updateFilters((prev) => ({ ...prev, startDate: newDate }));
      } else {
        //case chua chon endDate
        if (!endDate) {
          if (dayjs(startDate).isAfter(newDate)) {
            updateFilters((prev) => ({ ...prev, endDate: startDate, startDate: newDate }));
            form.setFieldValue('startDateInput', (oldInputStart) => {
              form.setFieldValue('endDateInput', oldInputStart);
              return dateToString(newDate);
            });
          } else {
            form.setFieldValue('endDateInput', dateToString(newDate));
            updateFilters((prev) => ({ ...prev, endDate: newDate }));
          }
        } else {
          form.setValues({ startDateInput: dateToString(newDate), endDateInput: '' });
          updateFilters((prev) => ({ ...prev, startDate: newDate, endDate: undefined }));
        }
      }
    },
    [startDate, form, updateFilters, endDate],
  );

  useEffect(() => {
    if (startDate || endDate) {
      if (!form.initialized) {
        form.initialize({
          startDateInput: dateToString(startDate),
          endDateInput: dateToString(endDate),
        });
      }
    }
  }, [form, startDate, endDate]);
  useEffect(() => {
    if (setValid) {
      setValid(form.isValid());
    }
  }, [form, setValid]);
  const handleOnInputValid = useCallback(() => {
    if (onInputValid && form.isValid()) {
      onInputValid();
    }
  }, [form, onInputValid]);
  const handleClear = useCallback(() => {
    onClear?.();
    form.setValues({ startDateInput: '', endDateInput: '' });
    form.setErrors({});
  }, [form, onClear]);

  return (
    <Box w={'35%'}>
      <Popover position='bottom' withArrow shadow='md' onClose={handleOnInputValid}>
        <KanbanTooltip disabled={!tooltipLabel} label={tooltipLabel} position='top-start'>
          <Flex align={'flex-start'} direction={'column'}>
            <form>
              <Flex align={'center'} w={'fit-content'} direction={'row'} className={styled.dateRangeWrapper}>
                <Flex align={'center'} w={'fit-content'} direction={'row'} onBlur={handleOnInputValid}>
                  <Input
                    className={styled.dateRangeInputWrap}
                    c={'white'}
                    size='xs'
                    type='text'
                    mb='0'
                    key={form.key('startDateInput')}
                    {...form.getInputProps('startDateInput')}
                    component={IMaskInput}
                    mask={[
                      {
                        mask: `${DATE_FORMAT_SIMPLE}`,
                        blocks: DATE_MASK_BLOCKS_WITH_CONSTANTS,
                      },
                    ]}
                    placeholder={`${DATE_FORMAT_SIMPLE}`.toLowerCase()}
                  />
                  <KanbanText c={'var(--mantine-color-gray-4)'}>-</KanbanText>
                  <Input
                    className={styled.dateRangeInputWrap}
                    c={'white'}
                    size='xs'
                    type='text'
                    mb='0'
                    key={form.key('endDateInput')}
                    {...form.getInputProps('endDateInput')}
                    component={IMaskInput}
                    mask={[
                      {
                        mask: `${DATE_FORMAT_SIMPLE}`,
                        blocks: DATE_MASK_BLOCKS_WITH_CONSTANTS,
                      },
                    ]}
                    placeholder={`${DATE_FORMAT_SIMPLE}`.toLowerCase()}
                  />
                </Flex>
                <Popover.Target>
                  <Flex align={'center'} mr={'xs'}>
                    <IconCalendar color='var(--mantine-color-gray-4)' />
                  </Flex>
                </Popover.Target>
                {onClear && hasValue && (
                  <ActionIcon
                    size='xs'
                    mr={'xs'}
                    onMouseDown={(e) => {
                      e.preventDefault();
                    }}
                    onClick={handleClear}>
                    <IconX size={14} />
                  </ActionIcon>
                )}
              </Flex>
            </form>

            <Text c='red' fz='sm' ml='0'>
              {form.errors['startDateInput'] ? form.errors['startDateInput'] : form.errors['endDateInput']}
            </Text>

            {Object.keys(form.errors).length === 0 && (
              <Popover.Dropdown>
                <Calendar
                  w={'fit-content'}
                  size='xs'
                  numberOfColumns={2}
                  defaultDate={startDate ? startDate : endDate ? endDate : new Date()}
                  getDayProps={(date) => {
                    const isDateInRange = isInRange(date, startDate, endDate, hoverEndDate);
                    const res: Omit<Partial<DayProps>, 'classNames' | 'styles' | 'vars'> = {
                      onMouseEnter: () => {
                        setHoverEndDate(date);
                      },
                      onMouseLeave: () => setHoverEndDate(null),
                      selected: (startDate && dayjs(date).isSame(startDate)) || (endDate && dayjs(date).isSame(endDate)),
                      onClick: () => handlePickDates(date),
                      inRange: isDateInRange,
                      bg: isDateInRange ? 'var(--mantine-color-blue-1)' : '',
                    };

                    return res;
                  }}
                />
              </Popover.Dropdown>
            )}
          </Flex>
        </KanbanTooltip>
      </Popover>
    </Box>
  );
}
