import { BaseURL } from '@common/constants/BaseUrl';
import { RequestConfig } from '@core/api';
import { User, UserSchema } from '@core/schema/User';
import { UserDetail, UserDetailSchema } from '@core/schema/UserDetails';
import { UserModel } from '@models/UserModel';
import { PaginationRequest } from './Type';
import { createRequest } from './Utils';
import { tableAffectedToMultiColumnFilterPaginationRequestModel } from '@common/utils/TableUtils';
import { TableAffactedSafeType } from 'kanban-design-system';
import { z } from 'zod';
import { ResponseData, createResponseSchema, Page, createPageSchema } from '@core/schema/Common';
import { GroupRole, GroupRoleSchema, GroupUser, GroupUserSchema } from '@core/schema/Group';
import { UserRole, UserRoleSchema } from '@core/schema/Role';
import qs from 'qs';

export class UserApi {
  static me(): RequestConfig<ResponseData<UserDetail>> {
    return {
      url: `${BaseURL.user}/me`,
      method: 'GET',
      schema: createResponseSchema(UserDetailSchema),
    };
  }

  static findAll(pagination: TableAffactedSafeType): RequestConfig<ResponseData<Page<User>>, PaginationRequest> {
    return {
      url: `${BaseURL.user}/all`,
      method: 'POST',
      schema: createResponseSchema(createPageSchema(UserSchema)),
      data: tableAffectedToMultiColumnFilterPaginationRequestModel(pagination),
    };
  }

  static findById(id: number) {
    return createRequest({
      url: `${BaseURL.user}/:id`,
      method: 'GET',
      schema: createResponseSchema(UserSchema),
      pathVariable: {
        id,
      },
    });
  }

  static save(user: UserModel): RequestConfig<ResponseData<User>> {
    return {
      url: `${BaseURL.user}`,
      method: 'POST',
      schema: createResponseSchema(UserSchema),
      data: user,
    };
  }

  static updateUserStatus({ isActive, username }: { username: string; isActive: boolean }) {
    return createRequest({
      url: `${BaseURL.user}/:username`,
      method: 'PUT',
      schema: createResponseSchema(UserSchema),
      pathVariable: {
        username,
      },
      params: {
        isActive,
      },
    });
  }

  static deleteByUsername(username: string): RequestConfig<ResponseData<void>> {
    return {
      url: `${BaseURL.user}/${username}`,
      method: 'DELETE',
    };
  }

  static deleteByUsernameIn(usernames: string[]): RequestConfig<ResponseData<void>> {
    return {
      url: `${BaseURL.user}`,
      method: 'DELETE',
      params: { usernames },
      paramsSerializer: (params) => qs.stringify(params, { arrayFormat: 'repeat' }),
    };
  }

  static existsByUsername(id: number | undefined, username: string) {
    return createRequest({
      url: `${BaseURL.user}/${username}/exists?id=${id}`,
      method: 'GET',
      schema: createResponseSchema(z.boolean()),
    });
  }

  static findAllGroupRolesByUsername(
    username: string,
    pagination: TableAffactedSafeType,
  ): RequestConfig<ResponseData<Page<GroupRole>>, PaginationRequest> {
    return {
      url: `${BaseURL.user}/${username}/group-roles`,
      method: 'POST',
      schema: createResponseSchema(createPageSchema(GroupRoleSchema)),
      data: tableAffectedToMultiColumnFilterPaginationRequestModel(pagination),
    };
  }

  static findAllSelectedRolesByUserName(
    username: string,
    pagination: TableAffactedSafeType,
  ): RequestConfig<ResponseData<Page<UserRole>>, PaginationRequest> {
    return {
      url: `${BaseURL.user}/${username}/roles`,
      method: 'POST',
      schema: createResponseSchema(createPageSchema(UserRoleSchema)),
      data: tableAffectedToMultiColumnFilterPaginationRequestModel(pagination),
    };
  }

  static findAllRolesWithSelectionByUsername(
    username: string,
    pagination: TableAffactedSafeType,
  ): RequestConfig<ResponseData<Page<UserRole>>, PaginationRequest> {
    return {
      url: `${BaseURL.user}/${username}/roles/all`,
      method: 'POST',
      schema: createResponseSchema(createPageSchema(UserRoleSchema)),
      data: tableAffectedToMultiColumnFilterPaginationRequestModel(pagination),
    };
  }

  static findAllRolesByGroupIdIn(ids: number[], pagination: TableAffactedSafeType): RequestConfig<ResponseData<Page<GroupRole>>, PaginationRequest> {
    return {
      url: `${BaseURL.user}/group-roles?ids=${ids}`,
      method: 'POST',
      schema: createResponseSchema(createPageSchema(GroupRoleSchema)),
      data: tableAffectedToMultiColumnFilterPaginationRequestModel(pagination),
    };
  }

  static findAllGroupsWithSelectionByUsername(
    username: string,
    pagination: TableAffactedSafeType,
  ): RequestConfig<ResponseData<Page<GroupUser>>, PaginationRequest> {
    return {
      url: `${BaseURL.user}/${username}/groups/all`,
      method: 'POST',
      schema: createResponseSchema(createPageSchema(GroupUserSchema)),
      data: tableAffectedToMultiColumnFilterPaginationRequestModel(pagination),
    };
  }

  static findAllSelectedGroupByUserName(
    username: string,
    pagination: TableAffactedSafeType,
  ): RequestConfig<ResponseData<Page<GroupUser>>, PaginationRequest> {
    return {
      url: `${BaseURL.user}/${username}/groups`,
      method: 'POST',
      schema: createResponseSchema(createPageSchema(GroupUserSchema)),
      data: tableAffectedToMultiColumnFilterPaginationRequestModel(pagination),
    };
  }
}
