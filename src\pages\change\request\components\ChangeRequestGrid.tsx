import React from 'react';
import GridLayout, { Layout, WidthProvider } from 'react-grid-layout';
import { Box, Paper } from '@mantine/core';
import styles from '@resources/styles/ChangeTemplateBuilder.module.scss';
import { useFormContext } from 'react-hook-form';
import { GRID_COLS, ROW_HEIGHT } from '@common/constants/ChangeTemplateConstants';
import { ChangeRequestFieldItem, ChangeRequestModel } from '@core/schema/ChangeRequest';
import CustomFieldRenderer from './CustomFieldRenderer';
const ResponsiveGridLayout = WidthProvider(GridLayout);

interface ChangeRequestGridProps {
  layouts: Layout[];
  action: boolean;
}

const ChangeRequestGrid: React.FC<ChangeRequestGridProps> = ({ action, layouts }) => {
  const formMethods = useFormContext<ChangeRequestModel>();
  const fields = formMethods.getValues('fields') || [];
  return (
    <Paper p='0' shadow='sm' radius='md' mih={500}>
      <ResponsiveGridLayout
        layout={layouts}
        className='layout'
        compactType='vertical'
        useCSSTransforms
        containerPadding={[0, 0]}
        margin={[5, 5]}
        cols={GRID_COLS}
        rowHeight={ROW_HEIGHT}
        isDraggable={false}
        isResizable={false}
        width={1200}
        style={{ minHeight: 500 }}>
        {fields.map((field: ChangeRequestFieldItem, index: number) => {
          const layout = layouts.find((l) => l.i === field.customFieldId.toString());
          if (!layout) {
            return null;
          }

          return (
            <Box key={layout.i} data-grid={layout}>
              <Paper className={styles.gridItemPaper} shadow='none'>
                <CustomFieldRenderer
                  changeRequestField={field}
                  fieldIndex={index}
                  register={formMethods.register}
                  errors={formMethods.formState.errors}
                  control={formMethods.control}
                  action={action}
                />
              </Paper>
            </Box>
          );
        })}
      </ResponsiveGridLayout>
    </Paper>
  );
};

export default ChangeRequestGrid;
