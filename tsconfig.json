{
  "extends": "./tsconfig.path.json",
  "compilerOptions": {
    "target": "ES6",
    "lib": [
      "DOM",
      "ES6",
      "DOM.Iterable",
      "ScriptHost",
      "ES2016.Array.Include",
      "es2020.string",
      "esnext",
      "WebWorker"
    ],
    "strict": true,
    "outDir": "./out/js/src",
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react",
    "downlevelIteration": true,
    "experimentalDecorators": true,
    "importHelpers": true,
    "typeRoots": [
      "./typings",
      "./node_modules/@types"
    ],
    "sourceMap": true,
    "baseUrl": "./src",
    "noFallthroughCasesInSwitch": true,
    "ignoreDeprecations": "5.0",
    "importsNotUsedAsValues": "remove",
  },
  "include": [
    "./src/**/*",
  ],
}