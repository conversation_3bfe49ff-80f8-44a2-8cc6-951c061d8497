// #region Basic Styling
$rqb-spacing: 0.2rem !default;
$rqb-spacing-x: 1rem !default;
$rqb-background-color: rgba(97, 131, 241, 0.2) !default;
$rqb-background-color-between-rules: rgba(92, 92, 92, 0.3) !default;
$rqb-border-color: var(--mantine-color-gray-8) !default;
$rqb-border-style: solid !default;
$rqb-border-radius: 0.25rem !default;
$rqb-border-width: 2px !default;
// #endregion

// #region Drag-and-Drop Styling
$rqb-dnd-hover-border-color: var(--mantine-color-blue-5) !default;
$rqb-dnd-hover-background-color: rgba(173, 216, 230, 0.8);
$rqb-dnd-hover-border-style: solid !default;
$rqb-dnd-hover-border-width: 5px !default;
$rqb-dnd-copy-border-color: var(--mantine-color-blue-5) !default;
$rqb-dnd-drop-indicator-color: var(--mantine-color-green-6) !default;
$rqb-dnd-drop-indicator-style: dashed !default;
$rqb-dnd-drop-indicator-width: 2px !default;
// #endregion

.ruleGroup {
  display: flex;
  flex-direction: column;
  gap: $rqb-spacing;
  padding: $rqb-spacing $rqb-spacing-x;
  border: $rqb-border-width $rqb-border-style $rqb-border-color;
  border-radius: $rqb-border-radius;
  background-color: $rqb-background-color;
  transition: background-color 0.3s ease, border-color 0.3s ease;

  &.drag-hover {
    border-color: $rqb-dnd-hover-border-color;
    background-color: var(--mantine-color-blue-1);
    border-style: $rqb-dnd-hover-border-style;
    border-width: $rqb-dnd-hover-border-width;
    animation: pulse 0.7s infinite alternate;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  }

  .ruleGroup-body {
    display: flex;
    flex-direction: column;
    gap: $rqb-spacing;

    &:empty {
      display: none;
    }
  }

  .ruleGroup-header {
    display: none;
  }

  .rule {
    display: flex;
    align-items: center;
    transition: color 0.3s ease;
  }

  .betweenRules {
    display: flex;
    align-items: center;
    justify-content: space-between;

    &::before,
    &::after {
      content: '';
      height: 1px;
      background-color: $rqb-background-color-between-rules;
    }

    &::before {
      flex-grow: 1;
    }

    &::after {
      flex-grow: 15;
    }
  }
}

// #endregion

// #region Branch Styling
$rqb-branch-indent: $rqb-spacing !default;
$rqb-branch-color: $rqb-border-color !default;
$rqb-branch-width: $rqb-border-width !default;
$rqb-branch-radius: $rqb-border-radius !default;
$rqb-branch-style: $rqb-border-style !default;

.ruleGroup-branches {
  display: flex;
  flex-direction: column;

  .ruleGroup-branch {
    width: $rqb-branch-width;
    border-left: $rqb-branch-width $rqb-branch-style $rqb-branch-color;
    margin-left: $rqb-branch-indent;
    padding-left: $rqb-branch-indent;
    border-radius: $rqb-branch-radius;
  }
}

// #endregion

.dndDragging {
  background-color: $rqb-dnd-hover-background-color;
  border-color: $rqb-dnd-hover-border-color;
  border-style: $rqb-dnd-hover-border-style;
  border-width: $rqb-dnd-hover-border-width;
  box-shadow: 0 0 10px $rqb-dnd-hover-border-color;
  transition: background-color 0.3s ease, border-color 0.3s ease; // Hiệu ứng chuyển tiếp khi kéo
  border-radius: $rqb-border-radius;
  animation: fadeIn 0.5s; // Hiệu ứng fade-in
}

.dndOver {
  border-color: $rqb-dnd-drop-indicator-color;
  border-style: $rqb-dnd-drop-indicator-style;
  border-width: $rqb-dnd-drop-indicator-width;
  background-color: $rqb-background-color-between-rules;
  transition: border-color 0.3s ease;
}

// Hiệu ứng fade in
@keyframes fadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

// Hiệu ứng pulse cho trạng thái drag hover
@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

// #region Drag Handle Styling
$rqb-drag-handle-background: var(--mantine-color-gray-1) !default;
$rqb-drag-handle-border-color: var(--mantine-color-gray-3) !default;
$rqb-drag-handle-border-radius: 0.5rem !default;
$rqb-drag-handle-width: 2rem !default;
$rqb-drag-handle-height: 2rem !default;
$rqb-drag-handle-hover-bg: var(--mantine-color-gray-2) !default;
$rqb-drag-handle-transition: background-color 0.3s ease, transform 0.3s ease !default;

.queryBuilder-dragHandle {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
  transition: background-color 0.3s ease, transform 0.2s ease;
  content: '↕️';
  margin-left: 0.4rem;

  &:hover {
    background-color: $rqb-drag-handle-hover-bg;
    transform: scale(1.05);
  }

  &:active {
    cursor: grabbing;
    transform: scale(1);
  }

  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

// #endregion