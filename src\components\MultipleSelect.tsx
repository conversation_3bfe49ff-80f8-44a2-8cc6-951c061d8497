import { ComboboxData, ComboboxItem, ComboboxItemGroup } from '@mantine/core';
import { IconSelectAll, IconX } from '@tabler/icons-react';
import { KanbanMultiSelect, KanbanMultiSelectProps, KanbanTooltip } from 'kanban-design-system';
import React, { useCallback, useMemo } from 'react';

interface Props extends KanbanMultiSelectProps {
  showSelectAll?: boolean;
  showClearAll?: boolean;
}

const getAllOptionValues = (data: ComboboxData): string[] => {
  return data
    ?.map((ele) => {
      if (typeof ele === 'string') {
        return ele;
      }
      if (typeof ele === 'object' && Object.hasOwn(ele, 'group')) {
        return getAllOptionValues((ele as ComboboxItemGroup).items);
      }
      return (ele as ComboboxItem).value;
    })
    .flat();
};

const MultipleSelect = ({ showClearAll = true, showSelectAll = true, ...rest }: Props) => {
  const { data, onChange, value } = rest;
  const clearAllHandler = useCallback(() => {
    onChange && onChange([]);
  }, [onChange]);
  const allOptionValues = useMemo(() => getAllOptionValues(data || []), [data]);
  const selectAllHandler = useCallback(() => {
    onChange && onChange(allOptionValues);
  }, [allOptionValues, onChange]);
  const showClearAllButton = showClearAll && value && value?.length > 0;
  const selectedAllOption = (value?.length || 0) === (allOptionValues?.length || 0);
  const showSelectAllButton = showSelectAll && !selectedAllOption;
  return (
    <KanbanMultiSelect
      rightSectionPointerEvents='all'
      checkIconPosition='left'
      rightSection={
        <>
          {showSelectAllButton && (
            <KanbanTooltip label='Select All'>
              <IconSelectAll onClick={selectAllHandler} />
            </KanbanTooltip>
          )}
          {showClearAllButton && (
            <KanbanTooltip label='Clear All'>
              <IconX onClick={clearAllHandler} />
            </KanbanTooltip>
          )}
        </>
      }
      {...rest}
    />
  );
};

export default MultipleSelect;
