import { AclPermissionModel } from '@core/schema/AclPermission';

export enum PermissionAction {
  SUPER_ADMIN = 'SUPER_ADMIN',
  CHANGE_VIEW = 'CHANGE_VIEW',
  CHANGE_ADD = 'CHANGE_ADD',
  <PERSON><PERSON><PERSON>_UPDATE = 'CHANGE_UPDATE',
  CHANGE_DELETE = 'CHANGE_DELETE',
  CHANGE_FINISH_STAGE_SUBMISSION = 'CHANGE_FINISH_STAGE_SUBMISSION',
  CHANGE_FINISH_STAGE_APPROVAL = 'CHANGE_FINISH_STAGE_APPROVAL',
  CHAN<PERSON>_FINISH_STAGE_IMPLEMENTATION = 'CHANGE_FINISH_STAGE_IMPLEMENTATION',
  CHANGE_FINISH_STAGE_CLOSE = 'CHANGE_FINISH_STAGE_CLOSE',
  CHANGE_APPROVE = 'CHANGE_APPROVE',
  CHANGE_ASSIGN = 'CHANGE_ASSIGN',
  CHANGE_COMMENT = 'CHANGE_COMMENT',
  ACTION_VIEW_LIST = 'ACTION_VIEW_LIST',
  ACTION_VIEW_DETAIL = 'ACTION_VIEW_DETAIL',
  ACTION_ADD = 'ACTION_ADD',
  ACTION_UPDATE = 'ACTION_UPDATE',
  ACTION_DELETE = 'ACTION_DELETE',
  ACTION_RUN = 'ACTION_RUN',
  ACTION_STOP = 'ACTION_STOP',
  ACTION_EXPORT = 'ACTION_EXPORT',
  ACTION_IMPORT = 'ACTION_IMPORT',
  ACTION_APPROVE = 'ACTION_APPROVE',
  ACTION_COMMENT = 'ACTION_COMMENT',
  ACTION_ASSIGN = 'ACTION_ASSIGN',
  REPORT_VIEW = 'REPORT_VIEW',
  REPORT_ADD = 'REPORT_ADD',
  REPORT_UPDATE = 'REPORT_UPDATE',
  REPORT_DELETE = 'REPORT_DELETE',
}

export enum PermissionActionModule {
  // 0.ADMIN
  ADMINISTRATOR = 'ADMINISTRATOR',

  // 1. User và phân quyền

  USER_MANAGEMENT = 'USER_MANAGEMENT',
  GROUP_MANAGEMENT = 'GROUP_MANAGEMENT',
  ROLE_MANAGEMENT = 'ROLE_MANAGEMENT',

  // 2. Change (Thay thế SDP)
  CHANGE_SDP = 'CHANGE_SDP',
  CAB_MANAGEMENT = 'CAB_MANAGEMENT',
  IMPLEMENTER_MANAGEMENT = 'IMPLEMENTER_MANAGEMENT',
  TEMPLATE_BUILDER = 'TEMPLATE_BUILDER',
  FLOW_BUILDER = 'FLOW_BUILDER',
  CATEGORY_BUILDER = 'CATEGORY_BUILDER',

  // 3. Task & Action
  TASK_MANAGEMENT = 'TASK_MANAGEMENT',
  TASK_FLOW_BUILDER = 'TASK_FLOW_BUILDER',
  SCHEDULER_MANAGEMENT = 'SCHEDULER_MANAGEMENT',
  FORM_BUILDER = 'FORM_BUILDER',

  // 4. Job và tích hợp
  JOB_MANAGEMENT = 'JOB_MANAGEMENT',
  INTEGRATION_API_MANAGEMENT = 'INTEGRATION_API_MANAGEMENT',

  // 5. Khác
  MONITORING = 'MONITORING',
  ALERT_MANAGEMENT = 'ALERT_MANAGEMENT',
  QUERY = 'QUERY',
  AUDIT_LOG = 'AUDIT_LOG',

  // 6. Quản lý môi trường
  DEV_MANAGEMENT = 'DEV_MANAGEMENT',
  LIVE_MANAGEMENT = 'LIVE_MANAGEMENT',
  PRE_PRODUCTION_MANAGEMENT = 'PRE_PRODUCTION_MANAGEMENT',
  ENVIRONMENT_SYNC = 'ENVIRONMENT_SYNC',

  // other khi khong set
  OTHER = 'OTHER',
}

export enum PermissionActionType {
  MODULE = 'MODULE',
  SUB_MODULE = 'SUB_MODULE',
}

export const ACL_PERMISSION_KEYS = {
  ADMIN_MANAGE_SYSTEM: 'ADMIN_MANAGE_SYSTEM',

  CHANGE_VIEW: 'CHANGE_VIEW',
  CHANGE_ADD: 'CHANGE_ADD',
  CHANGE_UPDATE: 'CHANGE_UPDATE',
  CHANGE_DELETE: 'CHANGE_DELETE',
  CHANGE_FINISH_STAGE_SUBMISSION: 'CHANGE_FINISH_STAGE_SUBMISSION',
  CHANGE_FINISH_STAGE_APPROVAL: 'CHANGE_FINISH_STAGE_APPROVAL',
  CHANGE_FINISH_STAGE_IMPLEMENTATION: 'CHANGE_FINISH_STAGE_IMPLEMENTATION',
  CHANGE_FINISH_STAGE_CLOSE: 'CHANGE_FINISH_STAGE_CLOSE',
  CHANGE_APPROVE: 'CHANGE_APPROVE',
  CHANGE_ASSIGN: 'CHANGE_ASSIGN',
  CHANGE_COMMENT: 'CHANGE_COMMENT',
  ACTION_VIEW_LIST: 'ACTION_VIEW_LIST',
  ACTION_VIEW_DETAIL: 'ACTION_VIEW_DETAIL',
  ACTION_ADD: 'ACTION_ADD',
  ACTION_UPDATE: 'ACTION_UPDATE',
  ACTION_DELETE: 'ACTION_DELETE',
  ACTION_RUN: 'ACTION_RUN',
  ACTION_STOP: 'ACTION_STOP',
  ACTION_EXPORT: 'ACTION_EXPORT',
  ACTION_IMPORT: 'ACTION_IMPORT',
  ACTION_APPROVE: 'ACTION_APPROVE',
  ACTION_COMMENT: 'ACTION_COMMENT',
  ACTION_ASSIGN: 'ACTION_ASSIGN',
  REPORT_VIEW: 'REPORT_VIEW',
  REPORT_ADD: 'REPORT_ADD',
  REPORT_UPDATE: 'REPORT_UPDATE',
  REPORT_DELETE: 'REPORT_DELETE',
};

//init acl permission
export const ACL_PERMISSIONS: Record<keyof typeof ACL_PERMISSION_KEYS, AclPermissionModel> = {
  // Admin
  ADMIN_MANAGE_SYSTEM: {
    permission: PermissionAction.SUPER_ADMIN,
    module: PermissionActionModule.ADMINISTRATOR,
    description: 'Quyền cao nhất của hệ thống',
  },

  // Change (Thay thế SDP)
  CHANGE_VIEW: {
    permission: PermissionAction.CHANGE_VIEW,
    module: PermissionActionModule.CHANGE_SDP,
    description: 'Xem danh sách/chi tiết change',
  },
  CHANGE_ADD: { permission: PermissionAction.CHANGE_ADD, module: PermissionActionModule.CHANGE_SDP, description: 'Tạo mới change' },
  CHANGE_UPDATE: { permission: PermissionAction.CHANGE_UPDATE, module: PermissionActionModule.CHANGE_SDP, description: 'Cập nhật change' },
  CHANGE_DELETE: { permission: PermissionAction.CHANGE_DELETE, module: PermissionActionModule.CHANGE_SDP, description: 'Xóa change' },
  CHANGE_FINISH_STAGE_SUBMISSION: {
    permission: PermissionAction.CHANGE_FINISH_STAGE_SUBMISSION,
    module: PermissionActionModule.CHANGE_SDP,
    description: 'Chuyển trạng thái từ submission sang approval',
  },
  CHANGE_FINISH_STAGE_APPROVAL: {
    permission: PermissionAction.CHANGE_FINISH_STAGE_APPROVAL,
    module: PermissionActionModule.CHANGE_SDP,
    description: 'Chuyển trạng thái từ approval sang implementation',
  },
  CHANGE_FINISH_STAGE_IMPLEMENTATION: {
    permission: PermissionAction.CHANGE_FINISH_STAGE_IMPLEMENTATION,
    module: PermissionActionModule.CHANGE_SDP,
    description: 'Chuyển trạng thái từ implementation sang close',
  },
  CHANGE_FINISH_STAGE_CLOSE: {
    permission: PermissionAction.CHANGE_FINISH_STAGE_CLOSE,
    module: PermissionActionModule.CHANGE_SDP,
    description: 'Đóng change',
  },
  CHANGE_APPROVE: {
    permission: PermissionAction.CHANGE_APPROVE,
    module: PermissionActionModule.CHANGE_SDP,
    description: 'Cho phép phê duyệt change/action',
  },
  CHANGE_ASSIGN: {
    permission: PermissionAction.CHANGE_ASSIGN,
    module: PermissionActionModule.CHANGE_SDP,
    description: 'Gán/gỡ user vào task/action',
  },
  CHANGE_COMMENT: {
    permission: PermissionAction.CHANGE_COMMENT,
    module: PermissionActionModule.CHANGE_SDP,
    description: 'Thêm comment cho change',
  },

  // Task & Action
  ACTION_VIEW_LIST: {
    permission: PermissionAction.ACTION_VIEW_LIST,
    module: PermissionActionModule.TASK_MANAGEMENT,
    description: 'Xem danh sách action',
  },
  ACTION_VIEW_DETAIL: {
    permission: PermissionAction.ACTION_VIEW_DETAIL,
    module: PermissionActionModule.TASK_MANAGEMENT,
    description: 'Xem chi tiết action',
  },
  ACTION_ADD: { permission: PermissionAction.ACTION_ADD, module: PermissionActionModule.TASK_MANAGEMENT, description: 'Thêm mới action' },
  ACTION_UPDATE: { permission: PermissionAction.ACTION_UPDATE, module: PermissionActionModule.TASK_MANAGEMENT, description: 'Cập nhật action' },
  ACTION_DELETE: { permission: PermissionAction.ACTION_DELETE, module: PermissionActionModule.TASK_MANAGEMENT, description: 'Xóa action' },
  ACTION_RUN: { permission: PermissionAction.ACTION_RUN, module: PermissionActionModule.TASK_MANAGEMENT, description: 'Chạy action' },
  ACTION_STOP: { permission: PermissionAction.ACTION_STOP, module: PermissionActionModule.TASK_MANAGEMENT, description: 'Dừng action' },
  ACTION_EXPORT: {
    permission: PermissionAction.ACTION_EXPORT,
    module: PermissionActionModule.TASK_MANAGEMENT,
    description: 'Xuất cấu hình action ra file',
  },
  ACTION_IMPORT: {
    permission: PermissionAction.ACTION_IMPORT,
    module: PermissionActionModule.TASK_MANAGEMENT,
    description: 'Import file vào action',
  },
  ACTION_APPROVE: { permission: PermissionAction.ACTION_APPROVE, module: PermissionActionModule.TASK_MANAGEMENT, description: 'Phê duyệt action' },
  ACTION_COMMENT: {
    permission: PermissionAction.ACTION_COMMENT,
    module: PermissionActionModule.TASK_MANAGEMENT,
    description: 'Thêm comment cho action (khác với quyền update action)',
  },
  ACTION_ASSIGN: {
    permission: PermissionAction.ACTION_ASSIGN,
    module: PermissionActionModule.TASK_MANAGEMENT,
    description: 'Gán user vào action',
  },

  // Khác
  REPORT_VIEW: { permission: PermissionAction.REPORT_VIEW, module: PermissionActionModule.OTHER, description: 'Xem report' },
  REPORT_ADD: { permission: PermissionAction.REPORT_ADD, module: PermissionActionModule.OTHER, description: 'Tạo report' },
  REPORT_UPDATE: { permission: PermissionAction.REPORT_UPDATE, module: PermissionActionModule.OTHER, description: 'Thêm report' },
  REPORT_DELETE: { permission: PermissionAction.REPORT_DELETE, module: PermissionActionModule.OTHER, description: 'Chỉnh sửa report' },
};
export const ACL_PERMISSIONS_CHECKLIST = Object.values(ACL_PERMISSIONS);

//DEFAULT acl permission field value
export const DEFAULT_RESOURCE_ID = '0';
export const DEFAULT_RESOURCE_PARENT_ID = '0';
export const DEFAULT_TYPE = PermissionActionType.MODULE;

// default check permissions
