import { AdvancedFilterMappingType, getDefaultTableAffected, KanbanCheckbox, KanbanText, KanbanTitle } from 'kanban-design-system';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanTable, KanbanTableProps, TableAffactedSafeType } from 'kanban-design-system';
import { renderDateTime } from 'kanban-design-system';
import { Box, Flex } from '@mantine/core';
import equal from 'fast-deep-equal';
import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { GroupApi } from '@api/GroupApi';

import useFetch from '@core/hooks/useFetch';
import customStyled from '@resources/styles/Common.module.scss';

import { UserGroupModel } from '@models/GroupRoleUserModel';
import { FilterTextInput } from '@components/AmtFilterTextInput';
import { ItemCheckComponentProps } from '@core/schema/Common';

export type UserGroupPopupFilter = {
  name?: string;
  department?: string;
  center?: string;
  userName?: string;
};

const initOrUpdatedFilterPayloads = (prev?: TableAffactedSafeType, initFilters?: UserGroupPopupFilter) => {
  const prevFilter = prev || getDefaultTableAffected();
  if (initFilters) {
    const filterObj: AdvancedFilterMappingType<UserGroupModel> = {
      ['name']: {
        filterOption: 'contains',
        value: {
          fromValue: initFilters.name,
        },
      },
      ['center']: {
        filterOption: 'contains',
        value: {
          fromValue: initFilters.center,
        },
      },
      ['department']: {
        filterOption: 'contains',
        value: {
          fromValue: initFilters.department,
        },
      },
      ['userName']: {
        filterOption: 'contains',
        value: {
          fromValue: initFilters.userName,
        },
      },
    };
    const advancedFilterMapping = { ...prevFilter.advancedFilterMapping, ...filterObj };
    return { ...prevFilter, advancedFilterMapping: { ...advancedFilterMapping } };
  }
  return prevFilter;
};

export type SearchObj = { field: string; value: { fromValue?: any; toValue?: any } };
export const UserGroupTableComponent: React.FC<ItemCheckComponentProps<UserGroupModel>> = ({
  parentId: groupId,
  selecteds: selectedUsers,
  setOldPagings,
  setSelecteds,
  setToDeletes,
  setToInserts,
  toDeletes,
}) => {
  const [totalRecords, setTotalRecords] = useState(0);
  const [listData, setListData] = useState<UserGroupModel[]>([]);

  const [tableAffected, setTableAffectedChange] = useState<TableAffactedSafeType>();
  const [inputFilters, setInputFilters] = useState<UserGroupPopupFilter>({});
  const [onBlurSearchFilter, setOnBlurSearchFilter] = useState<UserGroupPopupFilter>({});

  const { data: tableDataResponse } = useFetch(
    GroupApi.findPagingUsersByGroupIdAndOtherUsers(
      groupId,
      initOrUpdatedFilterPayloads(tableAffected, onBlurSearchFilter) || getDefaultTableAffected(),
    ),
    { enabled: !!tableAffected },
  );
  //table data
  useEffect(() => {
    if (tableDataResponse?.data?.content) {
      const results = tableDataResponse.data.content;
      setListData(results);
      setTotalRecords(tableDataResponse.data.totalElements);
      setSelecteds((prev) => {
        const updateMap = { ...prev };
        [...results.filter((it) => !(it.userName in updateMap) && !(it.userName in toDeletes) && it.groupSelected)].forEach((item) => {
          updateMap[item.userName] = item;
        });
        return updateMap;
      });

      if (setOldPagings) {
        setOldPagings((prev) => {
          const updateMap = { ...prev };
          [...results.filter((it) => !(it.userName in updateMap) && it.groupSelected)].forEach((item) => {
            updateMap[item.userName] = item;
          });
          return updateMap;
        });
      }
    }
  }, [tableDataResponse?.data, setSelecteds, toDeletes, setOldPagings]);
  const handleSearch = useCallback(() => {
    const filter = { ...inputFilters };
    setOnBlurSearchFilter(filter);
    setTableAffectedChange((prev) => initOrUpdatedFilterPayloads(prev, filter));
  }, [inputFilters]);

  const handleCheckAndUnCheck = useCallback(
    (rowData: UserGroupModel, checked: boolean) => {
      const key = rowData.userName;
      setSelecteds((prev) => {
        const updateMap = { ...prev };
        if (checked) {
          updateMap[key] = rowData;
        } else {
          delete updateMap[key];
        }

        return updateMap;
      });

      //add /remove item in insert list
      setToInserts((prev) => {
        const updateMap = { ...prev };
        if (checked) {
          updateMap[key] = rowData;
        } else {
          delete updateMap[key];
        }
        return updateMap;
      });
      // add /remove item in delete list
      setToDeletes((prev) => {
        const updateMap = { ...prev };
        if (checked) {
          delete updateMap[key];
        } else {
          updateMap[key] = rowData;
        }
        return updateMap;
      });
    },
    [setSelecteds, setToDeletes, setToInserts],
  );

  const tableProps: KanbanTableProps<UserGroupModel> = useMemo(() => {
    const tblProps: KanbanTableProps<UserGroupModel> = {
      title: '',
      columns: [
        {
          name: 'groupSelected',
          title: '',
          width: '5%',
          customRender: (data, row: UserGroupModel) => {
            return (
              <KanbanCheckbox checked={row.userName in selectedUsers} onChange={(e) => handleCheckAndUnCheck(row, e.target.checked)}></KanbanCheckbox>
            );
          },
        },
        {
          name: 'index',
          title: 'No.',
          width: '5%',
          customRender: (_, rowData) => {
            return <KanbanText>{listData.indexOf(rowData) + 1 + ((tableAffected?.page || 0) - 1) * (tableAffected?.rowsPerPage || 0)}</KanbanText>;
          },
        },
        {
          name: 'id',
          title: 'ID',
          hidden: true,
        },
        {
          name: 'name',
          title: 'Fullname',
        },
        {
          name: 'userName',
          title: 'Username',
        },
        {
          name: 'department',
          title: 'Department',
        },
        {
          name: 'center',
          title: 'Center',
        },
        {
          name: 'modifiedBy',
          title: 'Modified By',
          hidden: true,
        },
        {
          name: 'modifiedDate',
          title: 'Modified date',
          hidden: true,
          customRender: renderDateTime,
        },
        {
          name: 'createdBy',
          title: 'Created By',
          hidden: true,
        },
        {
          name: 'createdDate',
          title: 'Created date',
          customRender: renderDateTime,
          hidden: true,
        },
      ],
      data: listData,
      serverside: {
        totalRows: totalRecords,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffected, dataSet)) {
            setTableAffectedChange((prev) => (prev ? { ...dataSet, advancedFilterMapping: prev.advancedFilterMapping } : { ...dataSet }));
          }
        },
      },
      pagination: {
        enable: true,
      },
    };
    return tblProps;
  }, [handleCheckAndUnCheck, listData, selectedUsers, tableAffected, totalRecords]);

  return (
    <Box className={customStyled.tableCs}>
      <HeaderTitleComponent
        title=''
        leftSection={
          <Flex dir='row' gap='xs' align={'center'}>
            <KanbanTitle fz={'h4'}>Users</KanbanTitle>
          </Flex>
        }
      />
      <Flex dir='row' gap='xs' align={'center'} mb='xs'>
        <FilterTextInput
          placeholder='Fullname'
          value={inputFilters.name || ''}
          onBlur={handleSearch}
          onChange={(val) => {
            setInputFilters((prev) => ({ ...prev, name: val.target.value }));
          }}
        />
        <FilterTextInput
          placeholder='Username'
          value={inputFilters.userName || ''}
          onBlur={handleSearch}
          onChange={(val) => {
            setInputFilters((prev) => ({ ...prev, userName: val.target.value }));
          }}
        />
        <FilterTextInput
          placeholder='Department'
          value={inputFilters.department || ''}
          onBlur={handleSearch}
          onChange={(val) => {
            setInputFilters((prev) => ({ ...prev, department: val.target.value }));
          }}
        />
        <FilterTextInput
          placeholder='Center'
          value={inputFilters.center || ''}
          onBlur={handleSearch}
          onChange={(val) => {
            setInputFilters((prev) => ({ ...prev, center: val.target.value }));
          }}
        />
      </Flex>
      <Box className={customStyled.elementBorder}>
        <KanbanTable {...tableProps} title='' />
      </Box>
    </Box>
  );
};

export default UserGroupTableComponent;
