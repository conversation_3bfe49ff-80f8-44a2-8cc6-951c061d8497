import { KanbanInput } from 'kanban-design-system';
import React, { useState } from 'react';
import { SimpleGrid } from '@mantine/core';
import { Controller, UseFormReturn } from 'react-hook-form';
import { ChangeWorkflowNodeModel, JenkinsAuthenticationModel } from '@models/ChangeWorkflowNodeModel';
import { COMMON_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { IconEye, IconEyeOff } from '@tabler/icons-react';
import { ChangeApplicationTypeEnum } from '@common/constants/ChangeApplicationConstants';
import { INPUT_REQUIRE } from '@core/message/MesageConstant';
import CIConfigMapper, { CIConfigMapperHandle } from './CIConfigMapper';

type JenkinsAuthenticationProps = {
  form: UseFormReturn<ChangeWorkflowNodeModel>;
  onChangeDetected?: () => void;
  ciConfigRef: React.RefObject<CIConfigMapperHandle>;
};

export const JenkinsAuthentication: React.FC<JenkinsAuthenticationProps> = (props) => {
  const { control, setValue } = props.form;
  const { onChangeDetected } = props;
  const id = props.form.watch('id');

  const application = props.form.watch('application');
  const [hasChanged, setHasChanged] = useState(false);
  const [requireValidation, setRequireValidation] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [hasErrorUrl, setHasErrorUrl] = useState(false);
  const [hasErrorUserName, setHasErrorUserName] = useState(false);
  const [hasErrorPassword, setHasErrorPassword] = useState(false);
  return (
    <Controller
      name='authentication'
      control={control}
      rules={{
        validate: (value) => {
          if (!hasChanged || !requireValidation) {
            return true;
          }
          if (!value) {
            return 'Authentication data is required';
          }
          try {
            const auth: JenkinsAuthenticationModel = JSON.parse(value);
            if (!auth.url?.trim()) {
              return 'Url is required';
            }
            if (!auth.userName?.trim()) {
              return 'Username is required';
            }
            if (!auth.password?.trim()) {
              return 'Password is required';
            }
            return true;
          } catch {
            return 'Invalid authentication JSON data';
          }
        },
      }}
      render={({ field }) => {
        let auth: JenkinsAuthenticationModel = { url: '', userName: '', password: '' };
        if (field.value) {
          try {
            auth = JSON.parse(field.value);
          } catch (e) {
            console.error('Invalid authentication JSON:', field.value);
          }
        }
        const errorAuthencation = props.form.formState.errors.authentication?.message;
        if (errorAuthencation) {
          setHasErrorUrl(!auth.url.trim());
          setHasErrorUserName(!auth.userName.trim());
          setHasErrorPassword(!auth.password.trim());
        }

        const handleChange = (key: keyof JenkinsAuthenticationModel, value: string) => {
          const trimmedValue = value.replace(/\s+/g, '');

          setValue('isChange', true);
          setHasChanged(true);
          if (key === 'userName' || key === 'url') {
            setRequireValidation(true);
          }
          onChangeDetected?.();
          const updatedAuth = {
            ...auth,
            [key]: trimmedValue,
          };

          const isUrlEmpty = !updatedAuth.url?.trim();
          const isUserNameEmpty = !updatedAuth.userName?.trim();
          const isPasswordEmpty = !updatedAuth.password?.trim();
          setHasErrorUrl(isUrlEmpty);
          setHasErrorUserName(isUserNameEmpty);
          setHasErrorPassword(isPasswordEmpty);
          if (isUrlEmpty && isUserNameEmpty && isPasswordEmpty) {
            field.onChange(null);
          } else {
            field.onChange(JSON.stringify(updatedAuth));
          }
        };
        return (
          <>
            <SimpleGrid cols={2}>
              <KanbanInput
                key={application === ChangeApplicationTypeEnum.JENKINS ? 'jenkins_url' : 'wla_jenkins'}
                label='Url'
                type='url'
                value={auth.url || ''}
                onChange={(e) => handleChange('url', e.target.value.trim())}
                error={hasErrorUrl ? INPUT_REQUIRE : undefined}
                required
                maxLength={COMMON_MAX_LENGTH}
              />
            </SimpleGrid>

            <SimpleGrid cols={2}>
              <KanbanInput
                label='User'
                value={auth.userName || ''}
                onChange={(e) => handleChange('userName', e.target.value.trim())}
                error={hasErrorUserName ? INPUT_REQUIRE : undefined}
                maxLength={COMMON_MAX_LENGTH}
                required
              />
              <KanbanInput
                label='Pass'
                type={showPassword ? 'text' : 'password'}
                autoComplete='new-password'
                placeholder={id ? '*********' : ''}
                value={auth.password || ''}
                maxLength={COMMON_MAX_LENGTH}
                onChange={(e) => handleChange('password', e.target.value.trim())}
                error={hasErrorPassword ? INPUT_REQUIRE : undefined}
                required
                rightSection={
                  showPassword ? (
                    <IconEyeOff size={16} stroke={1.5} style={{ cursor: 'pointer' }} onClick={() => setShowPassword(!showPassword)} />
                  ) : (
                    <IconEye size={16} stroke={1.5} style={{ cursor: 'pointer' }} onClick={() => setShowPassword(!showPassword)} />
                  )
                }
              />
            </SimpleGrid>
            <CIConfigMapper form={props.form} ref={props.ciConfigRef} />
          </>
        );
      }}
    />
  );
};

export default JenkinsAuthentication;
