import React, { useState } from 'react';
import { KanbanButton, KanbanIconButton, KanbanModal, KanbanSelect, KanbanTable, KanbanText, KanbanTextarea } from 'kanban-design-system';
import { Badge } from '@mantine/core';

import { IconMessage } from '@tabler/icons-react';
import { useFormContext } from 'react-hook-form';
import { ChangeRequestRoleList } from '@core/schema/ChangeRequestRole';
import { statusConfig, WAITING_APPROVAL } from './approval/ChangeRequestApprovalComponent';
import ApprovalUserSelect from './ApprovalUserSelect';
import { ChangeRequestApprovalStatusEnum } from '@core/schema/ChangeRequestApproval';
import { ChangeRequestTransitionModel } from '@core/schema/ChangeRequest';
import { useDisclosure } from '@mantine/hooks';
import { ChangeRequestApi } from '@api/ChangeRequestApi';
import useMutate from '@core/hooks/useMutate';
import { APPROVAL_ACTION_OPTIONS, ChangeRequestApprovalMode } from '@common/constants/ChangeRequestApprovalConstants';
import { useSelector } from 'react-redux';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import { ChangeRequestRoleUser } from '@core/schema/ChangeRequestRoleUser';
import { useChangeRequestApprovalContext } from './approval/ChangeRequestApprovalContext';

export const RenderApproval: React.FC = () => {
  const { cabGroupUserIdx, changeFlowNodeId, control, handleOpenApprovalResults, mode, modeApproval, refetchChangeRequestRoles, roleIdx } =
    useChangeRequestApprovalContext();
  const { getValues } = useFormContext<ChangeRequestRoleList>();

  const roleUserPath = `roles.${roleIdx}.workflows.${0}.groups.${0}.users.${cabGroupUserIdx}` as const;
  const changeRequestRoleUser = getValues(`${roleUserPath}`) as ChangeRequestRoleUser;
  const approvalRequestId = changeRequestRoleUser.changeRequestApproval?.id;
  const changeRequestApproval = changeRequestRoleUser.changeRequestApproval || WAITING_APPROVAL;
  const userInfo = useSelector(getCurrentUser).userInfo;

  const changeApprovalStatusConfig = statusConfig[changeRequestApproval.overAllStatus];
  const [currentApproval, setCurrentApproval] = useState<ChangeRequestTransitionModel>({
    changeRequestApprovalId: 0,
    status: ChangeRequestApprovalStatusEnum.Enum.TOBE_SEND,
    approvalComment: '',
  });
  const [openedApprovalModal, { close: closeApprovalModal, open: openApprovalModal }] = useDisclosure(false);
  const { mutate: approvalRequest } = useMutate(ChangeRequestApi.processChangeRequestApproverTransition, {
    successNotification: `Process successfully`,
    errorNotification: (data) => ({ message: data.message }),
    onSuccess: () => {
      closeApprovalModal();
      refetchChangeRequestRoles();
    },
  });
  const handleApproval = () => {
    approvalRequest(currentApproval);
  };

  const isNotActiveAction =
    !changeRequestApproval.id ||
    changeRequestApproval.overAllStatus !== ChangeRequestApprovalStatusEnum.Enum.PENDING_APPROVAL ||
    changeRequestRoleUser.username !== userInfo?.userName;
  const [comment, setComment] = useState('');
  const roleName = getValues(`roles.${roleIdx}.changeFlowNode.name`);
  return (
    <>
      <KanbanTable
        title=''
        data={[changeRequestApproval]}
        columns={[
          {
            name: '',
            title: roleName,
            customRender: (_, __) => (
              <ApprovalUserSelect
                workflowIdx={0}
                roleIdx={roleIdx}
                control={control}
                mode={mode}
                cabGroupIdx={0}
                cabGroupUserIdx={0}
                changeFlowNodeId={changeFlowNodeId}
                modeApproval={modeApproval}
              />
            ),
          },
          {
            name: 'status',
            title: 'Status',
            customRender: () => (
              <Badge
                color={changeApprovalStatusConfig.color}
                radius='xs'
                style={{ textTransform: 'none' }}
                leftSection={changeApprovalStatusConfig.icon}>
                {changeApprovalStatusConfig.label}
              </Badge>
            ),
          },
          {
            name: 'sentOn',
            title: 'Sent On',
            customRender: () => (
              <KanbanText>{changeRequestApproval.createdDate ? new Date(changeRequestApproval.createdDate).toLocaleDateString() : '-'}</KanbanText>
            ),
          },
          {
            name: 'actOn',
            title: 'Act On',
            customRender: () => (
              <KanbanText>
                {changeRequestApproval.lastApprovalDate ? new Date(changeRequestApproval.lastApprovalDate).toLocaleDateString() : '-'}
              </KanbanText>
            ),
          },
          {
            name: 'comment',
            title: 'Comment',
            width: '10%',
            customRender: () => (
              <KanbanIconButton size='sx' variant='subtle' onClick={handleOpenApprovalResults}>
                <IconMessage size={20} />
              </KanbanIconButton>
            ),
          },
          {
            name: 'actions',
            title: 'Actions',
            width: '10%',
            customRender: () =>
              modeApproval === ChangeRequestApprovalMode.APPROVAL ? (
                <KanbanSelect
                  mb={0}
                  placeholder='Actions'
                  data={APPROVAL_ACTION_OPTIONS}
                  disabled={isNotActiveAction}
                  onChange={(status) => {
                    if (status === ChangeRequestApprovalStatusEnum.Enum.ACCEPT || status === ChangeRequestApprovalStatusEnum.Enum.REJECT) {
                      setCurrentApproval({
                        changeRequestApprovalId: Number(approvalRequestId),
                        status: status,
                      });
                      openApprovalModal();
                    }
                  }}
                />
              ) : null,
          },
        ]}
        pagination={{ enable: false }}
        topBar={{ enable: false }}
        bottomBar={{ enable: false }}
      />
      <KanbanModal
        title='Comments'
        size={'80%'}
        h={'80%'}
        opened={openedApprovalModal}
        actions={<KanbanButton onClick={handleApproval}>Confirm</KanbanButton>}
        onClose={closeApprovalModal}>
        <KanbanTextarea
          h={'50%'}
          value={comment}
          onChange={(e) => {
            const val = e.currentTarget.value;
            setComment(val);
            setCurrentApproval((prev) => ({
              ...prev,
              approvalComment: val,
            }));
          }}
          placeholder='Enter your comments here...'
        />
      </KanbanModal>
    </>
  );
};
