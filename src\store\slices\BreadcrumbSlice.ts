import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { type RootStoreType } from '@store';

interface BreadcrumbState {
  entityName?: string;
}

const initialState: BreadcrumbState = {
  entityName: undefined,
};

export const breadcrumbSlice = createSlice({
  name: 'breadcrumb',
  initialState,
  reducers: {
    setEntityName(state, action: PayloadAction<string | undefined>) {
      state.entityName = action.payload;
    },
  },
});

export const { setEntityName } = breadcrumbSlice.actions;
export const getBreadcrumbEntityName = (store: RootStoreType) => store.breadcrumbEntityName;
