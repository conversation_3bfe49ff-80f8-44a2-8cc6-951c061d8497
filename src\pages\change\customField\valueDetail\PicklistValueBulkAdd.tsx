import React, { useState } from 'react';
import { CustomFieldModel, PICK_LIST_OPTION_LENGTH, PicklistOption } from '@models/CustomField';
import { UseFormReturn } from 'react-hook-form';
import AmtModal from '@components/AmtModal';
import { KanbanButton, KanbanTextarea } from 'kanban-design-system';

type BulkAddProps = {
  form: UseFormReturn<CustomFieldModel>;
  tableData: PicklistOption[];
  setTableData: React.Dispatch<React.SetStateAction<PicklistOption[]>>;
  opened: boolean;
  onClose: () => void;
};

const BulkAdd: React.FC<BulkAddProps> = ({ form, onClose, opened, setTableData, tableData }) => {
  const [bulkInput, setBulkInput] = useState('');

  const handleBulkAdd = () => {
    const trimmedLines = bulkInput
      .split('\n')
      .map((line) => line.trim())
      .filter(Boolean);

    const currentValues = new Set(tableData.map((item) => item.value.trim()));
    const uniqueNewLines = trimmedLines.filter((line) => !currentValues.has(line));

    const currentLength = tableData.length;
    const availableSlots = Math.max(0, PICK_LIST_OPTION_LENGTH - currentLength);

    if (availableSlots === 0 || uniqueNewLines.length === 0) {
      setBulkInput('');
      onClose();
      return;
    }

    const newOptions: PicklistOption[] = uniqueNewLines.slice(0, availableSlots).map((value, idx) => ({
      value,
      isDefault: false,
      position: currentLength + idx,
    }));

    const updated = [...tableData, ...newOptions].map((item, idx) => ({
      ...item,
      position: idx,
    }));

    setTableData(updated);
    form.setValue('picklistOptions', updated);
    setBulkInput('');
    onClose();
  };

  return (
    <AmtModal
      size='50%'
      opened={opened}
      onClose={onClose}
      title='Bulk Add'
      actions={
        <KanbanButton variant='filled' onClick={handleBulkAdd}>
          Save
        </KanbanButton>
      }>
      <KanbanTextarea minRows={5} value={bulkInput} onChange={(e) => setBulkInput(e.currentTarget.value)} />
    </AmtModal>
  );
};

export default BulkAdd;
