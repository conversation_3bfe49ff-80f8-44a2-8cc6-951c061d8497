import React from 'react';
import { Box, Card, Flex, Paper, Stack, UnstyledButton } from '@mantine/core';
import { KanbanText, KanbanTitle } from 'kanban-design-system';
import { IconChevronLeft } from '@tabler/icons-react';

interface Props {
  toggle?: boolean;
}

const TodoSidebar: React.FC<Props> = () => {
  return (
    <Paper p='md' withBorder shadow='xs' pos={'sticky'} top={16}>
      <Flex display='flex' justify='space-between' align='center' mb='sm'>
        <KanbanTitle order={5}>To-do list</KanbanTitle>
        <UnstyledButton title='Hide To-do'>
          <IconChevronLeft size={18} />
        </UnstyledButton>
      </Flex>

      <Card withBorder radius='md' mb='sm'>
        <Box bg='gray.1' px='md' py={4} mb='xs'>
          <KanbanText size='md' fw={500}>
            Document
          </KanbanText>
        </Box>

        <Stack mt='xs' gap='xs' mah={600} style={{ overflowY: 'auto' }}></Stack>
      </Card>
    </Paper>
  );
};

export default TodoSidebar;
