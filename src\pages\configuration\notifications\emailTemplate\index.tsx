import { Box, Flex } from '@mantine/core';
import customStyled from '@resources/styles/Common.module.scss';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import {
  ColumnType,
  getDefaultTableAffected,
  KanbanButton,
  KanbanTable,
  KanbanTableProps,
  KanbanTitle,
  renderDateTime,
  TableAffactedSafeType,
} from 'kanban-design-system';
import { IconPlus, IconTrash } from '@tabler/icons-react';
import { buildDetailUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import { EntityAction } from '@common/constants/EntityActionConstants';
import { FilterTextInput } from '@components/AmtFilterTextInput';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSavedColumns } from '@core/hooks/useSavedColumns';
import { LocalStorageKey } from '@common/constants/LocalStorageKeyConstants';
import { useDisclosure } from '@mantine/hooks';
import { deleteConfirm } from '@components/utils/ConfirmMessageUtils';
import useMutate from '@core/hooks/useMutate';
import useFetch from '@core/hooks/useFetch';
import { NotificationsEmailTemplateApi } from '@api/NotificationsEmailTemplateApi';
import equal from 'fast-deep-equal';
import CommonActionColumn from '@components/utils/CommonActionColumn';

import AmtModal from '@components/AmtModal';
import useSavedFilterCycle from '@core/hooks/useSavedFilterCycle';
import { parseFilterWithDate } from '@common/utils/DateUtils';
import { DateRangeFilter, dateRangeKeys } from '@components/AmtDateRangeInput';
import { ShortRichContent } from '@pages/configuration/notifications/emailTemplate/components/ShortRichContent';
import { DocumentNode, EmailTemplateDetailModel } from '@models/EmailTemplateModel';
import { AmtShortTextLink } from '@components/AmtShortTextContent';
import { EMAIL_TEMPLATE_TYPE_LABEL } from '@common/constants/EmailTemplateConstants';

export type EmailTemplatePageFilter = DateRangeFilter & {
  emailTemplateName?: string;
};

const initOrUpdatedFilterPayloads = (prev?: TableAffactedSafeType<EmailTemplateDetailModel>, initFilters?: EmailTemplatePageFilter) => {
  const filter = { ...(prev || getDefaultTableAffected()) };
  if (initFilters?.emailTemplateName !== undefined) {
    filter.search = initFilters.emailTemplateName;
  }
  return filter;
};

function getTemplateTypeLabel(type: unknown): string {
  if (typeof type === 'string' && type in EMAIL_TEMPLATE_TYPE_LABEL) {
    return EMAIL_TEMPLATE_TYPE_LABEL[type as keyof typeof EMAIL_TEMPLATE_TYPE_LABEL];
  }
  return '';
}

const EmailTemplatePage = () => {
  const navigate = useNavigate();
  const [totalRecords, setTotalRecords] = useState(0);
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType>();
  const [inputFilters, setInputFilters] = useState<EmailTemplatePageFilter>({} as EmailTemplatePageFilter);
  const [savedFilters, setSavedFilters] = useSavedFilterCycle({
    key: LocalStorageKey.SEARCH_VALUE_EMAIL_TEMPLATE_PAGE,
    deserialize: (str) => parseFilterWithDate<EmailTemplatePageFilter>(str, dateRangeKeys),
    setInputFilters,
  });
  const [emailTemplates, setEmailTemplates] = useState<EmailTemplateDetailModel[]>([]);
  const [selectedForDelete, setSelectedForDelete] = useState<EmailTemplateDetailModel[]>([]);
  const [confirmDeleteModalOpened, confirmDeleteModal] = useDisclosure(false);
  const [orderColumn, setOrderColumn] = useState<string[]>();

  // Fetch data
  const { data: emailTemplateResponses, refetch } = useFetch(
    NotificationsEmailTemplateApi.findAll(initOrUpdatedFilterPayloads(tableAffected, savedFilters)),
    {
      enabled: !!tableAffected,
    },
  );

  //Update data state when fetch response changes
  useEffect(() => {
    if (emailTemplateResponses?.data?.content) {
      setEmailTemplates(emailTemplateResponses.data.content);
      setTotalRecords(emailTemplateResponses.data.totalElements);
    }
  }, [emailTemplateResponses?.data]);

  // Table columns
  const columns = useMemo<ColumnType<EmailTemplateDetailModel>[]>(
    () => [
      {
        name: 'name',
        title: 'Email Template Name',
        width: '30%',
        customRender: (_data, rowData) => {
          return (
            <AmtShortTextLink
              routePath={ROUTE_PATH.CONFIGURATION_NOTIFICATIONS_EMAIL_TEMPLATE_DETAIL}
              entityId={rowData.id}
              data={rowData.name}
              disableShorten
              fw={500}
            />
          );
        },
      },
      {
        name: 'subject',
        title: 'Subject',
        width: '30%',
        customRender: (data: string | DocumentNode) => {
          if (typeof data === 'string') {
            return data;
          } else {
            return <ShortRichContent content={data} />;
          }
        },
      },
      {
        name: 'type',
        title: 'Type',
        with: '10%',
        customRender: getTemplateTypeLabel,
      },
      { name: 'modifiedBy', title: 'Modified By' },
      { name: 'modifiedDate', title: 'Modified Date', customRender: renderDateTime },
      { name: 'createdBy', title: 'Created By', hidden: true },
      { name: 'createdDate', title: 'Created Date', customRender: renderDateTime, hidden: true },
    ],
    [],
  );

  // Saved column layout
  const savedColumnDisplay = useSavedColumns<EmailTemplateDetailModel>(
    LocalStorageKey.COLUMN_DISPLAY_NOTIFICATIONS_EMAIL_TEMPLATE,
    columns,
    orderColumn,
  );

  // Multi-delete mutation
  const { mutate: deleteMultiMutate } = useMutate(NotificationsEmailTemplateApi.deleteEmailTemplateByIds, {
    successNotification: 'Delete email template(s) successfully',
    onSuccess: () => {
      refetch();
    },
  });

  // Handle delete confirm
  const handleDeleteMulti = useCallback(() => {
    if (selectedForDelete.length === 0) {
      return;
    }
    deleteMultiMutate(selectedForDelete.map((it) => it.id));
    setSelectedForDelete([]);
    confirmDeleteModal.close();
  }, [selectedForDelete, deleteMultiMutate, confirmDeleteModal]);

  // Memo tableProps to avoid unnecessary re-renders
  const tableProps: KanbanTableProps<EmailTemplateDetailModel> = useMemo(
    () => ({
      title: '',
      columns: savedColumnDisplay,
      data: emailTemplates,
      customRowProps: (data) => ({
        onDoubleClick: () => navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_NOTIFICATIONS_EMAIL_TEMPLATE_DETAIL, data.id, EntityAction.EDIT)),
      }),
      columnOrderable: {
        onOrder: (data) => setOrderColumn(data.map((it) => it.name)),
      },
      pagination: { enable: true },
      serverside: {
        totalRows: totalRecords,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffected, dataSet)) {
            setTableAffected(dataSet);
          }
        },
      },
      selectableRows: {
        enable: true,
        customAction: () => (
          <KanbanButton leftSection={<IconTrash />} size='xs' bg='red' onClick={confirmDeleteModal.open}>
            Delete(s)
          </KanbanButton>
        ),
        crossPageSelected: {
          rowKey: 'id',
          setSelectedRows: setSelectedForDelete,
          selectedRows: selectedForDelete,
        },
      },
      actions: {
        customAction: (data) => (
          <CommonActionColumn
            id={data.id}
            onDelete={NotificationsEmailTemplateApi.deleteEmailTemplateById}
            onEdit={() => navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_NOTIFICATIONS_EMAIL_TEMPLATE_DETAIL, data.id, EntityAction.EDIT))}
            nameDelete={data.name}
            refetch={refetch}
            successNotification='Email Template deleted successfully'
          />
        ),
      },
      showNumericalOrderColumn: true,
    }),
    [savedColumnDisplay, emailTemplates, totalRecords, selectedForDelete, navigate, tableAffected, confirmDeleteModal.open, refetch],
  );

  // Search input handlers
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputFilters((prev) => ({ ...prev, emailTemplateName: e.target.value }));
  };

  const handleTriggerSearch = useCallback(() => {
    setSavedFilters((prev) => ({ ...prev, ...inputFilters }));
    setTableAffected((prev) => {
      return initOrUpdatedFilterPayloads(prev, inputFilters);
    });
  }, [inputFilters, setSavedFilters]);

  const handleClearFilter = useCallback(
    (key: keyof EmailTemplatePageFilter) => {
      const toUpdate = { [key]: '' };
      setInputFilters((prev) => ({ ...prev, ...toUpdate }));
      setSavedFilters((prev) => ({ ...prev, ...toUpdate }));
    },
    [setSavedFilters],
  );

  return (
    <Box className={customStyled.tableCs}>
      <HeaderTitleComponent
        title=''
        rightSection={
          <Flex>
            <KanbanButton
              leftSection={<IconPlus />}
              size='xs'
              onClick={() => navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_NOTIFICATIONS_EMAIL_TEMPLATE_DETAIL, 0, EntityAction.CREATE))}>
              Add Email Template
            </KanbanButton>
          </Flex>
        }
        leftSection={<KanbanTitle fz='h4'>Email Template List</KanbanTitle>}
      />
      <Flex dir='row' gap='xs' mb='xs'>
        <FilterTextInput
          placeholder='Email Template Name'
          value={inputFilters.emailTemplateName ?? ''}
          onChange={(e) => handleSearchChange(e)}
          onBlur={() => handleTriggerSearch()}
          onClear={() => handleClearFilter('emailTemplateName')}
        />
      </Flex>
      <Box className={customStyled.elementBorder}>
        <KanbanTable {...tableProps} title='' />
      </Box>
      <AmtModal
        opened={confirmDeleteModalOpened}
        title='Confirm delete email template(s)'
        onClose={confirmDeleteModal.close}
        actions={
          <KanbanButton variant='filled' onClick={handleDeleteMulti}>
            Confirm
          </KanbanButton>
        }>
        {deleteConfirm(selectedForDelete.map((it) => it.name)).children}
      </AmtModal>
    </Box>
  );
};

EmailTemplatePage.whyDidYouRender = true;
export default EmailTemplatePage;
