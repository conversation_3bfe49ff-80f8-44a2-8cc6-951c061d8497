//common models

import { AdvancedFilterMappingType, SortOrder } from 'kanban-design-system';

export type EntityModelBase = {
  id: number;
  createdBy?: string;
  createdDate?: Date;
  modifiedBy?: string;
  modifiedDate?: Date;
};

export type QueryRequestModel<T> = Omit<PaginationRequestModel<T>, 'page' | 'size'>;

export type PaginationRequestModel<T = unknown> = {
  page: number;
  size: number;
  search?: string;
  sortBy?: keyof T;
  sortOrder?: SortOrder;
  fromDate?: string;
  toDate?: string;
  advancedFilterMapping?: AdvancedFilterMappingType<T>;
};

export type PaginationResponseModel<T = unknown> = {
  content: T[];
  pageable: {
    sort: {
      empty: boolean;
      sorted: boolean;
      unsorted: boolean;
    };
    offset: number;
    pageNumber: number;
    pageSize: number;
    unpaged: boolean;
    paged: boolean;
  };
  last: boolean;
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  sort: {
    empty: boolean;
    sorted: boolean;
    unsorted: boolean;
  };
  numberOfElements: number;
  first: boolean;
  empty: boolean;
};
