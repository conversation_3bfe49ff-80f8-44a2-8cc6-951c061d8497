import { Tooltip, Flex } from '@mantine/core';
import { IconEdit, IconTrash } from '@tabler/icons-react';
import React from 'react';
import { KanbanIconButton } from 'kanban-design-system';
import { deleteConfirm } from '@components/utils/ConfirmMessageUtils';
import useMutate from '@core/hooks/useMutate';
import { RequestConfig } from '@core/api';
import { ResponseData } from '@core/schema/Common';

type CommonActionColumnProps = {
  id: number;
  nameDelete: string;
  onDelete: (id: number) => RequestConfig<ResponseData<void>>; // or any type that returns a Promise
  onEdit?: () => void;
  refetch?: () => void;
  editTooltip?: string;
  deleteTooltip?: string;
  successNotification?: string;
};

const CommonActionColumn = ({
  deleteTooltip = 'Delete',
  editTooltip = 'Edit',
  id,
  nameDelete,
  onDelete,
  onEdit,
  refetch,
  successNotification = 'Field deleted successfully',
}: CommonActionColumnProps) => {
  const { mutate: deleteMutate } = useMutate(onDelete, {
    successNotification: successNotification,
    confirm: deleteConfirm([nameDelete]),
    onSuccess: () => {
      if (refetch) {
        refetch();
      }
    },
  });

  return (
    <Flex gap='xs' align='center'>
      {onEdit && (
        <Tooltip label={editTooltip}>
          <KanbanIconButton variant='transparent' size='sm' onClick={onEdit}>
            <IconEdit />
          </KanbanIconButton>
        </Tooltip>
      )}

      <Tooltip label={deleteTooltip}>
        <KanbanIconButton variant='transparent' color='red' size='sm' onClick={() => deleteMutate(id)}>
          <IconTrash />
        </KanbanIconButton>
      </Tooltip>
    </Flex>
  );
};

export default CommonActionColumn;
