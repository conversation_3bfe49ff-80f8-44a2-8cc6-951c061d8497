export enum QueryBuilderOperatorEnum {
  IS = 'IS',
  IS_NOT = 'IS_NOT',

  IS_ONE_OF = 'IS_ONE_OF',
  IS_NOT_ONE_OF = 'IS_NOT_ONE_OF',

  CONTAINS = 'CONTAINS',
  DOES_NOT_CONTAIN = 'DOES_NOT_CONTAIN',

  BEGINS_WITH = 'BEGINS_WITH',
  ENDS_WITH = 'ENDS_WITH',

  GREATER_THAN = 'GREATER_THAN',
  GREATER_THAN_OR_EQUAL = 'GREATER_THAN_OR_EQUAL',

  LESS_THAN = 'LESS_THAN',
  LESS_THAN_OR_EQUAL = 'LESS_THAN_OR_EQUAL',

  IS_NULL = 'IS_EMPTY ',
  IS_NOT_NULL = 'IS_NOT_EMPTY',

  IS_NULL_OR_EMPTY = 'IS_NULL_OR_EMPTY ',
  IS_NOT_NULL_OR_NOT_EMPTY = 'IS_NOT_NULL_OR_NOT_EMPTY',
}
export const OPERATOR_LABELS: Record<QueryBuilderOperatorEnum, string> = {
  [QueryBuilderOperatorEnum.IS]: 'is',
  [QueryBuilderOperatorEnum.IS_NOT]: 'is not',
  [QueryBuilderOperatorEnum.IS_ONE_OF]: 'is one of',
  [QueryBuilderOperatorEnum.IS_NOT_ONE_OF]: 'is not one of',
  [QueryBuilderOperatorEnum.CONTAINS]: 'contains',
  [QueryBuilderOperatorEnum.DOES_NOT_CONTAIN]: 'does not contain',
  [QueryBuilderOperatorEnum.BEGINS_WITH]: 'begin with',
  [QueryBuilderOperatorEnum.ENDS_WITH]: 'end with',
  [QueryBuilderOperatorEnum.GREATER_THAN]: 'greater than',
  [QueryBuilderOperatorEnum.GREATER_THAN_OR_EQUAL]: 'greater than or equal',
  [QueryBuilderOperatorEnum.LESS_THAN]: 'less than',
  [QueryBuilderOperatorEnum.LESS_THAN_OR_EQUAL]: 'less than or equal',
  [QueryBuilderOperatorEnum.IS_NULL]: 'is null',
  [QueryBuilderOperatorEnum.IS_NOT_NULL]: 'is not null',
  [QueryBuilderOperatorEnum.IS_NULL_OR_EMPTY]: 'is null or empty',
  [QueryBuilderOperatorEnum.IS_NOT_NULL_OR_NOT_EMPTY]: 'is not null or not empty',
} as const;
