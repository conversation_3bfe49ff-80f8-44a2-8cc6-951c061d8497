import { Edge, Node, useNodesState, useEdgesState } from '@xyflow/react';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { WorkflowRightSideBar } from './WorkflowRightSideBar';
import { Box, Card, Flex, Modal, Group } from '@mantine/core';
import { KanbanButton, KanbanIconButton, KanbanInput, KanbanText, KanbanTitle } from 'kanban-design-system';
import useFetch from '@core/hooks/useFetch';
import { ChangeApplicationTypeEnum } from '@common/constants/ChangeApplicationConstants';
import { ChangeWorkflowNode } from '@core/schema/ChangeWorkflowNode';
import { CustomNodeData } from '@pages/change/request/workflow/reactFlow/CustomNode';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { IconArrowLeft, IconEdit, IconDeviceFloppy } from '@tabler/icons-react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { notifications } from '@mantine/notifications';
import stylesCss from './WorkflowDetailPage.module.scss';
import useMutate from '@core/hooks/useMutate';
import { ChangeRequestApi } from '@api/ChangeRequestApi';
import { ChangeRequestWorkflowNodeTypeEnum } from '@common/constants/ChangeWorkflowNodeType';
import { START_NODE_ICON, START_NODE_ID, START_NODE_LABLE } from '@common/constants/WorkflowConstants';
import { COMMON_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { buildChangeDetailUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import { EntityAction } from '@common/constants/EntityActionConstants';
import { useCheckPermissons } from '@core/hooks/useCheckPermissons';
import { changePermissionConfigs } from '@pages/change/changeList';
import WorkflowReactFlow from './reactFlow/WorkflowReactFlow';
import jenkins from 'access/icons/Jenkins.svg';
import wla from 'access/icons/wla.svg';
import playerPlay from 'access/icons/player-play.svg';
import { ChangeRquestTabsEnum } from '@common/constants/ChangeRequestConstants';
type WorkFlowNodeDetailPageProps = {
  id?: number;
};

export type ExtendedNode = Node & {
  changeNodeId?: number;
};

type WorkflowJsonData = {
  nodes: ExtendedNode[];
  edges: Edge[];
};

export type WorkflowData = {
  id?: number;
  name: string;
  description?: string;
  workflowData: string;
  nodes: ExtendedNode[];
  edges: Edge[];
  changeId: number;
  createdDate?: string;
  modifiedDate?: string;
};

export const getNodeIcon = (application: ChangeApplicationTypeEnum | undefined) => {
  switch (application) {
    case ChangeApplicationTypeEnum.JENKINS:
      return jenkins;
    case ChangeApplicationTypeEnum.WLA:
      return wla;
    default:
      return playerPlay;
  }
};

const generateWorkflowName = () => {
  const now = new Date();
  const dateStr = now.toLocaleDateString('vi-VN');
  const timeStr = now.toLocaleTimeString('vi-VN', { hour12: false });

  return `Workflow ${dateStr} ${timeStr}`;
};

export const WorkflowDetailPage: React.FC<WorkFlowNodeDetailPageProps> = () => {
  const id = Number(useParams().id);
  const changeId = Number(useParams().changeId);
  const navigate = useNavigate();
  const [nodes, setNodes, onNodesChange] = useNodesState<Node>([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState<Edge>([]);
  const [selectedNode, setSelectedNode] = useState<Node | undefined>(undefined);
  const flowWrapperRef = useRef<HTMLDivElement>(null);
  const [listNodeOpened, setlistNodeOpened] = useState(false);
  const canEditChange = useCheckPermissons([changePermissionConfigs.edit]);
  const [searchParams] = useSearchParams();
  const actionParam = searchParams.get('action');
  const isViewMode = actionParam === EntityAction.VIEW;

  const { mutate: saveWorkflowMutate } = useMutate(ChangeRequestApi.saveWorkflow, {
    successNotification: 'Workflow saved successfully',
    onSuccess: () => {
      navigate(buildChangeDetailUrl(ROUTE_PATH.CHANGE_REQUEST_DETAIL, 0, changeId, EntityAction.VIEW, ChangeRquestTabsEnum.WORKFLOW));
    },
  });

  const { data: workflowDetail } = useFetch(ChangeRequestApi.findDetailWorkflow(changeId, id), {
    enabled: !!id,
  });

  // mapping  workflow from server
  useEffect(() => {
    if (workflowDetail?.data) {
      setWorkflowData((prev) => ({ ...prev, name: workflowDetail?.data?.name || '' }));
      try {
        const workflow: WorkflowJsonData = JSON.parse(workflowDetail?.data?.workflowData || '');
        setNodes(workflow.nodes);
        setEdges(workflow.edges);
      } catch (e) {
        /* empty */
      }
    }
  }, [setEdges, setNodes, workflowDetail?.data]);

  // Workflow data state
  const [workflowData, setWorkflowData] = useState<WorkflowData>({
    id: id,
    name: generateWorkflowName(),
    description: '',
    changeId: changeId,
    workflowData: '{}',
    nodes: [],
    edges: [],
  });

  // Edit name modal state
  const [isEditNameModalOpen, setIsEditNameModalOpen] = useState(false);
  const [editingName, setEditingName] = useState('');

  /**
   * Handle open edit name modal
   */
  const handleOpenEditNameModal = useCallback(() => {
    setEditingName(workflowData.name);
    setIsEditNameModalOpen(true);
  }, [workflowData.name]);

  /**
   * Handle close edit name modal
   */
  const handleCloseEditNameModal = useCallback(() => {
    setIsEditNameModalOpen(false);
    setEditingName('');
  }, []);

  /**
   * Handle save workflow name
   */
  const handleSaveWorkflowName = useCallback(() => {
    if (editingName.trim()) {
      setWorkflowData((prev) => ({
        ...prev,
        name: editingName.trim(),
        modifiedDate: new Date().toISOString(),
      }));
      setIsEditNameModalOpen(false);
      notifications.show({
        title: 'Success',
        message: 'Workflow name updated successfully',
        color: 'green',
      });
    }
  }, [editingName]);

  /**
   * Handle node click to show settings panel
   */
  const handleNodeDoubleClick = useCallback((event: React.MouseEvent, node: Node) => {
    event.stopPropagation();

    // Skip sticky notes and start node
    if (node.type !== ChangeRequestWorkflowNodeTypeEnum.CUSTOM_NODE) {
      return;
    }

    setSelectedNode(node);
  }, []);

  /**
   * Handle edge type update (TRUE/FALSE/DEFAULT)
   */
  const handleEdgeTypeUpdate = useCallback(
    (edgeId: string, type: string) => {
      setEdges((prevEdges) =>
        prevEdges.map((edge) => {
          if (edge.id === edgeId) {
            return {
              ...edge,
              data: {
                ...edge.data,
                action: type,
              },
            };
          }
          return edge;
        }),
      );
    },
    [setEdges],
  );

  /**
   * method create node
   */
  const handleNodeCreate = useCallback(
    (nodeData: ChangeWorkflowNode) => {
      if (!flowWrapperRef.current) {
        return;
      }

      // Calculate position for the new node
      const position = {
        x: Math.random() * 300 + 100,
        y: Math.random() * 200 + 100,
      };
      const nodeId: string = `${nodeData.nodeId}_${Math.random()}`;
      const newNode: ExtendedNode = {
        id: nodeId,
        changeNodeId: nodeData.id,
        type: nodeData.nodeId === START_NODE_ID ? ChangeRequestWorkflowNodeTypeEnum.START_NODE : ChangeRequestWorkflowNodeTypeEnum.CUSTOM_NODE,
        position,
        data: {
          label: `${nodeData.nodeName}`,
          subtitle: ``,
          icon: getNodeIcon(nodeData.application),
          parameters: {},
          settings: {},
          application: nodeData.application,
        },
      };

      const startNode: Node = {
        id: START_NODE_ID,
        type: ChangeRequestWorkflowNodeTypeEnum.START_NODE,
        position: { x: position.x - 260, y: position.y },
        data: {
          label: START_NODE_LABLE,
          icon: START_NODE_ICON,
        },
      };

      // Update nodes state
      if (nodes.find((node) => node.type === ChangeRequestWorkflowNodeTypeEnum.START_NODE) || !nodeData.id) {
        setNodes((prev) => [...prev, newNode]);
      } else {
        setNodes((prev) => [...prev, newNode, startNode]);
        // Create an edge connecting the startNode to the newNode
        const newEdge: Edge = {
          id: `edge-start-${nodeData.nodeId}`,
          source: START_NODE_ID,
          target: `${newNode.id}`,
          type: 'animated',
          animated: false,
          data: {
            onDelete: (edgeId: string) => {
              setEdges((eds) => eds.filter((e) => e.id !== edgeId));
            },
            onTypeUpdate: handleEdgeTypeUpdate,
          },
        };

        setEdges((prev) => [...prev, newEdge]);
      }

      // If a node is currently selected, connect the new node to the selected node
      const nodeSelectedInDiragram: Node | undefined = nodes.find((node) => node.selected);

      if (nodeData && nodeSelectedInDiragram && `${nodeSelectedInDiragram.type}` === `${ChangeRequestWorkflowNodeTypeEnum.CUSTOM_NODE}`) {
        const connectionEdge: Edge = {
          id: `edge-${nodeSelectedInDiragram.id}-${nodeData.nodeId}`,
          source: `${nodeSelectedInDiragram.id}`,
          target: nodeId,
          type: 'animated',
          animated: false,
          data: {
            onDelete: (edgeId: string) => {
              setEdges((eds) => eds.filter((e) => e.id !== edgeId));
            },
            onTypeUpdate: handleEdgeTypeUpdate,
          },
        };

        setEdges((prev) => [...prev, connectionEdge]);
      }

      // Show settings panel for the new node
      if (newNode.type === ChangeRequestWorkflowNodeTypeEnum.CUSTOM_NODE) {
        setSelectedNode(newNode);
      }
    },
    [nodes, setNodes, handleEdgeTypeUpdate, setEdges],
  );

  /**
   * save param and setting of node
   */
  const handleNodeSave = useCallback(
    (nodeId: string, data?: CustomNodeData) => {
      setNodes((prev) =>
        prev.map((node) => {
          if (node.id === nodeId) {
            const nodeChange = {
              ...node,
              data: {
                ...node.data,
                parameters: data?.parameters,
                settings: data?.settings,
              },
            };
            return nodeChange;
          }
          return node;
        }),
      );

      // Reset selected node
      setSelectedNode(undefined);
    },
    [setNodes],
  );

  const handleSettingsClose = useCallback(() => {
    setSelectedNode(undefined);
  }, []);

  /**
   * Save workflow to backend
   */
  const handleSaveWorkflow = useCallback(() => {
    const workflowToSave: WorkflowData = {
      ...workflowData,
      nodes: nodes,
      edges: edges,
      workflowData: JSON.stringify({
        nodes: nodes,
        edges: edges,
      }),
    };

    saveWorkflowMutate(workflowToSave);
  }, [workflowData, nodes, edges, saveWorkflowMutate]);

  return (
    <Box className={stylesCss.pageContainer}>
      {/* Edit Name Modal */}
      <Modal opened={isEditNameModalOpen} onClose={handleCloseEditNameModal} title='Edit Workflow Name' size='md'>
        <KanbanInput
          label='Workflow Name'
          placeholder='Enter workflow name'
          value={editingName}
          onChange={(event) => setEditingName(event.currentTarget.value)}
          data-autofocus
          mb='md'
          maxLength={COMMON_MAX_LENGTH}
        />
        <Group justify='flex-end'>
          <KanbanButton variant='outline' onClick={handleCloseEditNameModal}>
            Cancel
          </KanbanButton>
          <KanbanButton onClick={handleSaveWorkflowName} disabled={!editingName.trim() || !canEditChange || isViewMode}>
            Save
          </KanbanButton>
        </Group>
      </Modal>

      <HeaderTitleComponent
        title=''
        rightSection={
          <Flex direction='row' align='center' gap='xs'>
            <KanbanButton
              size='xs'
              variant='light'
              onClick={() =>
                navigate(buildChangeDetailUrl(ROUTE_PATH.CHANGE_REQUEST_DETAIL, 0, changeId, EntityAction.EDIT, ChangeRquestTabsEnum.WORKFLOW))
              }>
              Cancel
            </KanbanButton>
            {isViewMode && canEditChange && (
              <KanbanButton
                size='xs'
                onClick={() => {
                  navigate(buildChangeDetailUrl(ROUTE_PATH.CHANGE_REQUEST_WORKFLOW_DETAIL, changeId, id || 0, EntityAction.EDIT));
                }}>
                Edit
              </KanbanButton>
            )}
            {!isViewMode && (
              <KanbanButton size='xs' disabled={!canEditChange} onClick={handleSaveWorkflow} leftSection={<IconDeviceFloppy size={14} />}>
                Save
              </KanbanButton>
            )}
          </Flex>
        }
        leftSection={
          <Flex direction='row' align='center' gap='xs'>
            <KanbanIconButton
              size='sm'
              variant='subtle'
              onClick={() =>
                navigate(
                  buildChangeDetailUrl(
                    ROUTE_PATH.CHANGE_REQUEST_DETAIL,
                    0,
                    changeId,
                    id ? EntityAction.EDIT : EntityAction.CREATE,
                    ChangeRquestTabsEnum.WORKFLOW,
                  ),
                )
              }>
              <IconArrowLeft />
            </KanbanIconButton>
            <KanbanTitle fz='h4'>
              {workflowDetail?.data?.id ? `${isViewMode ? '' : 'Edit'} ${workflowDetail?.data?.name}` : 'Create new Workflow'}
            </KanbanTitle>
          </Flex>
        }
      />
      <Card withBorder shadow='sm' radius='md' className={stylesCss.workflowCard}>
        <Card.Section withBorder inheritPadding className={stylesCss.cardHeader}>
          <Flex align='center' gap='xs'>
            <KanbanText fw={600} size='lg'>
              {workflowData.name}
            </KanbanText>
            {canEditChange && !isViewMode && (
              <KanbanIconButton size='xs' variant='subtle' onClick={handleOpenEditNameModal} title='Edit workflow name'>
                <IconEdit size={14} />
              </KanbanIconButton>
            )}
          </Flex>
        </Card.Section>

        <WorkflowRightSideBar
          onNodeCreate={handleNodeCreate}
          selectedNode={selectedNode}
          onNodeSave={handleNodeSave}
          onSettingsClose={handleSettingsClose}
          listNodeOpened={listNodeOpened}
          setListNodeOpened={setlistNodeOpened}
          nodes={nodes}
        />

        <div ref={flowWrapperRef} className={stylesCss.flowWrapper}>
          <WorkflowReactFlow
            nodes={nodes}
            edges={edges}
            setEdges={setEdges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onNodeDoubleClick={handleNodeDoubleClick}
            onNodeClick={() => setSelectedNode(undefined)}
            onEdgeTypeUpdate={handleEdgeTypeUpdate}
            setNodes={setNodes}
            isOnlyView={isViewMode}
          />
        </div>
      </Card>
    </Box>
  );
};

export default WorkflowDetailPage;
