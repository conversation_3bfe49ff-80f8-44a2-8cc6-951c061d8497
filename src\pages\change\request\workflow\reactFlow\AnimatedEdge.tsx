import React, { useEffect, useRef, useState, useCallback } from 'react';
import { EdgeProps, getBezierPath } from '@xyflow/react';
import stylesCss from './AnimatedEdge.module.scss';
import clsx from 'clsx';
export enum EdgeAction {
  SUCCESS = 'success',
  FAILURE = 'failure',
}

interface StaticEdgeData {
  onTypeUpdate?: (edgeId: string, type: EdgeAction) => void;
  edgeStyle?: React.CSSProperties;
  action?: EdgeAction;
}

export default function StaticEdge({ data, id, markerEnd, sourcePosition, sourceX, sourceY, targetPosition, targetX, targetY }: EdgeProps) {
  const { action = EdgeAction.SUCCESS, edgeStyle = {}, onTypeUpdate } = (data || {}) as StaticEdgeData;

  const [showControls, setShowControls] = useState(false);
  const [currentAction, setCurrentAction] = useState<EdgeAction>(action);
  const edgeRef = useRef<SVGGElement>(null);
  const controlsRef = useRef<SVGGElement>(null);

  const [edgePath, labelX, labelY] = getBezierPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
  });

  // Sync with data changes
  useEffect(() => {
    setCurrentAction(action);
  }, [action]);

  const handleActionChange = useCallback(
    (event: React.MouseEvent, newAction: EdgeAction) => {
      event.preventDefault();
      event.stopPropagation();

      setCurrentAction(newAction);

      if (onTypeUpdate) {
        onTypeUpdate(id, newAction);
      }

      // Hide controls after clicking
      setShowControls(false);
    },
    [id, onTypeUpdate],
  );

  const handleDoubleClick = useCallback(
    (event: React.MouseEvent) => {
      event.preventDefault();
      event.stopPropagation();
      setShowControls(!showControls);
    },
    [showControls],
  );

  const handleControlClick = useCallback((event: React.MouseEvent) => {
    // Prevent event bubbling to avoid closing controls
    event.stopPropagation();
  }, []);

  // Click outside to hide controls
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        controlsRef.current &&
        !controlsRef.current.contains(event.target as Node) &&
        edgeRef.current &&
        !edgeRef.current.contains(event.target as Node)
      ) {
        setShowControls(false);
      }
    };

    if (showControls) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showControls]);

  // ESC key to hide controls
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setShowControls(false);
      }
    };

    if (showControls) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [showControls]);

  const getEdgeColor = () => {
    return currentAction === EdgeAction.SUCCESS ? '#22c55e' : '#ef4444';
  };

  const getArrowColor = () => {
    return currentAction === EdgeAction.SUCCESS ? '#22c55e' : '#ef4444';
  };

  const getEdgeClassName = () => {
    return clsx('react-flow__edge-path', stylesCss.edgePath, {
      [stylesCss.successEdge]: currentAction === EdgeAction.SUCCESS,
      [stylesCss.failEdge]: currentAction === EdgeAction.FAILURE,
    });
  };

  return (
    <>
      <defs>
        <marker id={`static-arrow-${id}`} viewBox='0 0 6 6' refX='5' refY='3' markerWidth='6' markerHeight='6' orient='auto-start-reverse'>
          <path d='M 0 0 L 6 3 L 0 6 z' fill={getArrowColor()} />
        </marker>
      </defs>

      {/* Main Edge Path */}
      <g ref={edgeRef}>
        <path
          id={id}
          className={getEdgeClassName()}
          d={edgePath}
          style={{
            ...edgeStyle,
            stroke: getEdgeColor(),
          }}
          markerEnd={markerEnd || `url(#static-arrow-${id})`}
          onDoubleClick={handleDoubleClick}
        />

        {/* Invisible wider path for easier double clicking */}
        <path d={edgePath} fill='none' stroke='transparent' strokeWidth='20' onDoubleClick={handleDoubleClick} />
      </g>

      {/* Control Buttons */}
      {showControls && (
        <g ref={controlsRef} className={stylesCss.controlsGroup} onClick={handleControlClick}>
          {/* Background for controls */}
          <rect x={labelX - 75} y={labelY - 20} width={150} height={40} rx={20} className={stylesCss.controlsBackground} />

          {/* SUCCESS Button */}
          <g className={stylesCss.controlButton}>
            <rect
              x={labelX - 65}
              y={labelY - 12.5}
              width={60}
              height={25}
              rx={12.5}
              className={clsx(stylesCss.buttonBackground, stylesCss.successButtonBackground, {
                [stylesCss.activeButton]: currentAction === EdgeAction.SUCCESS,
              })}
              onClick={(e) => handleActionChange(e, EdgeAction.SUCCESS)}
            />
            <text
              x={labelX - 35}
              y={labelY + 3}
              textAnchor='middle'
              className={stylesCss.buttonText}
              onClick={(e) => handleActionChange(e, EdgeAction.SUCCESS)}>
              SUCCESS
            </text>
          </g>

          {/* FAIL Button */}
          <g className={stylesCss.controlButton}>
            <rect
              x={labelX + 5}
              y={labelY - 12.5}
              width={60}
              height={25}
              rx={12.5}
              className={clsx(stylesCss.buttonBackground, stylesCss.failButtonBackground, {
                [stylesCss.activeButton]: currentAction === EdgeAction.FAILURE,
              })}
              onClick={(e) => handleActionChange(e, EdgeAction.FAILURE)}
            />
            <text
              x={labelX + 35}
              y={labelY + 3}
              textAnchor='middle'
              className={stylesCss.buttonText}
              onClick={(e) => handleActionChange(e, EdgeAction.FAILURE)}>
              FAILURE
            </text>
          </g>
        </g>
      )}
    </>
  );
}
