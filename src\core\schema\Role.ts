import { z } from 'zod';
import { AuditSchema } from './Common';

export const RoleSchema = z
  .object({
    id: z.number().nullish(),
    name: z.string().nullish(),
    description: z.string().nullish(),
  })
  .merge(AuditSchema);

export type Role = z.infer<typeof RoleSchema>;

export const UserRoleSchema = z
  .object({
    id: z.number().nullish(),
    userName: z.string().nullish(),
    name: z.string().nullish(),
    roleId: z.number(),
    roleName: z.string(),
    userId: z.number().nullish(),
    description: z.string().nullish(),
  })
  .merge(AuditSchema);

export type UserRole = z.infer<typeof UserRoleSchema>;
