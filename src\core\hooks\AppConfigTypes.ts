import { NotificationData } from '@mantine/notifications';
import { OpenConfirmModalProps } from 'kanban-design-system';

export type EnableNotificationConfig = NotificationData & { enable?: true };
export type DisableNotificationConfig = Partial<NotificationData> & { enable: false };
export type NotificationConfig = EnableNotificationConfig | DisableNotificationConfig;

export type EnableConfirmConfig = Omit<OpenConfirmModalProps, 'onConfirm'> & { enable?: true };
export type DisableConfirmConfig = Partial<Omit<OpenConfirmModalProps, 'onConfirm'>> & { enable: false };
export type ConfirmConfig = EnableConfirmConfig | DisableConfirmConfig;

export type AppNotificationConfig<T> = NotificationConfig | ((data: T) => NotificationConfig) | string;

export type AppConfig<TData> = {
  showLoading?: boolean;
  errorNotification?: AppNotificationConfig<Error>;
  withSignal?: boolean;
  successNotification?: AppNotificationConfig<TData>;
  confirm?: ConfirmConfig;
  throwParsedError?: boolean;
};
