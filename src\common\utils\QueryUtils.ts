import { RequestConfig } from '@core/api';
import { queryClient } from '@core/configs/QueryClient';

export function getQueryKey<Response, SearchParam = unknown>(requestConfig: RequestConfig<Response, SearchParam>) {
  const { data, params, pathVariable, url } = requestConfig;
  return [url, pathVariable, params, data];
}

export function refetchRequest<Response, SearchParam = unknown>(requestConfig: RequestConfig<Response, SearchParam>) {
  return queryClient.invalidateQueries({ queryKey: getQuery<PERSON>ey(requestConfig) });
}
