import { ItCmdbApi } from '@api/ItCmdbApi';
import { ChangeApplicationTypeEnum } from '@common/constants/ChangeApplicationConstants';
import { LocalStorageKey } from '@common/constants/LocalStorageKeyConstants';
import { SaveColumnType } from '@common/constants/UserPreferenceType';
import useFetch from '@core/hooks/useFetch';
import { useSavedColumns } from '@core/hooks/useSavedColumns';
import { INPUT_REQUIRE } from '@core/message/MesageConstant';
import { AMT_TYPES, AMT_VALUES, AmtType, SEARCH_TYPES } from '@core/schema/ItCmdb';
import { Box, Flex } from '@mantine/core';
import { ChangeWorkflowNodeModel } from '@models/ChangeWorkflowNodeModel';
import { IconMinus, IconPlus } from '@tabler/icons-react';
import { ColumnType, KanbanIconButton, KanbanSelect, KanbanTable, KanbanTableProps, KanbanTitle } from 'kanban-design-system';
import React, { forwardRef, useCallback, useImperativeHandle, useMemo, useState } from 'react';
import { UseFormReturn } from 'react-hook-form';

type RowData = {
  id: number;
  operator: string | null;
  amt: string;
  ciTypeId: number | null;
  attributeId: number | null;
};

type AttributeCellProps = {
  rowData: RowData;
  onChange: (id: number | null) => void;
  error?: string;
};

const AttributeCell: React.FC<AttributeCellProps> = ({ error, onChange, rowData }) => {
  const ciTypeId = rowData.ciTypeId ?? -1;
  const { data: attributesData, isLoading } = useFetch(ItCmdbApi.getAttributes(ciTypeId), {
    enabled: ciTypeId > 0,
  });

  const attributeOptions = useMemo(
    () =>
      attributesData?.data?.map((attr) => ({
        value: attr.id.toString(),
        label: attr.name,
      })) || [],
    [attributesData?.data],
  );

  return (
    <KanbanSelect
      disabled={!rowData.ciTypeId || isLoading}
      mt='xs'
      size='xs'
      data={attributeOptions}
      searchable
      value={rowData.attributeId !== null ? rowData.attributeId.toString() : null}
      error={error}
      onChange={(value) => {
        onChange(value ? parseInt(value, 10) : null);
      }}
    />
  );
};

type CIConfigMapperProps = {
  form: UseFormReturn<ChangeWorkflowNodeModel>;
};

export type CIConfigMapperHandle = {
  validate: () => boolean;
};

const createDefaultRow = (app?: ChangeApplicationTypeEnum): RowData => {
  let amtValue: string;
  if (app === ChangeApplicationTypeEnum.JENKINS) {
    amtValue = AMT_VALUES.URL;
  } else if (app === ChangeApplicationTypeEnum.WLA) {
    amtValue = AMT_VALUES.IP;
  } else {
    amtValue = '';
  }

  return {
    id: Date.now() + Math.random(),
    operator: null,
    amt: amtValue,
    ciTypeId: null,
    attributeId: null,
  };
};

export const getAmtOptionsByApplication = (application?: ChangeApplicationTypeEnum) => {
  let filtered: AmtType[];

  switch (application) {
    case ChangeApplicationTypeEnum.JENKINS:
      filtered = AMT_TYPES.filter((item) => item === AMT_VALUES.URL);
      break;
    case ChangeApplicationTypeEnum.WLA:
      filtered = AMT_TYPES.filter((item) => item !== AMT_VALUES.URL);
      break;
    default:
      filtered = [...AMT_TYPES];
  }

  return filtered.map((item) => ({
    value: item,
    label: item,
  }));
};

const CIConfigMapper = forwardRef<CIConfigMapperHandle, CIConfigMapperProps>(({ form }, ref) => {
  const application = form.getValues('application');

  const { data: listCiType } = useFetch(ItCmdbApi.getCiTypes(), {
    enabled: true,
  });
  const ciTypeOptions = useMemo(() => {
    return (listCiType?.data || []).map((item) => ({
      value: item.id.toString(),
      label: item.name,
    }));
  }, [listCiType?.data]);
  const amtOptions = useMemo(() => getAmtOptionsByApplication(application), [application]);
  const [fieldErrors, setFieldErrors] = useState<Record<number, Partial<Record<keyof RowData, string>>>>({});
  const watchedConfig = form.watch('config') || '';
  const configData: RowData[] = useMemo(() => {
    try {
      const parsed = JSON.parse(watchedConfig);
      if (Array.isArray(parsed)) {
        return parsed;
      }
      return [createDefaultRow(application)];
    } catch {
      return [createDefaultRow(application)];
    }
  }, [application, watchedConfig]);
  const updateConfigData = useCallback(
    (newData: RowData[]) => {
      form.setValue('config', JSON.stringify(newData), { shouldValidate: true });
    },
    [form],
  );

  const handleAddRow = useCallback(
    (idToAddAfter: number) => {
      const index = configData.findIndex((r) => r.id === idToAddAfter);
      if (index === -1) {
        return;
      }
      const newRow = createDefaultRow(application);
      const newData = [...configData];
      newData.splice(index + 1, 0, newRow);
      updateConfigData(newData);
    },
    [application, configData, updateConfigData],
  );
  const handleRemoveRow = useCallback(
    (idToRemove: number) => {
      if (configData.length <= 1) {
        return;
      }
      const newData = configData.filter((r) => r.id !== idToRemove);
      updateConfigData(newData);
    },
    [configData, updateConfigData],
  );
  const handleFieldChange = (rowId: number, field: keyof RowData, value: any) => {
    const newData = configData.map((row) =>
      row.id === rowId
        ? {
            ...row,
            [field]: value,
            ...(field === 'ciTypeId' ? { attributeId: null } : {}),
          }
        : row,
    );
    updateConfigData(newData);
  };

  const validateConfigData = useCallback((): boolean => {
    const errors: Record<number, Partial<Record<keyof RowData, string>>> = {};
    const requiredFields: (keyof RowData)[] = ['ciTypeId', 'attributeId', 'operator', 'amt'];

    for (const row of configData) {
      const rowErrors: Partial<Record<keyof RowData, string>> = {};
      for (const field of requiredFields) {
        if (!row[field]) {
          rowErrors[field] = INPUT_REQUIRE;
        }
      }
      if (Object.keys(rowErrors).length > 0) {
        errors[row.id] = rowErrors;
      }
    }
    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  }, [configData]);
  const clearFieldError = (rowId: number, field: keyof RowData) => {
    setFieldErrors((prev) => {
      const updated = { ...prev };
      if (updated[rowId]?.[field]) {
        delete updated[rowId][field];
      }
      const errorsForRow = updated[rowId];
      if (errorsForRow && Object.keys(errorsForRow).length === 0) {
        delete updated[rowId];
      }
      return updated;
    });
  };

  useImperativeHandle(ref, () => ({
    validate: validateConfigData,
  }));

  const columns: ColumnType<RowData>[] = [
    {
      customRender: (_value, _rowData, rowIndex) => rowIndex > 0 && <Box>OR</Box>,
      name: 'conditionType',
      title: '',
    },
    {
      name: 'ciTypeId',
      title: 'CI Type',
      sortable: false,
      customRender: (_, rowData) => (
        <KanbanSelect
          mt='xs'
          size='xs'
          data={ciTypeOptions}
          value={rowData.ciTypeId !== null ? rowData.ciTypeId.toString() : null}
          onChange={(value) => {
            const newCiTypeId = value ? parseInt(value, 10) : null;
            if (newCiTypeId) {
              handleFieldChange(rowData.id, 'ciTypeId', newCiTypeId);
            }
            clearFieldError(rowData.id, 'ciTypeId');
          }}
          searchable
          error={fieldErrors[rowData.id]?.ciTypeId}
        />
      ),
    },
    {
      name: 'attributeId',
      title: 'Attribute',
      sortable: false,
      customRender: (_, rowData) => (
        <AttributeCell
          rowData={rowData}
          onChange={(id) => {
            if (id !== null) {
              handleFieldChange(rowData.id, 'attributeId', id);
            }
            clearFieldError(rowData.id, 'attributeId');
          }}
          error={fieldErrors[rowData.id]?.attributeId}
        />
      ),
    },
    {
      name: 'operator',
      title: 'Operator',
      sortable: false,
      customRender: (_, rowData) => (
        <KanbanSelect
          mt='xs'
          size='xs'
          data={SEARCH_TYPES.map((item) => ({
            value: item,
            label: item,
          }))}
          value={rowData.operator}
          onChange={(value) => {
            handleFieldChange(rowData.id, 'operator', value || null);
            clearFieldError(rowData.id, 'operator');
          }}
          error={fieldErrors[rowData.id]?.operator}
        />
      ),
    },
    {
      name: 'amt',
      title: 'AMT',
      sortable: false,
      customRender: (_, rowData) => (
        <KanbanSelect
          mt='xs'
          size='xs'
          data={amtOptions}
          disabled={application === ChangeApplicationTypeEnum.JENKINS}
          value={rowData.amt}
          onChange={(value) => {
            handleFieldChange(rowData.id, 'amt', value || '');
            clearFieldError(rowData.id, 'amt');
          }}
          error={fieldErrors[rowData.id]?.amt}
        />
      ),
    },
  ];
  const savedColumns = useSavedColumns<RowData>(LocalStorageKey.COLUMN_DISPLAY_NODE_CONFIG, columns, [], SaveColumnType.SAVE_DATABASE);

  const tableProps: KanbanTableProps<RowData> = useMemo(() => {
    const tblProps: KanbanTableProps<RowData> = {
      columns: savedColumns,
      data: configData,
      actions: {
        customAction: (data) => {
          return (
            <Flex gap='xs' align='center'>
              <KanbanIconButton disabled={configData.length === 1} onClick={() => handleRemoveRow(data.id)}>
                <IconMinus />
              </KanbanIconButton>
              <KanbanIconButton onClick={() => handleAddRow(data.id)}>
                <IconPlus />
              </KanbanIconButton>
            </Flex>
          );
        },
      },
      pagination: {
        enable: false,
      },
    };
    return tblProps;
  }, [savedColumns, configData, handleRemoveRow, handleAddRow]);

  return (
    <Box>
      <KanbanTitle size={'sm'} mt={'var(--mantine-spacing-xs)'}>
        Mapping CMDB
      </KanbanTitle>
      <KanbanTable {...tableProps} title='' />
    </Box>
  );
});

CIConfigMapper.displayName = 'CIConfigMapper';

export default CIConfigMapper;
