export type Service = 'server' | 'external-job';
export const getConfigs = () => {
  const apiBaseUrl = process.env.REACT_APP_MBAMT_API_URL || '';
  const apiBaseUrlRemote = process.env.REACT_APP_MBAMT_API_URL_REMOTE || apiBaseUrl;

  const apiServiceRemote = (process.env.REACT_APP_MBAMT_API_SERVICE_REMOTE || '').split(',');

  const isDevMode = process.env.NODE_ENV === 'development';

  return {
    name: process.env.REACT_APP_NAME || '',
    fullname: process.env.REACT_APP_FULLNAME || '',
    description: process.env.REACT_APP_DESCRIPTION || '',
    deployUrl: process.env.REACT_APP_DEPLOY_URL || '',
    apiBaseUrl: apiBaseUrl,
    apiBaseUrlRemote: apiBaseUrlRemote,
    buildApiBaseUrl: (service: Service = 'server') => {
      const map: Record<Service, string> = {
        server: '/api',
        'external-job': '/api/external-job',
      };
      const baseUrl = map[service];
      if (!isDevMode || !apiServiceRemote.includes(service)) {
        return apiBaseUrl + baseUrl;
      }
      return apiBaseUrlRemote + baseUrl;
    },
    keycloak: {
      enable: process.env.REACT_APP_KEYCLOAK_ENABLED === 'true',
      url: process.env.REACT_APP_KEYCLOAK_URL || '',
      realm: process.env.REACT_APP_KEYCLOAK_REALM || '',
      clientId: process.env.REACT_APP_KEYCLOAK_CLIENT_ID || '',
    },
  };
};

export { LABEL_MAP, IGNORED_SEGMENTS_MAP } from '@core/configs/BreadcrumbConfigs';
