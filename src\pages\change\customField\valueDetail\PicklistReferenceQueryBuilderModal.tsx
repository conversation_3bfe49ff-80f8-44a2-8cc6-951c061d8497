import AmtModal from '@components/AmtModal';
import QueryBuilderComponent, { QueryBuilderField, validateQuery } from '@components/queryBuilder';
import { QueryBuilderOperatorEnum } from '@components/queryBuilder/QueryBuilderOperatorEnum';
import { INPUT_REQUIRE } from '@core/message/MesageConstant';
import { QueryRuleGroupType } from '@core/schema/RuleGroupCondition';
import { QueryRuleGroupTypeModel } from '@models/RuleGroupTypeModel';
import { KanbanButton } from 'kanban-design-system';
import React from 'react';
import { useMemo } from 'react';
import { RuleGroupType, RuleType } from 'react-querybuilder';

type QueryBuilderModalProps = {
  opened: boolean;
  onClose: () => void;
  onSave: (condition: QueryRuleGroupTypeModel) => void;
  initialCondition: RuleGroupType;
  fields: QueryBuilderField[];
};
export const QueryBuilderModal: React.FC<QueryBuilderModalProps> = ({ fields, initialCondition, onClose, onSave, opened }) => {
  const [condition, setCondition] = React.useState<RuleGroupType>(initialCondition);
  const [showError, setShowError] = React.useState(false);

  const isRuleValid = useMemo(() => validateQuery(condition), [condition]);

  React.useEffect(() => {
    setCondition(initialCondition);
    setShowError(false);
  }, [initialCondition]);

  const handleSave = () => {
    if (isRuleValid) {
      onSave(condition as QueryRuleGroupType);
    } else {
      setShowError(true);
    }
  };

  return (
    <AmtModal
      title='Define Criteria'
      opened={opened}
      onClose={onClose}
      size='80%'
      actions={
        <KanbanButton variant='filled' onClick={handleSave}>
          Save
        </KanbanButton>
      }>
      <QueryBuilderComponent
        error={showError && !isRuleValid ? INPUT_REQUIRE : undefined}
        value={condition}
        onChange={(val) => {
          setCondition(val);
          setShowError(false);
        }}
        fields={fields}
        baseRule={initialCondition.rules[0] as RuleType}
        operators={Object.values(QueryBuilderOperatorEnum)}
      />
    </AmtModal>
  );
};
