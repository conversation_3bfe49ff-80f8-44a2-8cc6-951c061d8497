import { BaseURL } from '@common/constants/BaseUrl';
import { RequestConfig } from '@core/api';
import { PaginationRequest } from './Type';
import { createRequest } from './Utils';
import { tableAffectedToMultiColumnFilterPaginationRequestModel } from '@common/utils/TableUtils';
import { TableAffactedSafeType } from 'kanban-design-system';
import { ResponseData, createResponseSchema, Page, createPageSchema } from '@core/schema/Common';
import { ChangeDocument, ChangeDocumentSchema } from '@core/schema/ChangeDocument';
import { z } from 'zod';
import { ChangeDocumentModel } from '@models/DocumentModel';

export class ChangeDocumentApi {
  static findAll(pagination: TableAffactedSafeType): RequestConfig<ResponseData<Page<ChangeDocument>>, PaginationRequest> {
    return {
      url: `${BaseURL.changeDocument}/all`,
      method: 'POST',
      schema: createResponseSchema(createPageSchema(ChangeDocumentSchema)),
      data: tableAffectedToMultiColumnFilterPaginationRequestModel(pagination),
    };
  }

  static findById(id: number) {
    return createRequest({
      url: `${BaseURL.changeDocument}/:id`,
      method: 'GET',
      schema: createResponseSchema(ChangeDocumentSchema),
      pathVariable: {
        id,
      },
    });
  }

  static save(document: ChangeDocumentModel): RequestConfig<ResponseData<ChangeDocument>> {
    return {
      url: `${BaseURL.changeDocument}`,
      method: 'POST',
      schema: createResponseSchema(ChangeDocumentSchema),
      data: document,
    };
  }

  static deleteById(id: number): RequestConfig<ResponseData<void>> {
    return {
      url: `${BaseURL.changeDocument}/${id}`,
      method: 'DELETE',
    };
  }

  static existsByName({ id, name }: { id: number | undefined; name: string }) {
    return createRequest({
      url: `${BaseURL.changeDocument}/${id}/exists?name=${name}`,
      method: 'GET',
      schema: createResponseSchema(z.boolean()),
    });
  }
}
