import { COMMON_DESCRIPTION_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { INPUT_REQUIRE } from '@core/message/MesageConstant';
import { z } from 'zod';

export const DocumentChangeLevelEnum = z.enum(['1', '2', '3', '4']);
export type DocumentChangeLevel = z.infer<typeof DocumentChangeLevelEnum>;

export const ChangeDocumentModelSchema = z.object({
  id: z.number().nullish(),
  name: z.string().trim().min(1, { message: INPUT_REQUIRE }),
  description: z.string().max(COMMON_DESCRIPTION_MAX_LENGTH).nullish(),
  changeLevels: z.array(z.number()).nullish(),
});

export type ChangeDocumentModel = z.infer<typeof ChangeDocumentModelSchema>;
