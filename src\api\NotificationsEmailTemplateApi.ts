import { BaseURL } from '@common/constants/BaseUrl';
import { createRequest } from './Utils';
import { createPageSchema, createResponseSchema, Page, ResponseData } from '@core/schema/Common';
import { TableAffactedSafeType } from 'kanban-design-system';
import { tableAffectedToPaginationRequest } from '@common/utils/TableUtils';
import { RequestConfig } from '@core/api';
import { PaginationRequest } from '@api/Type';
import { EmailTemplateDetailModel, EmailTemplateDetailSchema } from '@models/EmailTemplateModel';
import { z } from 'zod';
import { EmailTemplateType } from '@common/constants/EmailTemplateConstants';

export class NotificationsEmailTemplateApi {
  static findAll(data: TableAffactedSafeType): RequestConfig<ResponseData<Page<EmailTemplateDetailModel>>, PaginationRequest> {
    return createRequest({
      url: `${BaseURL.notificationEmailTemplate}`,
      method: 'GET',
      schema: createResponseSchema(createPageSchema(EmailTemplateDetailSchema)),
      params: tableAffectedToPaginationRequest(data),
    });
  }

  static findById(id: number) {
    return createRequest({
      url: `${BaseURL.notificationEmailTemplate}/${id}`,
      method: 'GET',
      schema: createResponseSchema(EmailTemplateDetailSchema),
    });
  }

  static findByType(type: EmailTemplateType) {
    return createRequest({
      url: `${BaseURL.notificationEmailTemplate}/type`,
      method: 'GET',
      params: { type },
      schema: createResponseSchema(EmailTemplateDetailSchema),
    });
  }

  static saveOrUpdate(data: EmailTemplateDetailModel) {
    return createRequest({
      url: `${BaseURL.notificationEmailTemplate}`,
      method: 'POST',
      schema: createResponseSchema(EmailTemplateDetailSchema),
      data,
    });
  }

  static deleteEmailTemplateById(id: number): RequestConfig<ResponseData<void>> {
    return createRequest({
      url: `${BaseURL.notificationEmailTemplate}/${id}`,
      method: 'DELETE',
    });
  }

  static deleteEmailTemplateByIds(data: number[]) {
    return createRequest({
      url: BaseURL.notificationEmailTemplate,
      method: 'DELETE',
      schema: createResponseSchema(createPageSchema(EmailTemplateDetailSchema)),
      data,
    });
  }

  static existsByName({ id, name }: { id: number | undefined; name: string }) {
    return createRequest({
      url: `${BaseURL.notificationEmailTemplate}/${id}/exists`,
      method: 'GET',
      schema: createResponseSchema(z.boolean()),
      params: { name },
    });
  }
}
