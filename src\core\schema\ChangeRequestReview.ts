import { z } from 'zod';
import { ChangeRequestReviewStatusEnum } from '@common/constants/ChangeRequestReviewStatusConstants';
import { ChangeRequestReviewApprovalStatusEnum } from '@common/constants/ChangeRequestReviewApprovalStatusConstants';

export const ChangeRequestReviewApproverSchema = z.object({
  approver: z.string().optional().nullable(),
  approvalStatus: ChangeRequestReviewApprovalStatusEnum.optional().nullish(),
});

export type ChangeRequestReviewApprover = z.infer<typeof ChangeRequestReviewApproverSchema>;

export const ChangeRequestReviewSchema = z.object({
  owner: z.string().optional().nullable(),
  status: ChangeRequestReviewStatusEnum,
  note: z.string().optional().nullable(),
  documentName: z.string().optional().nullable(),
  approvers: z.array(ChangeRequestReviewApproverSchema).optional().nullable(),
  comment: z.string().optional().nullable(),
  skipFileUpload: z.boolean().optional().default(false),
});

export type ChangeRequestReview = z.infer<typeof ChangeRequestReviewSchema>;

// Request schemas for different API endpoints
export const CoordinatorReviewRequestSchema = z.object({
  owner: z.string().min(1, { message: 'Owner is required' }).max(255, { message: 'Owner must not exceed 255 characters' }),
  note: z.string().min(1, { message: 'Note is required' }).max(2000, { message: 'Note must not exceed 2000 characters' }),
});

export const OwnerReviewRequestSchema = z.object({
  approvers: z
    .array(z.string().min(1, { message: 'Approver cannot be blank' }).max(255, { message: 'Approver must not exceed 255 characters' }))
    .nonempty({ message: 'Approvers list is required' }),
  owner: z.string().min(1, { message: 'Owner is required' }).max(255, { message: 'Owner must not exceed 255 characters' }),
  note: z.string().min(1, { message: 'Note is required' }).max(2000, { message: 'Note must not exceed 2000 characters' }),
  skipFileUpload: z.boolean().optional().default(false),
});

export const ApproverStatusRequestSchema = z.object({
  approvalStatus: z.enum(['ACCEPT', 'REJECT']),
  comment: z.string().min(1, { message: 'Comment is required' }).max(1000, { message: 'Comment must not exceed 255 characters' }),
});

export type CoordinatorReviewRequest = z.infer<typeof CoordinatorReviewRequestSchema>;
export type OwnerReviewRequest = z.infer<typeof OwnerReviewRequestSchema>;
export type ApproverStatusRequest = z.infer<typeof ApproverStatusRequestSchema>;
