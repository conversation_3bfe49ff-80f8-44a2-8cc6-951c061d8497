import { callRequest, RequestConfig } from '@core/api';
import { AppConfig } from '@core/hooks/AppConfigTypes';
import { defaultErrorNotification, showErrorNotification, showSuccessNotification } from '@core/hooks/Utils';

export async function callAppRequest<Response, SearchParam = unknown, BodyData = unknown>(
  requestConfig: RequestConfig<Response, SearchParam, BodyData>,
  appConfig?: Omit<AppConfig<Response>, 'withSignal' | 'confirm'>,
) {
  const { errorNotification = defaultErrorNotification, showLoading = false, successNotification, throwParsedError = true } = appConfig || {};

  try {
    const response = await callRequest(requestConfig, { showLoading, throwParsedError });
    showSuccessNotification(successNotification, response);
    return response;
  } catch (error) {
    const requestError = error instanceof Error ? error : new Error('Call Request error!');
    showErrorNotification(errorNotification, requestError);
  }
}
