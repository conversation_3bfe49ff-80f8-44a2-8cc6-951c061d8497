import { QueryObserverBaseResult } from '@tanstack/react-query';
import { useEffect } from 'react';
import { AppConfig } from './AppConfigTypes';
import { defaultErrorNotification, showErrorNotification } from './Utils';

const useConfigNotification = <TQueryFnData>(
  queryResult: QueryObserverBaseResult<TQueryFnData, Error>,
  appConfig?: Pick<AppConfig<TQueryFnData>, 'errorNotification'>,
) => {
  const { error, isError } = queryResult;
  const { errorNotification = defaultErrorNotification } = appConfig || {};

  // show ErrorNotificaiton
  useEffect(() => {
    if (isError && error) {
      showErrorNotification(errorNotification, error);
    }
  }, [error, errorNotification, isError]);
};

export default useConfigNotification;
