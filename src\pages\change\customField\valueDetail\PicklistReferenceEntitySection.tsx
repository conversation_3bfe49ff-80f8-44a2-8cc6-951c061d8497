import { ActionIcon, Box, Group } from '@mantine/core';
import { KanbanSelect, KanbanTable, KanbanText, TableAffactedSafeType } from 'kanban-design-system';
import { CustomFieldConditionModel, CustomFieldModel, PicklistOption } from '@models/CustomField';
import { UseFormReturn, useWatch } from 'react-hook-form';
import { getQueryFieldsFromReference, getReferenceFieldFromTable, mockReferenceTables } from '../ReferenceTableHelper';
import { CustomFieldApi } from '@api/CustomFieldApi';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import React from 'react';
import { EMPTY_RULE_GROUP, groupToString, QueryBuilderField } from '@components/queryBuilder';
import { QueryBuilderCombinatorEnum } from '@components/queryBuilder/QueryBuilderCombinatorEnum';
import { OPERATOR_LABELS, QueryBuilderOperatorEnum } from '@components/queryBuilder/QueryBuilderOperatorEnum';
import { QueryRuleGroupType } from '@core/schema/RuleGroupCondition';
import { QueryRuleGroupTypeModel } from '@models/RuleGroupTypeModel';
import { RuleGroupType } from 'react-querybuilder';
import equal from 'fast-deep-equal';
import { IconEdit, IconTrash } from '@tabler/icons-react';
import { DEFAULT_TABLE_AFFECTED } from '@common/utils/TableUtils';
import useMutate from '@core/hooks/useMutate';
import { QueryBuilderModal } from './PicklistReferenceQueryBuilderModal';

type Props = {
  form: UseFormReturn<CustomFieldModel>;
};

const ReferenceEntitySection: React.FC<Props> = ({ form }) => {
  const [selectedReference, setSelectedReference] = useState<string | null>(form.getValues('condition.tableReference'));
  const [showConditionSession, setShowConditionSession] = useState(false);
  const [tableData, setTableData] = useState<PicklistOption[]>([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const [tableAffectedChange, setTableAffectedChange] = useState<TableAffactedSafeType>(DEFAULT_TABLE_AFFECTED);

  const [tempCondition, setTempCondition] = useState<QueryRuleGroupTypeModel | undefined>(form.getValues('condition.ruleGroup'));

  const tableReference = useWatch({ control: form.control, name: 'condition.tableReference' });
  const ruleGroup = useWatch({ control: form.control, name: 'condition.ruleGroup' });

  const queryFields = useMemo<QueryBuilderField[]>(() => {
    return getQueryFieldsFromReference(selectedReference || '');
  }, [selectedReference]);

  const operatorOptions = useMemo(
    () =>
      Object.values(QueryBuilderOperatorEnum).map((e) => ({
        name: e,
        label: OPERATOR_LABELS[e as QueryBuilderOperatorEnum],
      })),
    [],
  );

  const defaultGroupCondition = useMemo<QueryRuleGroupType>(() => {
    if (!queryFields.length) {
      return EMPTY_RULE_GROUP;
    }
    return {
      combinator: QueryBuilderCombinatorEnum.AND,
      rules: [
        {
          field: queryFields[0].name,
          operator: queryFields[0]?.operators?.[0] ?? QueryBuilderOperatorEnum.CONTAINS,
          value: '',
        },
      ],
    };
  }, [queryFields]);

  const { data, mutate: mutateValueCondition } = useMutate(
    (params: { payload: CustomFieldConditionModel; pagination: TableAffactedSafeType }) => CustomFieldApi.condition(params),
    {
      successNotification: { enable: false },
      errorNotification: { enable: false },
    },
  );

  const fetchConditionData = useCallback(() => {
    if (!tableReference || !ruleGroup) {
      return;
    }
    mutateValueCondition({
      payload: {
        tableReference,
        columnReference: getReferenceFieldFromTable(tableReference) || '',
        ruleGroup,
      },
      pagination: tableAffectedChange,
    });
  }, [tableReference, ruleGroup, mutateValueCondition, tableAffectedChange]);

  useEffect(() => {
    if (data?.data?.content) {
      setTotalRecords(data.data.totalElements);
      setTableData(data.data.content);
    }
  }, [data]);

  const isInitialMount = useRef(true);
  const prevReference = useRef<string | null>(null);
  useEffect(() => {
    if (selectedReference) {
      const existingRuleGroup = form.getValues('condition.ruleGroup');
      const isReferenceChanged = prevReference.current !== selectedReference;
      if (isInitialMount.current) {
        if (!existingRuleGroup || !existingRuleGroup.rules?.length) {
          form.setValue('condition.ruleGroup', EMPTY_RULE_GROUP);
        }
        isInitialMount.current = false;
      } else if (isReferenceChanged) {
        form.setValue('condition.ruleGroup', EMPTY_RULE_GROUP);
      }
      prevReference.current = selectedReference;
      setTableData([]);
      fetchConditionData();
    }
  }, [selectedReference, form, fetchConditionData]);

  return (
    <>
      <Box mb='md'>
        <KanbanSelect
          label='Select Reference Table'
          allowDeselect={false}
          withAsterisk
          placeholder='Choose table'
          data={Array.from(mockReferenceTables.entries()).map(([key, value]) => ({
            label: `${key} (${value.referenceField})`,
            value: key,
          }))}
          value={selectedReference}
          onChange={(newValue) => {
            setSelectedReference(newValue);
            form.setValue(
              'condition',
              {
                ...form.getValues('condition'),
                tableReference: newValue || '',
                columnReference: getReferenceFieldFromTable(newValue || '') || '',
              },
              { shouldValidate: true },
            );
          }}
          error={form.formState.errors.condition?.tableReference?.message}
        />

        {selectedReference && (
          <>
            <Group mt='md' gap='xs' align='center'>
              <KanbanText fw='bold'>Criteria</KanbanText>
              <ActionIcon variant='light' size='sm'>
                <IconEdit
                  size={20}
                  onClick={() => {
                    const current = form.getValues('condition.ruleGroup');
                    if (!current?.rules?.length) {
                      setTempCondition(defaultGroupCondition);
                    } else {
                      setTempCondition(current);
                    }
                    setShowConditionSession(true);
                  }}
                />
              </ActionIcon>
              <ActionIcon variant='light' color='red' size='sm'>
                <IconTrash
                  size={20}
                  onClick={() => {
                    form.setValue('condition.ruleGroup', EMPTY_RULE_GROUP);
                    fetchConditionData();
                  }}
                />
              </ActionIcon>
            </Group>

            {form.getValues('condition.ruleGroup') && (
              <KanbanText mt='md' size='xs'>
                {groupToString(form.getValues('condition.ruleGroup') as RuleGroupType, queryFields, operatorOptions)}
              </KanbanText>
            )}
          </>
        )}
      </Box>
      {showConditionSession && (
        <QueryBuilderModal
          opened={showConditionSession}
          onClose={() => setShowConditionSession(false)}
          initialCondition={(tempCondition as RuleGroupType) ?? defaultGroupCondition}
          fields={queryFields}
          onSave={(val: QueryRuleGroupType | undefined) => {
            form.setValue('condition.ruleGroup', val);
            fetchConditionData();
            setShowConditionSession(false);
          }}
        />
      )}

      <KanbanTable
        columns={[
          {
            name: 'value',
            title: 'Value',
            customRender: (_data, rowData) => rowData.value,
          },
        ]}
        data={tableData}
        pagination={{ enable: true }}
        serverside={{
          totalRows: totalRecords,
          onTableAffected: (dataSet) => {
            if (!equal(tableAffectedChange, dataSet)) {
              setTableAffectedChange(dataSet);
            }
          },
        }}
      />
    </>
  );
};

export default ReferenceEntitySection;
