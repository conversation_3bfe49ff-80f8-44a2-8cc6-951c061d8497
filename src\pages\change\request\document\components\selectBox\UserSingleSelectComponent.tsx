import { SelectWithPage } from '@components/SelectWithPage';
import React, { useMemo, useState } from 'react';
import { getDefaultTableAffected, TableAffactedSafeType } from 'kanban-design-system';
import useInfiniteFetch from '@core/hooks/useInfiniteFetch';
import { UserApi } from '@api/UserApi';
import { ComboboxItem } from '@mantine/core';

interface UserSingleSelectComponentProps {
  onChange: (val: string | undefined) => void;
  value: ComboboxItem;
  error?: string;
}

const UserSingleSelectComponent = ({ error, onChange, value }: UserSingleSelectComponentProps) => {
  const [searchSelect, setSearchSelect] = useState('');

  const serviceSearchParams: TableAffactedSafeType = useMemo(
    () => ({
      ...getDefaultTableAffected(),
      sortedBy: 'name',
      userName: searchSelect,
      advancedFilterMapping: {
        userName: {
          filterOption: 'contains',
          value: { fromValue: searchSelect },
        },
      },
    }),
    [searchSelect],
  );

  const { fetchNextPage, flatData } = useInfiniteFetch(UserApi.findAll(serviceSearchParams), {
    showLoading: false,
    enabled: true,
  });

  const options = useMemo(
    () =>
      flatData.map((item) => ({
        value: item.userName,
        label: `${item.name} (${item.userName})`,
      })),
    [flatData],
  );

  return (
    <SelectWithPage
      onSearch={(val) => setSearchSelect(val || '')}
      value={value}
      isLoading={flatData.length === 0}
      handleScrollToBottom={fetchNextPage}
      options={options}
      clearable
      onChange={onChange}
      error={error}
    />
  );
};

export default UserSingleSelectComponent;
