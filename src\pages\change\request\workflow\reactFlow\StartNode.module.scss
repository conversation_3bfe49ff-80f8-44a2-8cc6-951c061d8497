.startNode {
  position: relative;
  width: 150px;
  height: 150px;
  border-radius: 8px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  background: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px;
  align-items: center;
  z-index: 2;

  &.selected {
    border-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  }
}

.nodeLabel {
  color: #374151;
  margin-top: 10px;
}

.handle {
  background: #6b7280;
  width: 16px;
  height: 16px;
  border: 2px solid white;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  margin-top: -8px;

  &.sourceHandle {
    right: -8px;
  }
}