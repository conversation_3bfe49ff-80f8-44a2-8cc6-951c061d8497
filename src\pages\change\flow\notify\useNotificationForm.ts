import { useState, useCallback, useMemo } from 'react';
import { ChangeFlowNotificationModel } from '@models/ChangeFlowModel';
import { ComboboxItem } from '@mantine/core';
import { INPUT_REQUIRE } from '@core/message/MesageConstant';

export const VARIABLE_PREFIX = '$';
export function useNotificationForm() {
  const [selectedTemplate, setSelectedTemplate] = useState<number | null>(null);
  const [notifyItems, setNotifyItems] = useState<string[]>([]);
  const [notifyError, setNotifyError] = useState<string | null>(null);

  const prepareNotification = (options: ComboboxItem[], statusId: string): ChangeFlowNotificationModel | null => {
    if (selectedTemplate === null) {
      return {
        emailTemplateId: null,
        notifyTo: [],
        notifyNode: [],
        statusId,
      };
    }

    if (notifyItems.length === 0) {
      setNotifyError(INPUT_REQUIRE);
      return null;
    }

    const notifyTo: string[] = [];
    const notifyNode: string[] = [];

    for (const item of notifyItems) {
      if (options?.some((opt) => opt.value === item) || item.startsWith(VARIABLE_PREFIX)) {
        notifyNode.push(item);
      } else {
        notifyTo.push(item);
      }
    }

    return {
      emailTemplateId: selectedTemplate,
      notifyTo,
      notifyNode,
      statusId,
    };
  };

  const resetNotificationForm = useCallback(() => {
    setSelectedTemplate(null);
    setNotifyItems([]);
    setNotifyError(null);
  }, []);

  // Memoize setter functions to prevent unnecessary rerenders
  const memoizedSetters = useMemo(
    () => ({
      setSelectedTemplate,
      setNotifyItems,
      setNotifyError,
    }),
    [],
  );

  return {
    selectedTemplate,
    setSelectedTemplate: memoizedSetters.setSelectedTemplate,
    notifyItems,
    setNotifyItems: memoizedSetters.setNotifyItems,
    notifyError,
    setNotifyError: memoizedSetters.setNotifyError,
    prepareNotification,
    resetNotificationForm,
  };
}
