import React, { useCallback, useEffect, useRef, useState, useMemo } from 'react';
import {
  ReactFlow,
  Background,
  Node,
  addEdge,
  NodeChange,
  EdgeChange,
  OnConnect,
  ConnectionLineType,
  BackgroundVariant,
  Panel,
  PanOnScrollMode,
  Edge,
  ReactFlowInstance,
  OnNodeDrag,
} from '@xyflow/react';
import stylesCss from './WorkflowReactFlow.module.scss';
import CustomNode from './CustomNode';
import AnimatedEdge from './AnimatedEdge';
import StickyNoteNode from './StickyNoteNode';
import { KanbanIconButton } from 'kanban-design-system';
import { IconHandClick, IconHandOff, IconNote, IconScan, IconZoomIn, IconZoomOut, IconArrowBackUp, IconArrowForwardUp } from '@tabler/icons-react';
import StartNode from './StartNode';
import { ChangeRequestWorkflowNodeTypeEnum } from '@common/constants/ChangeWorkflowNodeType';
import useUndoable from 'use-undoable';
import { SICKY_NOTE_CONTENT, STICKY_NOTE_TITLE } from '@common/constants/WorkflowConstants';
import { ZOOM_CONFIG, KEYBOARD_SHORTCUTS, GRID_CONFIG, KeyboardShortcutEvent } from '@common/constants/ReactFlowConstant';

// **Type Definitions:** (giữ nguyên như cũ)
interface StickyNote {
  id: string;
  title: string;
  content: string;
  position: { x: number; y: number };
}

interface ClipboardData {
  nodes: Node[];
  edges: Edge[];
}

interface FlowState {
  nodes: Node[];
  edges: Edge[];
}

interface NodeIdMapping {
  [key: string]: string;
}

// **Constants:** (giữ nguyên như cũ)
const nodeTypes = {
  CUSTOM_NODE: CustomNode,
  STICKY_NOTE: StickyNoteNode,
  START_NODE: StartNode,
} as const;

const edgeTypes = {
  animated: AnimatedEdge,
} as const;

const DEFAULT_STICKY_NOTE_SIZE = {
  width: 200,
  height: 200,
} as const;

// **Component Props:** (giữ nguyên như cũ)
type WorkflowReactFlowProps = {
  nodes: Node[];
  edges: Edge[];
  onNodesChange: (changes: NodeChange[]) => void;
  onEdgesChange: (changes: EdgeChange[]) => void;
  setEdges: React.Dispatch<React.SetStateAction<Edge[]>>;
  setNodes: React.Dispatch<React.SetStateAction<Node[]>>;
  onNodeClick?: (event: React.MouseEvent, node: Node) => void;
  onNodeDoubleClick?: (event: React.MouseEvent, node: Node) => void;
  onEdgeTypeUpdate?: (edgeId: string, type: string) => void;
  isOnlyView?: boolean;
};

export const WorkflowReactFlow: React.FC<WorkflowReactFlowProps> = ({
  edges,
  isOnlyView,
  nodes,
  onEdgesChange,
  onEdgeTypeUpdate,
  onNodeClick,
  onNodeDoubleClick,
  onNodesChange,
  setEdges,
  setNodes,
}) => {
  // **Refs:**
  const reactFlowInstance = useRef<ReactFlowInstance | null>(null);
  const hasInitialized = useRef<boolean>(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const dragTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isCurrentlyDragging = useRef<boolean>(false);

  // **State Management:**
  const [isPanning, setIsPanning] = useState<boolean>(false);
  const [isDiagramFocused, setIsDiagramFocused] = useState<boolean>(false);
  const [clipboard, setClipboard] = useState<ClipboardData>({ nodes: [], edges: [] });
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [isResizing, setIsResizing] = useState<boolean>(false);
  const [isMiddleMousePressed, setIsMiddleMousePressed] = useState<boolean>(false);
  const [isSpaceKeyPressed, setIsSpaceKeyPressed] = useState<boolean>(false);

  // **Pan State:**
  const [panStart, setPanStart] = useState<{ x: number; y: number } | null>(null);
  const [lastViewport, setLastViewport] = useState<{ x: number; y: number; zoom: number }>({ x: 0, y: 0, zoom: 1 });

  // **Undo/Redo State Management:**
  const [, setState, { canRedo, canUndo, future, past, redo, reset, undo }] = useUndoable<FlowState>({
    nodes: [],
    edges: [],
  });

  // **Initialize state management effect:**
  useEffect(() => {
    if (nodes.length > 0 && !hasInitialized.current) {
      reset({ nodes, edges });
      hasInitialized.current = true;
    }
  }, [nodes, edges, reset]);

  // **Optimized State Sync Effect - Debounced:**
  useEffect(() => {
    if (!hasInitialized.current || isCurrentlyDragging.current) {
      return;
    }

    // Clear existing timeout
    if (dragTimeoutRef.current) {
      clearTimeout(dragTimeoutRef.current);
    }

    // Debounce state updates
    dragTimeoutRef.current = setTimeout(() => {
      if (!isCurrentlyDragging.current) {
        setState({ nodes, edges });
      }
    }, 100);

    return () => {
      if (dragTimeoutRef.current) {
        clearTimeout(dragTimeoutRef.current);
      }
    };
  }, [nodes, edges, setState]);

  // **Update panning state:**
  useEffect(() => {
    setIsPanning(isSpaceKeyPressed || isMiddleMousePressed);
  }, [isSpaceKeyPressed, isMiddleMousePressed]);

  // **Optimized Node Drag Handlers:**
  const onNodeDragStart = useCallback<OnNodeDrag>(() => {
    if (!isResizing) {
      isCurrentlyDragging.current = true;
      setIsDragging(true);

      // Clear any pending state updates
      if (dragTimeoutRef.current) {
        clearTimeout(dragTimeoutRef.current);
        dragTimeoutRef.current = null;
      }
    }
  }, [isResizing]);

  const onNodeDragStop = useCallback<OnNodeDrag>(() => {
    // Use setTimeout to ensure this runs after the position update
    setTimeout(() => {
      isCurrentlyDragging.current = false;
      setIsDragging(false);

      // Save state after drag is complete
      if (hasInitialized.current) {
        setState({ nodes, edges });
      }
    }, 0);
  }, [setState, nodes, edges]);

  // **Resize Handlers:**
  const handleResizeStart = useCallback((): void => {
    setIsResizing(true);
  }, []);

  const handleResizeStop = useCallback((): void => {
    setIsResizing(false);
  }, []);

  // **Optimized Connection Handler:**
  const onConnect = useCallback<OnConnect>(
    (connection) => {
      if (!connection.source || !connection.target) {
        return;
      }

      const newEdge: Edge = {
        ...connection,
        id: `edge-${connection.source}-${connection.target}-${Date.now()}`,
        type: 'animated',
        animated: false,
        data: {
          onDelete: (edgeId: string): void => {
            setEdges((eds) => eds.filter((e) => e.id !== edgeId));
          },
          onTypeUpdate: (edgeId: string, action: string): void => {
            setEdges((eds) =>
              eds.map((edge) => {
                if (edge.id === edgeId) {
                  return {
                    ...edge,
                    data: {
                      ...edge.data,
                      action: action,
                    },
                  };
                }
                return edge;
              }),
            );

            if (onEdgeTypeUpdate) {
              onEdgeTypeUpdate(edgeId, action);
            }
          },
        },
      };

      setEdges((eds) => addEdge(newEdge, eds));
    },
    [setEdges, onEdgeTypeUpdate],
  );

  // **Simplified Change Handlers:**
  const handleNodesChange = useCallback(
    (changes: NodeChange[]): void => {
      onNodesChange(changes);
    },
    [onNodesChange],
  );

  const handleEdgesChange = useCallback(
    (changes: EdgeChange[]): void => {
      onEdgesChange(changes);
    },
    [onEdgesChange],
  );

  // **Optimized Nodes Memo - Reduced Dependencies:**
  const updatedNodes = useMemo((): Node[] => {
    // Only recalculate when absolutely necessary
    const shouldDisableDrag = isResizing || isMiddleMousePressed;

    return nodes.map((node): Node => {
      const baseNode = {
        ...node,
        draggable: !shouldDisableDrag,
      };

      // Only add resize handlers for sticky notes
      if (node.type === ChangeRequestWorkflowNodeTypeEnum.STICKY_NOTE) {
        return {
          ...baseNode,
          data: {
            ...node.data,
            onResizeStart: handleResizeStart,
            onResizeStop: handleResizeStop,
          },
        };
      }

      return baseNode;
    });
  }, [nodes, isResizing, isMiddleMousePressed, handleResizeStart, handleResizeStop]);

  // **Zoom Functions:** (giữ nguyên như cũ)
  const zoomIn = useCallback((): void => {
    reactFlowInstance.current?.zoomIn({ duration: ZOOM_CONFIG.duration });
  }, []);

  const zoomOut = useCallback((): void => {
    reactFlowInstance.current?.zoomOut({ duration: ZOOM_CONFIG.duration });
  }, []);

  const zoomToFit = useCallback((): void => {
    reactFlowInstance.current?.fitView({
      duration: ZOOM_CONFIG.duration,
      maxZoom: ZOOM_CONFIG.maxZoom,
      minZoom: ZOOM_CONFIG.minZoom,
      padding: ZOOM_CONFIG.padding,
    });
  }, []);

  // **Selection Functions:** (giữ nguyên như cũ)
  const selectAllItems = useCallback((): void => {
    const selectNodes: NodeChange[] = nodes.map((node) => ({
      type: 'select',
      id: node.id,
      selected: true,
    }));
    const selectEdges: EdgeChange[] = edges.map((edge) => ({
      type: 'select',
      id: edge.id,
      selected: true,
    }));

    onNodesChange(selectNodes);
    onEdgesChange(selectEdges);
  }, [nodes, edges, onNodesChange, onEdgesChange]);

  // **Clipboard Functions:** (giữ nguyên như cũ - các hàm này không ảnh hưởng đến drag performance)
  const copySelectedItems = useCallback((): void => {
    const selectedNodes = nodes.filter((node) => node.selected && node.type !== ChangeRequestWorkflowNodeTypeEnum.START_NODE);
    const selectedEdges = edges.filter((edge) => edge.selected);

    setClipboard({
      nodes: selectedNodes,
      edges: selectedEdges,
    });
  }, [nodes, edges]);

  const generateNodeIdMapping = useCallback((clipboardNodes: Node[]): NodeIdMapping => {
    const mapping: NodeIdMapping = {};
    clipboardNodes.forEach((node) => {
      mapping[node.id] = `${node.id}-copy-${Date.now()}-${Math.random()}`;
    });
    return mapping;
  }, []);

  const pasteItems = useCallback((): void => {
    if (clipboard.nodes.length === 0 && clipboard.edges.length === 0) {
      return;
    }

    const nodeIdMapping = generateNodeIdMapping(clipboard.nodes);

    const newNodes: Node[] = clipboard.nodes.map((node) => ({
      ...node,
      id: nodeIdMapping[node.id],
      position: {
        x: node.position.x + 50,
        y: node.position.y + 50,
      },
      selected: true,
    }));

    const newEdges: Edge[] = clipboard.edges
      .filter((edge) => nodeIdMapping[edge.source] && nodeIdMapping[edge.target])
      .map((edge) => ({
        ...edge,
        id: `${edge.id}-copy-${Date.now()}-${Math.random()}`,
        source: nodeIdMapping[edge.source],
        target: nodeIdMapping[edge.target],
        selected: true,
      }));

    setNodes((prevNodes) => [...prevNodes, ...newNodes]);
    setEdges((prevEdges) => [...prevEdges, ...newEdges]);
  }, [clipboard, setNodes, setEdges, generateNodeIdMapping]);

  // **Delete Function:**  (giữ nguyên như cũ)
  const deleteSelectedItems = useCallback((): void => {
    if (isOnlyView) {
      return;
    }
    const selectedNodeIds = nodes.filter((node) => node.selected).map((node) => node.id);
    const selectedEdgeIds = edges.filter((edge) => edge.selected).map((edge) => edge.id);

    if (selectedNodeIds.length === 0 && selectedEdgeIds.length === 0) {
      return;
    }

    const nodeRemoveChanges: NodeChange[] = selectedNodeIds.map((id) => ({
      type: 'remove',
      id,
    }));

    const edgeRemoveChanges: EdgeChange[] = selectedEdgeIds.map((id) => ({
      type: 'remove',
      id,
    }));

    onNodesChange(nodeRemoveChanges);
    onEdgesChange(edgeRemoveChanges);
  }, [isOnlyView, nodes, edges, onNodesChange, onEdgesChange]);

  // **Duplicate Function:** (giữ nguyên như cũ)
  const duplicateSelectedItems = useCallback((): void => {
    copySelectedItems();
    setTimeout(() => {
      pasteItems();
    }, 10);
  }, [copySelectedItems, pasteItems]);

  // **Sticky Note Function:** (giữ nguyên như cũ)
  const addStickyNote = useCallback((): void => {
    const newStickyNote: StickyNote = {
      id: `sticky-${Date.now()}`,
      title: STICKY_NOTE_TITLE,
      content: SICKY_NOTE_CONTENT,
      position: {
        x: Math.random() * 200,
        y: Math.random() * 200,
      },
    };

    const newNode: Node = {
      id: newStickyNote.id,
      type: ChangeRequestWorkflowNodeTypeEnum.STICKY_NOTE,
      position: newStickyNote.position,
      draggable: !isResizing,
      data: {
        title: newStickyNote.title,
        content: newStickyNote.content,
        onResizeStart: handleResizeStart,
        onResizeStop: handleResizeStop,
      },
      style: {
        width: DEFAULT_STICKY_NOTE_SIZE.width,
        height: DEFAULT_STICKY_NOTE_SIZE.height,
      },
    };

    setNodes((prevNodes) => [...prevNodes, newNode]);
  }, [setNodes, isResizing, handleResizeStart, handleResizeStop]);

  // **Pan Mode Toggle:**
  const togglePanMode = useCallback((): void => {
    setIsSpaceKeyPressed((prev) => !prev);
  }, []);

  // **Improved Undo/Redo Functions:**
  const handleUndo = useCallback((): void => {
    if (canUndo && hasInitialized.current && !isCurrentlyDragging.current) {
      undo();
      const previousState = past[past.length - 1];
      if (previousState) {
        setNodes(previousState.nodes);
        setEdges(previousState.edges);
      }
    }
  }, [canUndo, undo, past, setNodes, setEdges]);

  const handleRedo = useCallback((): void => {
    if (canRedo && hasInitialized.current && !isCurrentlyDragging.current) {
      redo();
      const nextState = future[0];
      if (nextState) {
        setNodes(nextState.nodes);
        setEdges(nextState.edges);
      }
    }
  }, [canRedo, redo, future, setNodes, setEdges]);

  // **Focus Handlers:**
  const onContainerFocus = useCallback((): void => {
    setIsDiagramFocused(true);
  }, []);

  const onContainerBlur = useCallback((): void => {
    setIsDiagramFocused(false);
  }, []);

  // **Optimized Mouse Handlers - Reduced Complex Logic:**
  const handleContainerMouseDown = useCallback((event: React.MouseEvent<HTMLDivElement>): void => {
    if (event.button === 1) {
      event.preventDefault();
      setIsMiddleMousePressed(true);
      setPanStart({ x: event.clientX, y: event.clientY });

      if (reactFlowInstance.current) {
        const viewport = reactFlowInstance.current.getViewport();
        setLastViewport(viewport);
      }
    }
  }, []);

  const handleContainerMouseUp = useCallback((event: React.MouseEvent<HTMLDivElement>): void => {
    if (event.button === 1) {
      event.preventDefault();
      setIsMiddleMousePressed(false);
      setPanStart(null);
    }
  }, []);

  const handleContainerMouseLeave = useCallback((): void => {
    if (isMiddleMousePressed) {
      setIsMiddleMousePressed(false);
      setPanStart(null);
    }
  }, [isMiddleMousePressed]);

  const handleContainerMouseMove = useCallback(
    (event: React.MouseEvent<HTMLDivElement>): void => {
      if (isMiddleMousePressed && panStart && reactFlowInstance.current) {
        event.preventDefault();

        const dx = event.clientX - panStart.x;
        const dy = event.clientY - panStart.y;

        const newViewport = {
          x: lastViewport.x + dx,
          y: lastViewport.y + dy,
          zoom: lastViewport.zoom,
        };

        reactFlowInstance.current.setViewport(newViewport);
      }
    },
    [isMiddleMousePressed, panStart, lastViewport],
  );

  // **Simplified Global Handlers:**
  const handleGlobalMouseUp = useCallback(
    (event: MouseEvent): void => {
      if (event.button === 1 && isMiddleMousePressed) {
        setIsMiddleMousePressed(false);
        setPanStart(null);
      }
    },
    [isMiddleMousePressed],
  );

  const handleGlobalMouseMove = useCallback(
    (event: MouseEvent): void => {
      if (isMiddleMousePressed && panStart && reactFlowInstance.current) {
        const dx = event.clientX - panStart.x;
        const dy = event.clientY - panStart.y;

        const newViewport = {
          x: lastViewport.x + dx,
          y: lastViewport.y + dy,
          zoom: lastViewport.zoom,
        };

        reactFlowInstance.current.setViewport(newViewport);
      }
    },
    [isMiddleMousePressed, panStart, lastViewport],
  );

  const handleWheel = useCallback(
    (event: React.WheelEvent<HTMLDivElement>): void => {
      if (isMiddleMousePressed) {
        event.preventDefault();
      }
    },
    [isMiddleMousePressed],
  );

  const handleContextMenu = useCallback(
    (event: React.MouseEvent<HTMLDivElement>): void => {
      if (isMiddleMousePressed) {
        event.preventDefault();
      }
    },
    [isMiddleMousePressed],
  );

  // **Optimized Keyboard Handler:**
  const handleKeyboardShortcuts = useCallback(
    (event: KeyboardEvent): void => {
      const { code, ctrlKey, key } = event as KeyboardShortcutEvent;

      if (code === KEYBOARD_SHORTCUTS.SPACE && !ctrlKey) {
        setIsSpaceKeyPressed(true);
        return;
      }

      if (code === KEYBOARD_SHORTCUTS.DELETE && isDiagramFocused) {
        event.preventDefault();
        deleteSelectedItems();
        return;
      }

      if (!isDiagramFocused || !ctrlKey) {
        return;
      }

      const keyLower = key.toLowerCase();

      switch (keyLower) {
        case KEYBOARD_SHORTCUTS.ZOOM_IN[0]:
        case KEYBOARD_SHORTCUTS.ZOOM_IN[1]:
          event.preventDefault();
          zoomIn();
          break;
        case KEYBOARD_SHORTCUTS.ZOOM_OUT:
          event.preventDefault();
          zoomOut();
          break;
        case KEYBOARD_SHORTCUTS.ZOOM_FIT:
          event.preventDefault();
          zoomToFit();
          break;
        case KEYBOARD_SHORTCUTS.SELECT_ALL:
          event.preventDefault();
          selectAllItems();
          break;
        case KEYBOARD_SHORTCUTS.COPY:
          event.preventDefault();
          copySelectedItems();
          break;
        case KEYBOARD_SHORTCUTS.PASTE:
          event.preventDefault();
          pasteItems();
          break;
        case KEYBOARD_SHORTCUTS.DUPLICATE:
          event.preventDefault();
          duplicateSelectedItems();
          break;
        case KEYBOARD_SHORTCUTS.UNDO:
          event.preventDefault();
          handleUndo();
          break;
        case KEYBOARD_SHORTCUTS.REDO:
          event.preventDefault();
          handleRedo();
          break;
        default:
          break;
      }
    },
    [
      isDiagramFocused,
      zoomIn,
      zoomOut,
      zoomToFit,
      selectAllItems,
      copySelectedItems,
      pasteItems,
      duplicateSelectedItems,
      deleteSelectedItems,
      handleUndo,
      handleRedo,
    ],
  );

  const handleKeyRelease = useCallback((event: KeyboardEvent): void => {
    if (event.code === KEYBOARD_SHORTCUTS.SPACE) {
      setIsSpaceKeyPressed(false);
    }
  }, []);

  // **Event Listeners Effect:**
  useEffect(() => {
    document.addEventListener('keydown', handleKeyboardShortcuts);
    document.addEventListener('keyup', handleKeyRelease);
    document.addEventListener('mouseup', handleGlobalMouseUp);
    document.addEventListener('mousemove', handleGlobalMouseMove);

    return (): void => {
      document.removeEventListener('keydown', handleKeyboardShortcuts);
      document.removeEventListener('keyup', handleKeyRelease);
      document.removeEventListener('mouseup', handleGlobalMouseUp);
      document.removeEventListener('mousemove', handleGlobalMouseMove);
    };
  }, [handleKeyboardShortcuts, handleKeyRelease, handleGlobalMouseUp, handleGlobalMouseMove]);

  // **Optimized Click Handlers:**
  const onNodeClickInternal = useCallback(
    (event: React.MouseEvent, node: Node): void => {
      if (onNodeClick) {
        onNodeClick(event, node);
      }
      if (isResizing || isDragging || isMiddleMousePressed) {
        return;
      }

      const deselectAllChanges: NodeChange[] = nodes.map((n) => ({
        type: 'select',
        id: n.id,
        selected: false,
      }));

      const selectClickedNodeChange: NodeChange[] = [
        {
          type: 'select',
          id: node.id,
          selected: true,
        },
      ];

      onNodesChange([...deselectAllChanges, ...selectClickedNodeChange]);
    },
    [onNodeClick, isResizing, isDragging, isMiddleMousePressed, nodes, onNodesChange],
  );

  const onNodeDoubleClickInternal = useCallback(
    (event: React.MouseEvent, node: Node): void => {
      if (!isMiddleMousePressed) {
        onNodeDoubleClick?.(event, node);
      }
    },
    [onNodeDoubleClick, isMiddleMousePressed],
  );

  const onPaneClick = useCallback((): void => {
    if (isResizing || isMiddleMousePressed) {
      return;
    }

    const deselectNodes: NodeChange[] = nodes.map((node) => ({
      type: 'select',
      id: node.id,
      selected: false,
    }));

    const deselectEdges: EdgeChange[] = edges.map((edge) => ({
      type: 'select',
      id: edge.id,
      selected: false,
    }));

    onNodesChange(deselectNodes);
    onEdgesChange(deselectEdges);
  }, [nodes, edges, onNodesChange, onEdgesChange, isResizing, isMiddleMousePressed]);

  // **Cleanup Effect:**
  useEffect(() => {
    return () => {
      if (dragTimeoutRef.current) {
        clearTimeout(dragTimeoutRef.current);
      }
    };
  }, []);

  // **Component JSX:**
  return (
    <div
      ref={containerRef}
      className={`${stylesCss.flowContainer} ${isPanning ? stylesCss.panningMode : ''}`}
      tabIndex={0}
      onFocus={onContainerFocus}
      onBlur={onContainerBlur}
      onMouseDown={handleContainerMouseDown}
      onMouseUp={handleContainerMouseUp}
      onMouseLeave={handleContainerMouseLeave}
      onMouseMove={handleContainerMouseMove}
      onWheel={handleWheel}
      onContextMenu={handleContextMenu}
      style={{
        outline: 'none',
        cursor: isMiddleMousePressed ? 'grabbing' : isPanning ? 'grab' : 'default',
      }}>
      <ReactFlow
        onNodeClick={onNodeClickInternal}
        onNodeDoubleClick={onNodeDoubleClickInternal}
        onPaneClick={onPaneClick}
        className={stylesCss.reactFlow}
        nodes={updatedNodes}
        edges={edges}
        onNodesChange={handleNodesChange}
        onEdgesChange={handleEdgesChange}
        onConnect={onConnect}
        onNodeDragStart={onNodeDragStart}
        onNodeDragStop={onNodeDragStop}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        connectionRadius={30}
        connectionLineType={ConnectionLineType.Bezier}
        onInit={(instance): void => {
          reactFlowInstance.current = instance;
        }}
        fitView={true}
        fitViewOptions={ZOOM_CONFIG}
        minZoom={ZOOM_CONFIG.minZoom}
        maxZoom={5}
        snapToGrid={true}
        snapGrid={GRID_CONFIG.snap}
        panOnDrag={isPanning && !isResizing && !isMiddleMousePressed}
        selectionOnDrag={!isPanning && !isResizing && !isMiddleMousePressed}
        panOnScroll={false}
        panOnScrollMode={PanOnScrollMode.Free}
        zoomOnScroll={true}
        zoomOnPinch={true}
        zoomOnDoubleClick={false}
        multiSelectionKeyCode='Shift'
        nodesDraggable={!isResizing && !isMiddleMousePressed}
        nodesConnectable={!isResizing && !isMiddleMousePressed}
        elementsSelectable={!isResizing && !isMiddleMousePressed}
        deleteKeyCode={[]}>
        <Background variant={BackgroundVariant.Dots} gap={GRID_CONFIG.gap} size={GRID_CONFIG.size} color='#e5e7eb' />

        <Panel position='top-left' className={stylesCss.controlPanel}>
          <KanbanIconButton variant='outline' title='Zoom In (Ctrl + +)' onClick={zoomIn}>
            <IconZoomIn size={18} />
          </KanbanIconButton>

          <KanbanIconButton variant='outline' title='Zoom Out (Ctrl + -)' onClick={zoomOut}>
            <IconZoomOut size={18} />
          </KanbanIconButton>

          <KanbanIconButton variant='outline' title='Zoom To Fit (Ctrl + X)' onClick={zoomToFit}>
            <IconScan size={18} />
          </KanbanIconButton>
          {!isOnlyView && (
            <KanbanIconButton variant='outline' title='Add Sticky Note' onClick={addStickyNote}>
              <IconNote size={18} />
            </KanbanIconButton>
          )}

          <KanbanIconButton
            variant={isPanning ? 'filled' : 'outline'}
            title={isPanning ? 'Pan Mode Active (Hold Space or Middle Mouse)' : 'Pan Mode (Hold Space or Middle Mouse)'}
            onClick={togglePanMode}>
            {isPanning ? <IconHandClick size={18} /> : <IconHandOff size={18} />}
          </KanbanIconButton>
          {!isOnlyView && (
            <>
              <KanbanIconButton variant='outline' title='Undo (Ctrl + Z)' onClick={handleUndo} disabled={!canUndo}>
                <IconArrowBackUp size={18} />
              </KanbanIconButton>
              <KanbanIconButton variant='outline' title='Redo (Ctrl + Y)' onClick={handleRedo} disabled={!canRedo}>
                <IconArrowForwardUp size={18} />
              </KanbanIconButton>
            </>
          )}
        </Panel>
      </ReactFlow>
    </div>
  );
};

export default WorkflowReactFlow;
