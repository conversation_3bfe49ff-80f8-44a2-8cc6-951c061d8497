import React, { useMemo } from 'react';
import { IconGripVertical, IconSettings, IconX } from '@tabler/icons-react';
import GridLayout, { Layout, WidthProvider } from 'react-grid-layout';
import { Box, Group, Menu, Paper } from '@mantine/core';

import styles from '@resources/styles/ChangeTemplateBuilder.module.scss';

import FieldRenderer from './FieldRenderer';
import { useFormContext } from 'react-hook-form';
import { ChangeTemplate, TemplateCustomFieldTypeEnum, TemplateFieldItem } from '@core/schema/ChangeTemplate';
import {
  DRAG_TAG,
  DROP_ITEM_PREFIX,
  DROP_ZONE_DEFAULT_COLOR,
  DROP_ZONE_HIGHLIGHT_COLOR,
  GRID_COLS,
  ROW_HEIGHT,
} from '@common/constants/ChangeTemplateConstants';
import { KanbanIconButton } from 'kanban-design-system';
import RequiredCheckbox from './RequiredCheckbox';

const ResponsiveGridLayout = WidthProvider(GridLayout);

interface DroppingItem {
  i: string;
  w: number;
  h: number;
  x: number;
  y: number;
}

interface TemplateGridProps {
  layouts: Layout[];
  fields: TemplateFieldItem[];
  handleLayoutChange: (newLayout: Layout[]) => void;
  handleDropSourceItemOnGrid: (event: React.DragEvent, layout: Layout[], droppingItem: DroppingItem) => void;
  handleDragSourceItemOver: (event: React.DragEvent) => void;
  isDraggingSourceItem: boolean;
  draggedSourceItem: TemplateFieldItem | null;
  droppableHighlight: boolean;
  removeField: (fieldId: string) => void;
}

const TemplateGrid: React.FC<TemplateGridProps> = ({
  draggedSourceItem,
  droppableHighlight,
  fields,
  handleDragSourceItemOver,
  handleDropSourceItemOnGrid,
  handleLayoutChange,
  isDraggingSourceItem,
  layouts,
  removeField,
}) => {
  const formMethods = useFormContext<ChangeTemplate>();
  const { resetField } = formMethods;

  const calculateDropItem = useMemo(() => {
    if (!draggedSourceItem) {
      return undefined;
    }

    const { customFieldId, customFieldType } = draggedSourceItem;
    const baseItem = {
      i: `${DROP_ITEM_PREFIX}${customFieldId}`,
      w: GRID_COLS / 2,
      h: 1,
    };

    switch (customFieldType) {
      case TemplateCustomFieldTypeEnum.Enum.BREAK:
        return { ...baseItem, w: GRID_COLS, h: 3 };
      case TemplateCustomFieldTypeEnum.Enum.RICH_TEXT:
        return { ...baseItem, h: 3 };
      default:
        return baseItem;
    }
  }, [draggedSourceItem]);

  return (
    <Paper
      p='md'
      shadow='sm'
      radius='md'
      style={{
        minHeight: 500,
        backgroundColor: droppableHighlight ? DROP_ZONE_HIGHLIGHT_COLOR : DROP_ZONE_DEFAULT_COLOR,
        transition: 'background-color 0.2s ease-in-out',
      }}
      onDragOver={handleDragSourceItemOver}>
      <ResponsiveGridLayout
        layout={layouts}
        className='layout'
        compactType='vertical'
        useCSSTransforms
        containerPadding={[0, 0]}
        margin={[5, 5]}
        cols={GRID_COLS}
        rowHeight={ROW_HEIGHT}
        resizeHandles={['se']}
        draggableHandle={`.${DRAG_TAG}`}
        width={1200}
        style={{ minHeight: 500 }}
        onLayoutChange={handleLayoutChange}
        isDroppable={isDraggingSourceItem}
        droppingItem={calculateDropItem}
        onDrop={(layout, item, event) => {
          // Type guard
          handleDropSourceItemOnGrid(event as unknown as React.DragEvent, layout, item);
        }}>
        {fields.map((field: TemplateFieldItem, index: number) => {
          const layout = layouts.find((l) => l.i === field.customFieldId.toString());
          if (!layout) {
            return null;
          }

          return (
            <div key={layout.i} data-grid={layout}>
              <Paper className={styles.gridItemPaper}>
                <Group justify='flex-start' gap='0'>
                  {field.customFieldType === TemplateCustomFieldTypeEnum.Enum.BREAK && (
                    <Box mx='xs'>
                      <IconGripVertical size={16} style={{ opacity: 0.8 }} className={`${styles.dragHandle} ${DRAG_TAG}`} />
                    </Box>
                  )}
                  <Menu shadow='md'>
                    {field.customFieldType !== TemplateCustomFieldTypeEnum.Enum.BREAK && (
                      <Menu.Target>
                        <KanbanIconButton variant='subtle'>
                          <IconSettings size='1rem' stroke={1.5} />
                        </KanbanIconButton>
                      </Menu.Target>
                    )}

                    <Menu.Dropdown>
                      <Menu.Item key='requiredCb' leftSection={<RequiredCheckbox control={formMethods.control} name={`fields.${index}.required`} />}>
                        Mandatory
                      </Menu.Item>
                      <Menu.Item
                        key='delete'
                        leftSection={<IconX size={16} />}
                        color='red'
                        onClick={() => {
                          resetField(`fields.${index}.required`, { defaultValue: false });
                          removeField(field.customFieldId.toString());
                        }}>
                        Delete
                      </Menu.Item>
                    </Menu.Dropdown>
                  </Menu>
                </Group>

                <FieldRenderer
                  templateField={field}
                  fieldIndex={index}
                  register={formMethods.register}
                  errors={formMethods.formState.errors}
                  control={formMethods.control}
                />
              </Paper>
            </div>
          );
        })}
      </ResponsiveGridLayout>
    </Paper>
  );
};

export default TemplateGrid;
