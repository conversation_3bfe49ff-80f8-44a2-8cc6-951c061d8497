import React from 'react';
import { Flex, Box, Container } from '@mantine/core';
import { Outlet } from 'react-router-dom';
import { Sidebar } from './SideBarSelection';

export const Layout: React.FC = () => {
  return (
    <Flex h='100vh'>
      <Box w={60} style={{ transition: 'width 0.3s ease' }}>
        <Sidebar />
      </Box>
      <Container
        bg='white'
        fluid
        flex={1}
        pt='sm'
        pb='sm'
        style={{
          overflow: 'auto',
          wordWrap: 'break-word',
          wordBreak: 'break-word',
          whiteSpace: 'normal',
        }}>
        <Outlet />
      </Container>
    </Flex>
  );
};

export default Layout;
