import { AdvancedFilterMappingType, ColumnType, getDefaultTableAffected, KanbanIconButton, KanbanSwitch, KanbanTitle } from 'kanban-design-system';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanButton } from 'kanban-design-system';
import { KanbanTable, KanbanTableProps, TableAffactedSafeType } from 'kanban-design-system';
import { renderDateTime } from 'kanban-design-system';
import { Box, Flex, Tooltip } from '@mantine/core';
import { IconEdit, IconPlus, IconTrash } from '@tabler/icons-react';
import equal from 'fast-deep-equal';
import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import useFetch from '@core/hooks/useFetch';
import { UserApi } from '@api/UserApi';
import { User } from '@core/schema/User';
import useMutate from '@core/hooks/useMutate';
import dayjs from 'dayjs';
import { AmtDateRangeInput, DateRangeFilter, dateRangeKeys } from '@components/AmtDateRangeInput';
import { LocalStorageKey } from '@common/constants/LocalStorageKeyConstants';
import { buildDetailUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import { EntityAction } from '@common/constants/EntityActionConstants';
import { FilterTextInput } from '@components/AmtFilterTextInput';
import { deleteConfirm } from '@components/utils/ConfirmMessageUtils';
import { parseFilterWithDate } from '@common/utils/DateUtils';
import { UserModel } from '@models/UserModel';
import { useDisclosure } from '@mantine/hooks';
import AmtModal from '@components/AmtModal';
import { AmtShortTextLink } from '@components/AmtShortTextContent';
import { useSavedColumns } from '@core/hooks/useSavedColumns';
import useSavedFilterCycle from '@core/hooks/useSavedFilterCycle';

export type UserPageFilter = DateRangeFilter & {
  filterName?: string;
  filterUsername?: string;
  filterCenter?: string;
  filterDepartment?: string;
};

export const initOrUpdatedFilterPayloads = (prev?: TableAffactedSafeType<User>, initFilters?: UserPageFilter) => {
  const prevFilter = prev || getDefaultTableAffected();
  if (initFilters) {
    const filterObj: AdvancedFilterMappingType<User> = {
      ['name']: {
        filterOption: 'contains',
        value: {
          fromValue: initFilters.filterName,
        },
      },
      ['userName']: {
        filterOption: 'contains',
        value: {
          fromValue: initFilters.filterUsername,
        },
      },
      ['department']: {
        filterOption: 'contains',
        value: {
          fromValue: initFilters.filterDepartment,
        },
      },
      ['center']: {
        filterOption: 'contains',
        value: {
          fromValue: initFilters.filterCenter,
        },
      },
      ['modifiedDate']: {
        filterOption:
          initFilters.startDate && initFilters.endDate ? 'betweenInclusive' : initFilters.startDate ? 'greaterThanOrEqualTo' : 'lessThanOrEqualTo',
        value: {
          fromValue: initFilters.startDate ? dayjs(initFilters.startDate).format() : '',
          toValue: initFilters.endDate ? dayjs(initFilters.endDate).format() : '',
        },
      },
    };
    const advancedFilterMapping = { ...prevFilter.advancedFilterMapping, ...filterObj };
    return { ...prevFilter, advancedFilterMapping: { ...advancedFilterMapping } };
  }
  return prevFilter;
};

const columns: ColumnType<User>[] = [
  {
    name: 'userName',
    title: 'Username',
    customRender: (_, rowData) => {
      return (
        <AmtShortTextLink
          routePath={ROUTE_PATH.CONFIGURATION_USER_USERS_DETAIL}
          entityId={rowData.id}
          data={rowData.userName}
          fw={500}
          disableShorten
        />
      );
    },
  },
  {
    name: 'name',
    title: 'Full Name',
  },
  {
    name: 'description',
    title: 'Description',
    hidden: true,
  },
  {
    name: 'email',
    title: 'Email',
    hidden: true,
  },
  {
    name: 'phone',
    title: 'Phone',
    hidden: true,
  },
  {
    name: 'title',
    title: 'Title',
    hidden: true,
  },
  {
    name: 'department',
    title: 'Department',
  },
  {
    name: 'center',
    title: 'Center',
  },

  {
    name: 'status',
    title: 'Status',
    width: '5%',
    customRender: (_data, rowData) => <KanbanSwitch checked={rowData?.isActive} />,
    hidden: true,
  },
  {
    name: 'createdDate',
    title: 'Created Date',
    hidden: true,
    customRender: renderDateTime,
  },
  {
    name: 'createdBy',
    title: 'Created By',
    hidden: true,
  },
  {
    name: 'modifiedDate',
    title: 'Modified Date',
    customRender: renderDateTime,
  },
  {
    name: 'modifiedBy',
    title: 'Modified By',
    hidden: true,
  },
  {
    name: 'expired',
    title: 'Expired Date',
    customRender: (_data, rowData) => (rowData.expired ? renderDateTime(_data) : null),
    hidden: true,
  },
];
export const UserManagementPage = () => {
  const [totalRecords, setTotalRecords] = useState(0);
  const [users, setUsers] = useState<User[]>([]);

  const navigate = useNavigate();

  const [inputFilters, setInputFilters] = useState<UserPageFilter>({});

  const [savedFilters, setSavedFilters] = useSavedFilterCycle({
    key: LocalStorageKey.SEARCH_VALUE_ROLES_PAGE,
    deserialize: (str) => parseFilterWithDate<UserPageFilter>(str, dateRangeKeys),
    setInputFilters,
  });

  const [columnOrders, setColumnOrders] = useState<string[]>();

  const savedColumns = useSavedColumns<User>(LocalStorageKey.COLUMN_DISPLAY_USERS_PAGE, columns, columnOrders);

  const [tableAffectedChange, setTableAffectedChange] = useState<TableAffactedSafeType>();

  const [curentToDeleteMulti, setCurentToDeleteMulti] = useState<User[]>([]);
  const [openedModalConfirmDeleteMulti, { close: closeModalConfirmDeleteMulti, open: openModal }] = useDisclosure(false);

  useEffect(() => {
    setInputFilters({ ...savedFilters });
  }, [savedFilters]);

  const { data: usersResponse, refetch: refetchList } = useFetch(UserApi.findAll(initOrUpdatedFilterPayloads(tableAffectedChange, savedFilters)), {
    enabled: !!tableAffectedChange,
  });

  const handleSearch = useCallback(
    (filters?: UserPageFilter) => {
      const appliedFilters = filters ?? inputFilters;
      setSavedFilters((prev) => ({ ...prev, ...appliedFilters }));
      setTableAffectedChange((prev) => {
        return initOrUpdatedFilterPayloads(prev, appliedFilters);
      });
    },
    [inputFilters, setSavedFilters],
  );

  useEffect(() => {
    if (usersResponse?.data?.content) {
      setUsers(usersResponse.data.content);
      setTotalRecords(usersResponse.data.totalElements);
    }
  }, [usersResponse]);

  const { mutate: deleteMultiMutate } = useMutate(UserApi.deleteByUsernameIn, {
    successNotification: 'User(s) deleted successfully',
    onSuccess: () => {
      refetchList();
    },
  });

  const handleDeleteMulti = useCallback(
    (datas: UserModel[]) => {
      deleteMultiMutate(datas.map((it) => it.userName));
      setCurentToDeleteMulti([]);
      closeModalConfirmDeleteMulti();
    },
    [closeModalConfirmDeleteMulti, deleteMultiMutate],
  );

  const handleClearDateFilter = useCallback(() => {
    const toUpdate: DateRangeFilter = { startDate: undefined, endDate: undefined };

    setInputFilters((prev) => ({ ...prev, ...toUpdate }));
    setSavedFilters((prev) => ({ ...prev, ...toUpdate }));
  }, [setSavedFilters]);

  const handleInputFilterChange = (key: keyof typeof inputFilters, value: string) => {
    const updatedFilters = { ...inputFilters, [key]: value };
    setInputFilters(updatedFilters);
  };

  const handleClearInputFilter = (key: keyof typeof inputFilters) => {
    const updatedFilters = { ...inputFilters, [key]: '' };
    setInputFilters(updatedFilters);
    handleSearch(updatedFilters);
  };

  const tableProps: KanbanTableProps<User> = useMemo(() => {
    const tblProps: KanbanTableProps<User> = {
      columns: savedColumns,
      data: users,
      customRowProps: (data) => ({
        onDoubleClick: () => {
          navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_USER_USERS_DETAIL, data.id, EntityAction.EDIT));
        },
      }),
      columnOrderable: {
        /** Default is `true` */
        onOrder: (data) => {
          setColumnOrders(data.map((it) => it.name));
        },
      },
      serverside: {
        totalRows: totalRecords,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffectedChange, dataSet)) {
            setTableAffectedChange(dataSet);
          }
        },
      },
      actions: {
        customAction: (data) => {
          return <ActionColumn user={data} refetch={refetchList} />;
        },
      },
      selectableRows: {
        enable: true,
        customAction: (_, __) => (
          <KanbanButton leftSection={<IconTrash />} size='xs' bg='red' color='white' onClick={() => openModal()}>
            Delete(s)
          </KanbanButton>
        ),
        crossPageSelected: {
          rowKey: 'id',
          setSelectedRows: setCurentToDeleteMulti,
          selectedRows: curentToDeleteMulti,
        },
      },
      pagination: {
        enable: true,
      },
      showNumericalOrderColumn: true,
    };
    return tblProps;
  }, [savedColumns, users, totalRecords, curentToDeleteMulti, navigate, tableAffectedChange, refetchList, openModal]);

  return (
    <Box>
      <HeaderTitleComponent
        title=''
        rightSection={
          <Flex>
            <KanbanButton
              size='xs'
              leftSection={<IconPlus />}
              onClick={() => {
                navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_USER_USERS_DETAIL, 0, EntityAction.CREATE));
              }}>
              Add User
            </KanbanButton>
          </Flex>
        }
        leftSection={<KanbanTitle fz={'h4'}>Users</KanbanTitle>}
      />
      <Flex dir='row' gap='xs' align={'center'}>
        <FilterTextInput
          onClear={() => handleClearInputFilter('filterName')}
          placeholder='Full Name'
          value={inputFilters.filterName ?? ''}
          onChange={(val) => handleInputFilterChange('filterName', val.target.value)}
          onBlur={() => handleSearch(inputFilters)}
        />

        <FilterTextInput
          onClear={() => handleClearInputFilter('filterUsername')}
          placeholder='Username'
          value={inputFilters.filterUsername ?? ''}
          onChange={(val) => handleInputFilterChange('filterUsername', val.target.value)}
          onBlur={() => handleSearch(inputFilters)}
        />

        <FilterTextInput
          onClear={() => handleClearInputFilter('filterDepartment')}
          placeholder='Department'
          value={inputFilters.filterDepartment ?? ''}
          onChange={(val) => handleInputFilterChange('filterDepartment', val.target.value)}
          onBlur={() => handleSearch(inputFilters)}
        />

        <FilterTextInput
          onClear={() => handleClearInputFilter('filterCenter')}
          placeholder='Center'
          value={inputFilters.filterCenter ?? ''}
          onChange={(val) => handleInputFilterChange('filterCenter', val.target.value)}
          onBlur={() => handleSearch(inputFilters)}
        />

        <AmtDateRangeInput
          tooltipLabel='Modified Date'
          startDate={inputFilters.startDate}
          endDate={inputFilters.endDate}
          updateFilters={setInputFilters}
          onBlur={() => {
            handleSearch();
          }}
          onClear={handleClearDateFilter}
        />
      </Flex>
      <KanbanTable {...tableProps} title='' />
      <AmtModal
        opened={openedModalConfirmDeleteMulti}
        title={'Confirm delete user(s)'}
        onClose={() => {
          closeModalConfirmDeleteMulti();
        }}
        actions={
          <KanbanButton variant='filled' onClick={() => handleDeleteMulti(curentToDeleteMulti)}>
            Confirm
          </KanbanButton>
        }>
        {deleteConfirm(curentToDeleteMulti.map((it) => it.userName)).children}
      </AmtModal>
    </Box>
  );
};

const ActionColumn = ({ refetch, user }: { user: User; refetch: () => void }) => {
  const navigate = useNavigate();

  const { mutate: deleteMutate } = useMutate(UserApi.deleteByUsername, {
    successNotification: 'User deleted successfully',
    confirm: deleteConfirm(),
    onSuccess: () => refetch(),
  });

  return (
    <Flex gap='xs' align='center'>
      <Tooltip label='Edit'>
        <KanbanIconButton
          variant='transparent'
          size={'sm'}
          onClick={() => {
            navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_USER_USERS_DETAIL, user.id, EntityAction.EDIT));
          }}>
          <IconEdit />
        </KanbanIconButton>
      </Tooltip>

      <Tooltip label='Delete'>
        <KanbanIconButton
          variant='transparent'
          color='red'
          size={'sm'}
          onClick={() =>
            deleteMutate(user.userName, {
              confirm: deleteConfirm([user.userName]),
            })
          }>
          <IconTrash />
        </KanbanIconButton>
      </Tooltip>
    </Flex>
  );
};

export default UserManagementPage;
