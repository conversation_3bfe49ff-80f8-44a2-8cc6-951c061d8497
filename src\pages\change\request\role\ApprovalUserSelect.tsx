import React, { useCallback, useEffect, useState } from 'react';
import { Control, Controller, Noop, useFormContext, useWatch } from 'react-hook-form';
import { Box, ComboboxItem, ComboboxLikeRenderOptionInput, Group, Tooltip } from '@mantine/core';
import { ChangeRequestRoleList } from '@core/schema/ChangeRequestRole';
import useFetch from '@core/hooks/useFetch';
import { getDefaultTableAffected, KanbanMultiSelect, KanbanText, SortOrder } from 'kanban-design-system';
import { ChangeFlowNodeApi } from '@api/ChangeFlowNodeApi';
import { ChangeFlowNodeTypeEnum } from '@models/ChangeFlowModel';
import { EntityAction } from '@common/constants/EntityActionConstants';
import styled from '../ChangeRequestDetail.module.scss';
import { COMMON_MAX_DROP_DOWN_HEIGHT, COMMON_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { ChangeRoleUserReponse } from '@core/schema/User';
import { IconCheck } from '@tabler/icons-react';
import { ChangeRequestApprovalMode } from '@common/constants/ChangeRequestApprovalConstants';

interface ItemProps {
  control: Control<ChangeRequestRoleList>;
  roleIdx: number;
  cabGroupIdx: number;
  cabGroupUserIdx: number;
  changeFlowNodeId: number;
  mode: EntityAction;
  workflowIdx: number;
  modeApproval: ChangeRequestApprovalMode;
}

type UserComboboxItem = ComboboxItem & {
  isActive: boolean;
};
const ApprovalUserSelect: React.FC<ItemProps> = ({
  cabGroupIdx,
  cabGroupUserIdx,
  changeFlowNodeId,
  control,
  mode,
  modeApproval,
  roleIdx,
  workflowIdx,
}) => {
  const {
    clearErrors,
    formState: { errors },
    getValues,
    setError,
  } = useFormContext<ChangeRequestRoleList>();
  const [searchTerm, setSearchTerm] = useState('');
  const [modifiedSearch, setModifiedSearch] = useState(true);
  const [userOptions, setUserOptions] = useState<UserComboboxItem[]>();

  const [shouldFetchUser, setShouldFetchUser] = useState(true);
  const userPath = `roles.${roleIdx}.workflows.${workflowIdx}.groups.${cabGroupIdx}.users.${cabGroupUserIdx}` as const;
  const userNameFormPath = `${userPath}.username` as const;
  const displayUsername = getValues(`${userPath}.displayUsername`) || '';
  const roleName = getValues(`roles.${roleIdx}.changeFlowNode.name`);
  const roleType = useWatch({ control, name: `roles.${roleIdx}.changeFlowNode.type` });

  const { data: usersResponse } = useFetch(
    ChangeFlowNodeApi.findAllApprovalUsersById(changeFlowNodeId, {
      ...getDefaultTableAffected(),
      advancedFilterMapping: {
        ['name']: {
          filterOption: 'contains',
          value: {
            fromValue: searchTerm,
          },
        },
        ['username']: {
          filterOption: 'contains',
          value: {
            fromValue: searchTerm,
          },
        },
        ['email']: {
          filterOption: 'contains',
          value: {
            fromValue: searchTerm,
          },
        },
      },
      sortedBy: 'name',
      sortOrder: SortOrder.ASC,
      rowsPerPage: 200,
    }),
    {
      enabled: shouldFetchUser && modifiedSearch && !!changeFlowNodeId,
      showLoading: false,
    },
  );

  useEffect(() => {
    if (usersResponse?.data?.content) {
      const usersData = (usersResponse?.data?.content || [])?.map((r: ChangeRoleUserReponse) => ({
        value: r.userName,
        label: `${r.name} (${r.userName})`,
        isActive: r.isActive,
      }));
      const displayUsername = getValues(`roles.${roleIdx}.groupedCabUserGroups.${cabGroupIdx}.users.${cabGroupUserIdx}.displayUsername`) || '';
      const usernameActive = getValues(`roles.${roleIdx}.groupedCabUserGroups.${cabGroupIdx}.users.${cabGroupUserIdx}.usernameActive`);
      const username = getValues(userNameFormPath);
      if (!usersData.some((us) => us.value === username) && username) {
        usersData.unshift({ value: username, label: displayUsername || '', isActive: !!usernameActive });
      }

      setUserOptions(usersData);
      setShouldFetchUser(false);
      setModifiedSearch(false);
    }
  }, [cabGroupIdx, cabGroupUserIdx, displayUsername, getValues, roleIdx, userNameFormPath, userPath, usersResponse?.data?.content]);
  const handleValidate = useCallback(
    (_?: Noop) => {
      if (!getValues(userNameFormPath)) {
        setError(userNameFormPath, { message: 'Please fill in all the required fields!' });
      } else {
        clearErrors(userNameFormPath);
      }
    },
    [clearErrors, getValues, setError, userNameFormPath],
  );
  const fieldError = errors?.roles?.[roleIdx]?.workflows?.[workflowIdx]?.groups?.[cabGroupIdx]?.users?.[cabGroupUserIdx]?.username;
  const renderMultiSelectOption = useCallback(
    ({ checked, option }: ComboboxLikeRenderOptionInput<ComboboxItem>) => {
      const optionVal = userOptions?.find((it) => it.value === option.value);
      return (
        <Group gap={'xs'}>
          {checked && <IconCheck size={14} />}
          <KanbanText size='sm' c={optionVal?.isActive ? 'black' : 'red'}>
            {option.label}
          </KanbanText>
        </Group>
      );
    },
    [userOptions],
  );

  const getSelectedUserColor = useCallback(
    (username?: string | null) => {
      const optionVal = userOptions?.find((it) => it.value === username);
      return optionVal?.isActive ? 'black' : 'red';
    },
    [userOptions],
  );

  const handleChange = (value: any, field: any) => {
    value.length === 0 ? field.onChange('') : value.length === 2 ? field.onChange(value[1]) : field.onChange(value[0]);
  };
  const isViewMode = EntityAction.VIEW === mode;
  return (
    <Box bg={ChangeFlowNodeTypeEnum.Enum.CAB === roleType ? 'white' : ''}>
      <Group justify='flex-start' align='center' gap={8} mb={8}>
        <Tooltip disabled={ChangeFlowNodeTypeEnum.Enum.CAB === roleType} label={roleName} multiline maw={'30%'} position='top-start' openDelay={500}>
          <KanbanText className={styled.roleName} w={ChangeFlowNodeTypeEnum.Enum.CAB === roleType ? '' : '300'}>
            {ChangeFlowNodeTypeEnum.Enum.CAB === roleType || ChangeRequestApprovalMode.APPROVAL === modeApproval ? '' : roleName}
          </KanbanText>
        </Tooltip>
        <Controller
          control={control}
          name={userNameFormPath}
          render={({ field }) => (
            <>
              {userOptions && (
                <KanbanMultiSelect
                  mb={0}
                  styles={{
                    pill: {
                      color: getSelectedUserColor(field.value),
                    },
                    root: {
                      height: '100%',
                      flexGrow: 1,
                    },
                    wrapper: {
                      height: '40px',
                    },
                    input: {
                      height: '100%',
                    },
                  }}
                  renderOption={renderMultiSelectOption}
                  data={userOptions}
                  readOnly={isViewMode}
                  searchable
                  clearable={!isViewMode}
                  searchValue={searchTerm}
                  onSearchChange={(value) => {
                    setModifiedSearch(true);
                    setSearchTerm(value);
                  }}
                  value={field.value ? [field.value] : undefined}
                  onChange={(value) => handleChange(value, field)}
                  maxLength={COMMON_MAX_LENGTH}
                  maxDropdownHeight={COMMON_MAX_DROP_DOWN_HEIGHT}
                  style={{ flexGrow: 1 }}
                  onBlur={() => {
                    handleValidate(field.onBlur);
                  }}
                  onFocus={() => {
                    setShouldFetchUser(true);
                  }}
                  onClear={handleValidate}
                  error={fieldError ? fieldError.message : ''}
                />
              )}
            </>
          )}
        />
      </Group>
    </Box>
  );
};
export default ApprovalUserSelect;
