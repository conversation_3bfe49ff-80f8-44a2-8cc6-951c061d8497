.sidebarContainer {
  width: 280px;
  background-color: var(--mantine-color-gray-0);
}

.sidebarHeader {
  padding: var(--mantine-spacing-xs) var(--mantine-spacing-sm);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nodeList {
  padding: var(--mantine-spacing-sm);
}

.nodeItem {
  border: 1px solid var(--mantine-color-blue-6);
  background-color: var(--mantine-color-blue-0);
  border-radius: var(--mantine-radius-md);
  padding: var(--mantine-spacing-sm) var(--mantine-spacing-md);
  box-shadow: var(--mantine-shadow-xs);
  cursor: grab;
  font-weight: 500;
  text-align: center;
  user-select: none;
  transition: background-color 150ms ease;

  &:hover {
    background-color: var(--mantine-color-blue-2);
  }
}

.divider {
  margin: var(--mantine-spacing-sm) 0;
}

.approvalNode,
.cabNode {
  padding: var(--mantine-spacing-sm);
  box-shadow: var(--mantine-shadow-xs);
  text-align: center;
  font-weight: 500;
  cursor: grab;
  border-radius: var(--mantine-radius-sm);
}

.approvalNode {
  background-color: var(--mantine-color-orange-0);
  border: 1px solid var(--mantine-color-orange-6);
}

.cabNode {
  background-color: var(--mantine-color-violet-0);
  border: 1px solid var(--mantine-color-violet-6);
}