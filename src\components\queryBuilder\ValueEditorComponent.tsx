import React, { useEffect, useState } from 'react';
import { QueryBuilderOperatorEnum } from './QueryBuilderOperatorEnum';
import { ValueEditorProps } from 'react-querybuilder';
import { KanbanInput, KanbanTagsInput, KanbanNumberInput, KanbanSelect } from 'kanban-design-system';
import { ComboboxData } from '@mantine/core';
import { isArray, isNumber } from 'lodash';
import MultipleSelect from '@components/MultipleSelect';
import { MantineValueEditor } from '@react-querybuilder/mantine';
export type CustomValueEditorProps = ValueEditorProps & { isViewMode?: boolean };
type ValueTextCustomProps = {
  value: string;
  isViewMode?: boolean;
  onChange: (value: string) => void;
};

const VALUE_MAX_LENGTH = 300;
const TAG_MAX_LENGTH = 100;
const VALUE_NUMBER_MAX_LENGTH = 1_000_000_000_000_000;
const VALUE_NUMBER_MIN_LENGTH = -1_000_000_000_000_000;
const ValueTextCustom: React.FC<ValueTextCustomProps> = ({ isViewMode, onChange, value }) => {
  const [content, setContent] = useState<string>(value);
  useEffect(() => {
    if (!value) {
      setContent('');
    }
  }, [value]);

  return (
    <KanbanInput
      disabled={isViewMode}
      mt={'sm'}
      value={content || ''}
      placeholder='Enter value'
      maxLength={VALUE_MAX_LENGTH}
      onChange={(event) => {
        const val = event.currentTarget.value;
        setContent(val);
      }}
      onBlur={() => {
        onChange(content);
      }}
    />
  );
};
type ValueNumberCustomProps = {
  value: number;
  isViewMode?: boolean;
  onChange: (value: number) => void;
};
const ValueNumberCustom: React.FC<ValueNumberCustomProps> = ({ isViewMode, onChange, value }) => {
  const [content, setContent] = useState<number>(Number(value) || 0);
  useEffect(() => {
    if (!value) {
      setContent(0);
    }
  }, [value]);
  return (
    <KanbanNumberInput
      disabled={isViewMode}
      mt={'sm'}
      value={content || ''}
      placeholder='Enter number'
      max={VALUE_NUMBER_MAX_LENGTH}
      min={VALUE_NUMBER_MIN_LENGTH}
      clampBehavior='strict'
      decimalScale={2}
      onChange={(value) => {
        const val = Number(value) || 0;
        setContent(val);
      }}
      onBlur={() => {
        onChange(content);
      }}
    />
  );
};

type ValueTagsCustomProps = {
  value: string[];
  isViewMode?: boolean;
  onChange: (value: string[]) => void;
};

const ValueTagsCustom: React.FC<ValueTagsCustomProps> = ({ isViewMode, onChange, value }) => {
  const [content, setContent] = useState<string[]>(value);
  const [searchValue, setSearchValue] = useState<string>('');
  useEffect(() => {
    if (!value) {
      setContent([]);
    }
  }, [value]);
  return (
    <KanbanTagsInput
      disabled={isViewMode}
      mt={'sm'}
      mah={100}
      value={Array.isArray(content) ? content : []}
      onChange={(val) => setContent(val)}
      onBlur={() => {
        onChange(content);
      }}
      maxLength={VALUE_MAX_LENGTH}
      maxTags={TAG_MAX_LENGTH}
      placeholder='Enter substring'
      withScrollArea={true}
      pointer={false}
      onDuplicate={(val) => {
        if (!content.includes(val)) {
          setContent((prev) => [...prev, val]);
          setSearchValue('');
        }
      }}
      searchValue={searchValue}
      onSearchChange={(val) => setSearchValue(val)}
    />
  );
};
const ValueEditorComponent = (props: CustomValueEditorProps) => {
  const { fieldData, handleOnChange, isViewMode, operator, type, value, values } = props;
  const { selectorComponent } = fieldData;
  if (selectorComponent && typeof selectorComponent === 'function') {
    return selectorComponent({ ...props });
  }
  switch (operator) {
    case QueryBuilderOperatorEnum.IS:
    case QueryBuilderOperatorEnum.IS_NOT:
    case QueryBuilderOperatorEnum.CONTAINS:
    case QueryBuilderOperatorEnum.DOES_NOT_CONTAIN:
    case QueryBuilderOperatorEnum.BEGINS_WITH:
    case QueryBuilderOperatorEnum.ENDS_WITH:
      if (isArray(value)) {
        handleOnChange('');
      }
      if (type === 'multiselect') {
        return (
          <KanbanSelect
            disabled={isViewMode}
            mt={'sm'}
            value={value}
            onChange={handleOnChange}
            data={values as ComboboxData}
            placeholder='Select values'
            searchable
          />
        );
      }
      return <ValueTextCustom isViewMode={isViewMode} value={value || ''} onChange={handleOnChange} />;
    case QueryBuilderOperatorEnum.IS_ONE_OF:
    case QueryBuilderOperatorEnum.IS_NOT_ONE_OF:
      if (!isArray(value)) {
        handleOnChange([]);
      }
      if (type === 'multiselect') {
        return (
          <MultipleSelect
            withScrollArea={true}
            disabled={isViewMode}
            mt={'sm'}
            value={Array.isArray(value) ? value : []}
            onChange={handleOnChange}
            data={values as ComboboxData}
            placeholder='Select values'
            searchable
          />
        );
      } else {
        return <ValueTagsCustom isViewMode={isViewMode} value={value || []} onChange={handleOnChange} />;
      }
    case QueryBuilderOperatorEnum.GREATER_THAN:
    case QueryBuilderOperatorEnum.GREATER_THAN_OR_EQUAL:
    case QueryBuilderOperatorEnum.LESS_THAN:
    case QueryBuilderOperatorEnum.LESS_THAN_OR_EQUAL: {
      if (!isNumber(value)) {
        handleOnChange(0);
      }
      return <ValueNumberCustom isViewMode={isViewMode} value={value || ''} onChange={handleOnChange} />;
    }
    case QueryBuilderOperatorEnum.IS_NULL:
    case QueryBuilderOperatorEnum.IS_NOT_NULL:
    case QueryBuilderOperatorEnum.IS_NULL_OR_EMPTY:
    case QueryBuilderOperatorEnum.IS_NOT_NULL_OR_NOT_EMPTY:
      return <KanbanInput disabled={true} mt={'sm'} />;
    default:
      return <MantineValueEditor disabled={isViewMode} {...props} />;
  }
};

export default ValueEditorComponent;
