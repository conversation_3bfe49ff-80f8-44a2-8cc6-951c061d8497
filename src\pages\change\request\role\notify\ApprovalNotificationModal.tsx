import React, { useCallback, useState } from 'react';
import AmtModal from '@components/AmtModal';
import { KanbanButton } from 'kanban-design-system';
import { Box } from '@mantine/core';
import { ApprovalNotificationForm } from '@pages/change/request/role/notify/ApprovalNotificationForm';
import { ApprovalNotificationFormModel, ApprovalNotificationFormSchema } from '@models/ApprovalNotificationFormModel';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import useMutate from '@core/hooks/useMutate';
import { ChangeRequestApi } from '@api/ChangeRequestApi';
import { useDisclosure } from '@mantine/hooks';
import { MappedUser } from '../ChangeRequestDetailRole';
import { changePermissionConfigs } from '@pages/change/changeList';
import { useCheckPermissons } from '@core/hooks/useCheckPermissons';
import { ChangeStageType, ChangeStageTypeEnum } from '@common/constants/ChageStageType';

interface ApprovalNotificationModalProps {
  changeRequestId: number;
  changeFlowNodeId: number;
  selectedUsers: MappedUser[];
  stage?: ChangeStageType;
}

export const DEFAULT_APPROVAL_NOTIFICATION_FORM: ApprovalNotificationFormModel = {
  id: 0,
  name: '',
  subject: '',
  content: '',
  type: undefined,
  changeRequestId: 0,
  changeFlowNodeId: 0,
  usernames: [],
};

export const ApprovalNotificationModal: React.FC<ApprovalNotificationModalProps> = ({ ...props }) => {
  const [openedModalNotification, { close: closeModalNotification, open: openModalNotification }] = useDisclosure(false);
  const canEditChange = useCheckPermissons([changePermissionConfigs.edit]);
  const [approvalNotificationForm] = useState<ApprovalNotificationFormModel>({
    ...DEFAULT_APPROVAL_NOTIFICATION_FORM,
    usernames: props.selectedUsers.map((user) => user.username ?? ''),
    changeRequestId: props.changeRequestId,
    changeFlowNodeId: props.changeFlowNodeId,
  });
  const form = useForm<ApprovalNotificationFormModel>({
    defaultValues: approvalNotificationForm,
    resolver: zodResolver(ApprovalNotificationFormSchema),
    mode: 'onChange',
  });

  const { mutate: saveMutate } = useMutate(ChangeRequestApi.sendApprovalEmail, {
    successNotification: 'Send approval email successfully',
    onSuccess: () => {
      closeModalNotification();
    },
  });

  const handleSave = useCallback(() => {
    let { content, subject } = form.getValues();

    if (Array.isArray(content)) {
      content = JSON.stringify(content);
    }
    if (Array.isArray(subject)) {
      subject = JSON.stringify(subject);
    }

    if (!content || !subject) {
      form.setValue('content', content);
      form.setValue('subject', subject);
      form.trigger();
      return;
    }

    const payload = {
      ...form.getValues(),
      subject: subject,
      content: content,
    };

    saveMutate(payload);
  }, [form, saveMutate]);
  return (
    <>
      <KanbanButton
        size='xs'
        variant='light'
        onClick={openModalNotification}
        disabled={!props?.selectedUsers?.length || !canEditChange || props?.stage === ChangeStageTypeEnum.Enum.SUBMISSION_PLANNING}>
        Send For Approval
      </KanbanButton>
      <AmtModal
        size='80%'
        opened={openedModalNotification}
        onClose={closeModalNotification}
        title='Send For Approval'
        centered
        actions={
          <KanbanButton
            variant='filled'
            onClick={() => {
              handleSave();
            }}>
            Send
          </KanbanButton>
        }>
        <Box onClick={(e) => e.stopPropagation()} onDoubleClick={(e) => e.stopPropagation()}>
          <ApprovalNotificationForm {...props} form={form} />
        </Box>
      </AmtModal>
    </>
  );
};
