import { INPUT_REQUIRE } from '@core/message/MesageConstant';
import { EmailTemplateDetailSchema } from '@models/EmailTemplateModel';
import { z } from 'zod';

export const ApprovalNotificationFormSchema = EmailTemplateDetailSchema.extend({
  changeRequestId: z.number(),
  changeFlowNodeId: z.number(),
  usernames: z.array(z.string().min(1)).min(1, { message: INPUT_REQUIRE }),
});

export type ApprovalNotificationFormModel = z.infer<typeof ApprovalNotificationFormSchema>;
