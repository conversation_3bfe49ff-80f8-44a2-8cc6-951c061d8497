import { z } from 'zod';
import { AclPermissionSchema } from './AclPermission';

export const UserSchema = z.object({
  id: z.number(),
  userName: z.string(),
  name: z.string(),
  email: z.string(),
  phone: z.string().nullish(),
  title: z.string().nullish(),
  description: z.string().nullish(),
  department: z.string().nullish(),
  center: z.string().nullish(),
  expired: z.string().nullish(),
  isActive: z.boolean(),
  isAdmin: z.boolean(),
  createdDate: z.string(),
  createdBy: z.string().optional(),
  modifiedBy: z.string().optional(),
  modifiedDate: z.string(),
  aclPermissions: z.array(AclPermissionSchema).nullish(),
});

export const ChangeRoleUserReponseSchema = z.object({
  userName: z.string(),
  name: z.string(),
  email: z.string(),
  isActive: z.boolean(),
});
export type ChangeRoleUserReponse = z.infer<typeof ChangeRoleUserReponseSchema>;

export type User = z.infer<typeof UserSchema>;
