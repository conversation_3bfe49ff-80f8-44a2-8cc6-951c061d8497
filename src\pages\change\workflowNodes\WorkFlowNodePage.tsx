import { ChangeWorkflowNodeApi } from '@api/ChangeWorkflowNodeApi';
import { ChangeApplicationTypeEnum } from '@common/constants/ChangeApplicationConstants';
import { EntityAction } from '@common/constants/EntityActionConstants';
import { LocalStorageKey } from '@common/constants/LocalStorageKeyConstants';
import { parseFilterWithDate } from '@common/utils/DateUtils';
import { buildDetailUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import { DateRangeFilter, dateRangeKeys } from '@components/AmtDateRangeInput';
import { FilterTextInput } from '@components/AmtFilterTextInput';
import { AmtShortTextContent, AmtShortTextLink } from '@components/AmtShortTextContent';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { deleteConfirm } from '@components/utils/ConfirmMessageUtils';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import { useSavedColumns } from '@core/hooks/useSavedColumns';
import useSavedFilterCycle from '@core/hooks/useSavedFilterCycle';
import { ChangeWorkflowNode } from '@core/schema/ChangeWorkflowNode';
import { Box, Flex, Tooltip } from '@mantine/core';
import { IconEdit, IconPlus, IconTrash } from '@tabler/icons-react';
import equal from 'fast-deep-equal';
import {
  AdvancedFilterMappingType,
  ColumnType,
  getDefaultTableAffected,
  KanbanButton,
  KanbanIconButton,
  KanbanSelect,
  KanbanTable,
  KanbanTableProps,
  KanbanTitle,
  renderDateTime,
  TableAffactedSafeType,
} from 'kanban-design-system';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';

export type ChangeWorkflowNodePageFilter = DateRangeFilter & {
  nodeName?: string;
  application?: ChangeApplicationTypeEnum;
};

const initOrUpdatedFilterPayloads = (prev?: TableAffactedSafeType<ChangeWorkflowNode>, initFilters?: ChangeWorkflowNodePageFilter) => {
  const prevFilter = prev || getDefaultTableAffected();
  if (initFilters) {
    const filterObj: AdvancedFilterMappingType<ChangeWorkflowNode> = {
      ['nodeName']: {
        filterOption: 'contains',
        value: {
          fromValue: initFilters.nodeName,
        },
      },
      ['application']: {
        filterOption: 'equals',
        value: {
          fromValue: initFilters.application,
        },
      },
    };
    const advancedFilterMapping = { ...prevFilter.advancedFilterMapping, ...filterObj };
    return { ...prevFilter, advancedFilterMapping: { ...advancedFilterMapping } };
  }
  return prevFilter;
};

const columns: ColumnType<ChangeWorkflowNode>[] = [
  {
    name: 'nodeName',
    title: 'Node Name',
    customRender: (_data, rowData) => {
      return (
        <AmtShortTextLink
          routePath={ROUTE_PATH.CONFIGURATION_CHANGE_WORKFLOW_NODE_DETAIL}
          entityId={rowData.id}
          data={rowData.nodeName}
          disableShorten
          fw={500}
        />
      );
    },
  },
  {
    name: 'description',
    title: 'Description',
    customRender: (_data, rowData) => <AmtShortTextContent data={rowData.description || ''} />,
  },
  {
    name: 'authentication',
    title: 'Url',
    customRender: (data, _rowData) => {
      try {
        if (!data) {
          return '';
        }
        const jsonData = JSON.parse(data);
        return <AmtShortTextContent data={jsonData?.url || ''} />;
      } catch (error) {
        return '';
      }
    },
  },
  {
    name: 'application',
    title: 'Application',
  },
  {
    name: 'createdDate',
    title: 'Created Date',
    hidden: true,
    customRender: renderDateTime,
  },
  {
    name: 'createdBy',
    title: 'Created By',
    hidden: true,
  },
  {
    name: 'modifiedDate',
    title: 'Modified Date',
    hidden: true,
    customRender: renderDateTime,
  },
  {
    name: 'modifiedBy',
    title: 'Modified By',
    hidden: true,
  },
];

export const WorkFlowNodePage = () => {
  const [totalRecords, setTotalRecords] = useState(0);

  const navigate = useNavigate();

  const [tableAffectedChange, setTableAffectedChange] = useState<TableAffactedSafeType>();
  const [columnOrders, setColumnOrders] = useState<string[]>();
  const [inputFilters, setInputFilters] = useState<ChangeWorkflowNodePageFilter>({});
  const [savedFilters, setSavedFilters] = useSavedFilterCycle({
    key: LocalStorageKey.SEARCH_VALUE_CHANGE_WORKFLOW_NODE_PAGE,
    deserialize: (str) => parseFilterWithDate<ChangeWorkflowNodePageFilter>(str, dateRangeKeys),
    setInputFilters,
  });

  const savedColumns = useSavedColumns<ChangeWorkflowNode>(LocalStorageKey.COLUMN_DISPLAY_CHANGE_WORKFLOW_NODE_PAGE, columns, columnOrders);

  const { data, refetch: refetchList } = useFetch(
    ChangeWorkflowNodeApi.findAllWithPage(initOrUpdatedFilterPayloads(tableAffectedChange, savedFilters)),
    {
      enabled: !!tableAffectedChange,
    },
  );

  useEffect(() => {
    if (data?.data?.content) {
      setTotalRecords(data.data.totalElements);
    }
  }, [data?.data]);

  const handleSearch = useCallback(
    (filters?: ChangeWorkflowNodePageFilter) => {
      const appliedFilters = filters ?? inputFilters;
      setSavedFilters((prev) => ({ ...prev, ...appliedFilters }));
      setTableAffectedChange((prev) => {
        return initOrUpdatedFilterPayloads(prev, appliedFilters);
      });
    },
    [inputFilters, setSavedFilters],
  );

  const handleClearNameFilter = () => {
    const updatedFilters = { ...inputFilters, nodeName: '' };
    handleSearch(updatedFilters);
    setInputFilters(updatedFilters);
  };

  const handleApplicationChange = (val: string | null) => {
    const updatedFilters = {
      ...inputFilters,
      application: val as ChangeApplicationTypeEnum,
    };
    handleSearch(updatedFilters);
    setInputFilters(updatedFilters);
  };

  const tableProps: KanbanTableProps<ChangeWorkflowNode> = useMemo(() => {
    const tblProps: KanbanTableProps<ChangeWorkflowNode> = {
      columns: savedColumns,
      data: data?.data?.content ?? [],
      customRowProps: (data) => ({
        onDoubleClick: () => {
          navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_CHANGE_WORKFLOW_NODE_DETAIL, data.id, EntityAction.EDIT));
        },
      }),

      serverside: {
        totalRows: totalRecords,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffectedChange, dataSet)) {
            setTableAffectedChange(dataSet);
          }
        },
      },
      columnOrderable: {
        onOrder: (data) => {
          setColumnOrders(data.map((it) => it.name));
        },
      },
      actions: {
        customAction: (data) => {
          return <ActionColumn changeWorkflowNode={data} refetch={refetchList} />;
        },
      },
      pagination: {
        enable: true,
      },
      showNumericalOrderColumn: true,
    };
    return tblProps;
  }, [savedColumns, data?.data?.content, totalRecords, navigate, tableAffectedChange, refetchList]);

  return (
    <Box>
      <HeaderTitleComponent
        title=''
        rightSection={
          <Flex>
            <KanbanButton
              size='xs'
              leftSection={<IconPlus />}
              onClick={() => {
                navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_CHANGE_WORKFLOW_NODE_DETAIL, 0, EntityAction.CREATE));
              }}>
              Add Node
            </KanbanButton>
          </Flex>
        }
        leftSection={<KanbanTitle fz={'h4'}>Node List</KanbanTitle>}
      />
      <Flex dir='row' gap='xs' align={'center'}>
        <FilterTextInput
          onClear={handleClearNameFilter}
          placeholder='Node Name'
          value={inputFilters.nodeName ?? ''}
          onChange={(val) => {
            setInputFilters({ ...inputFilters, nodeName: val.target.value });
          }}
          onBlur={() => {
            handleSearch(inputFilters);
          }}
        />

        <Tooltip label='application' position='top-start'>
          <KanbanSelect
            mt='xs'
            size='xs'
            data={Object.values(ChangeApplicationTypeEnum).map((enumValue) => ({
              value: `${enumValue}`,
              label: `${enumValue}`,
            }))}
            placeholder='Application'
            clearable
            value={inputFilters.application ?? null}
            onChange={handleApplicationChange}
          />
        </Tooltip>
      </Flex>
      <KanbanTable {...tableProps} title='' />
    </Box>
  );
};

const ActionColumn = ({ changeWorkflowNode, refetch }: { changeWorkflowNode: ChangeWorkflowNode; refetch: () => void }) => {
  const navigate = useNavigate();

  const { mutate: deleteMutate } = useMutate(ChangeWorkflowNodeApi.deleteById, {
    successNotification: 'Node deleted successfully',
    confirm: deleteConfirm(),
    onSuccess: () => refetch(),
  });

  return (
    <Flex gap='xs' align='center'>
      <Tooltip label='Edit'>
        <KanbanIconButton
          variant='transparent'
          size={'sm'}
          onClick={() => {
            navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_CHANGE_WORKFLOW_NODE_DETAIL, changeWorkflowNode.id, EntityAction.EDIT));
          }}>
          <IconEdit />
        </KanbanIconButton>
      </Tooltip>

      <Tooltip label='Delete'>
        <KanbanIconButton
          variant='transparent'
          color='red'
          size={'sm'}
          onClick={() =>
            deleteMutate(changeWorkflowNode.id, {
              confirm: deleteConfirm([changeWorkflowNode.nodeName]),
            })
          }>
          <IconTrash />
        </KanbanIconButton>
      </Tooltip>
    </Flex>
  );
};

export default WorkFlowNodePage;
