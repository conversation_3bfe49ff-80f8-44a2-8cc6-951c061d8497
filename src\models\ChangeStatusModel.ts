import { ChangeStageTypeEnum } from '@common/constants/ChageStageType';
import { COMMON_DESCRIPTION_MAX_LENGTH, COMMON_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { INPUT_REQUIRE } from '@core/message/MesageConstant';
import { z } from 'zod';

export const ChangeStatusModelSchema = z
  .object({
    id: z.number().nullish(),
    name: z.string().trim().min(1, { message: INPUT_REQUIRE }),
    description: z.string().max(COMMON_DESCRIPTION_MAX_LENGTH).nullish(),
    action: z.string().max(COMMON_MAX_LENGTH).nullish(),
    stage: ChangeStageTypeEnum.optional(),
  })
  .superRefine((data, ctx) => {
    if (!data.stage || data.stage.trim() === '') {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: INPUT_REQUIRE,
        path: ['stage'],
      });
    }
  });
export type ChangeStatusModel = z.infer<typeof ChangeStatusModelSchema>;
