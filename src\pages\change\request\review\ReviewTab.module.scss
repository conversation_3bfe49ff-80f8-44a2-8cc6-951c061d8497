/* Table Input Field Consistent Spacing */
.tableInputField {
  padding-top: 8px;
}

/* Owner Field Styles */
.ownerFieldContainer {
  padding-top: 8px;
}

.ownerPill {
  &.disabled {
    background-color: var(--mantine-color-gray-1);
    color: var(--mantine-color-gray-6);
    opacity: 0.7;
  }

  &.enabled {
    background-color: #e3f2fd;
    color: inherit;
    opacity: 1;
  }
}

/* Document Field Styles */
.documentFieldContainer {
  padding-top: 8px;
}

.documentButton {
  &.disabled {
    opacity: 0.7;
    background-color: var(--mantine-color-gray-0);
  }

  &.enabled {
    opacity: 1;
    background-color: inherit;
  }
}

.downloadButton {
  &.disabled {
    opacity: 0.7;
  }

  &.enabled {
    opacity: 1;
  }
}

/* Approver MultiSelect Styles */
.approverMultiSelect {
  width: 100%;

  :global(.mantine-MultiSelect-input) {
    font-size: 12px;
  }
}

/* Approver Field Styles */
.approverFieldContainer {
  padding-top: 8px;
}

.approverComboboxLoadMore {
  width: 100%;

  :global(.mantine-PillsInput-input) {
    font-size: 12px;
  }

  :global(.mantine-Combobox-dropdown) {
    max-height: 200px;
  }

  /* Status-based styling for ComboboxLoadMore pills */
  :global(.mantine-Pill-root) {
    font-size: 12px;
    font-weight: 500;

    /* Default/Pending status - neutral blue */
    &[data-status="pending"], &:not([data-status]) {
      background-color: var(--mantine-color-blue-0) !important;
      color: var(--mantine-color-blue-9) !important;
      border: 1px solid var(--mantine-color-blue-3) !important;
    }

    /* Accepted status - green */
    &[data-status="accepted"] {
      background-color: var(--mantine-color-green-0) !important;
      color: var(--mantine-color-green-9) !important;
      border: 1px solid var(--mantine-color-green-3) !important;
    }

    /* Rejected status - red */
    &[data-status="rejected"] {
      background-color: var(--mantine-color-red-0) !important;
      color: var(--mantine-color-red-9) !important;
      border: 1px solid var(--mantine-color-red-3) !important;
    }
  }
}

/* Approver Pills Styles */
.approverPill {
  font-size: 12px;
  font-weight: 500;

  /* Default/Pending status - neutral blue */
  &.pending {
    background-color: var(--mantine-color-blue-0);
    color: var(--mantine-color-blue-9);
    border: 1px solid var(--mantine-color-blue-3);
  }

  /* Accepted status - green */
  &.accepted {
    background-color: var(--mantine-color-green-0);
    color: var(--mantine-color-green-9);
    border: 1px solid var(--mantine-color-green-3);
  }

  /* Rejected status - red */
  &.rejected {
    background-color: var(--mantine-color-red-0);
    color: var(--mantine-color-red-9);
    border: 1px solid var(--mantine-color-red-3);
  }
}

/* No Approvers Input Styles */
.noApproversInput {
  width: 100%;

  :global(.mantine-Input-input) {
    font-size: 12px;
  }
}

/* Note Field Styles */
.noteFieldContainer {
  padding-top: 8px;
}

.noteSection {
  background-color: #fff;
  border-color: #e0e0e0;
}

.noteTitle {
  margin-bottom: 0;
  color: #333;
  font-weight: 600;
}

.noteTextarea {
  :global(.mantine-Textarea-input) {
    font-size: 14px;
  }
}

/* Modal Styles */
.modalTitle {
  margin-bottom: 0;
}

.modalTextarea {
  :global(.mantine-Textarea-input) {
    font-size: 14px;
  }
}
