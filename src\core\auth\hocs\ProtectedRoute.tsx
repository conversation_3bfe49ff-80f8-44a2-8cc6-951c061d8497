import { isAuthorized } from '@common/utils/AclPermissionUtils';
import { AclPermission } from '@core/schema/AclPermission';
import React from 'react';

type ProtectedRouteProps = {
  children: React.ReactNode;
  //One of those role
  requirePermissions: AclPermission[];
  userPermissions: AclPermission[];
  errorElement: React.ReactNode;
  allMatchPermissions?: boolean;
};

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  allMatchPermissions,
  children,
  errorElement,
  requirePermissions,
  userPermissions,
}) => {
  return <>{isAuthorized(userPermissions, requirePermissions, allMatchPermissions) ? children : errorElement}</>;
};
