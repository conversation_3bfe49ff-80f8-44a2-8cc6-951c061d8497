import React, { useEffect, useRef, useState } from 'react';
import { KanbanSelect, KanbanTabs, KanbanText } from 'kanban-design-system';

import { ChangeRquestTabsEnum } from '@common/constants/ChangeRequestConstants';
import ChangeRequestDetailRole, { ChangeRequestDetailRoleRef } from './role/ChangeRequestDetailRole';
import ChangeRequestDetailTab from './detail';
import { Box, Flex } from '@mantine/core';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { useLocation, useParams, useSearchParams } from 'react-router-dom';
import { EntityAction } from '@common/constants/EntityActionConstants';
import { WorkflowPage } from './workflow/WorkflowPage';
import { ChangeRequestModel } from '@core/schema/ChangeRequest';
import { ChangeStageType, ChangeStageTypeEnum } from '@common/constants/ChageStageType';
import { ChangeRequestApi } from '@api/ChangeRequestApi';
import useFetch from '@core/hooks/useFetch';
import StageProgress from '@pages/change/request/components/StageProgress';
import ErrorPage from '@pages/base/ErrorPage';
import { useCheckPermissons } from '@core/hooks/useCheckPermissons';
import { PermissionAction, PermissionActionModule } from '@common/constants/AclPermissionConstants';
import { ChangeStatusApi } from '@api/ChangeStatus';
import useMutate from '@core/hooks/useMutate';
import ChangeRequestDocumentTab from '@pages/change/request/document';
import { ReviewTab } from './review/ReviewTab';

export const DEFAULT_CHANGE_REQUEST: ChangeRequestModel = {
  id: 0,
  changeTemplateId: 0,
  templateName: '',
  description: '',
  title: '',
  stage: ChangeStageTypeEnum.Enum.SUBMISSION_PLANNING,
};
const ChangeRequestDetailPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const actionParam = searchParams.get('tab');
  const initTab = (actionParam ?? ChangeRquestTabsEnum.DETAIL) as ChangeRquestTabsEnum;
  const [activeTab, setActiveTab] = useState<ChangeRquestTabsEnum>(initTab);

  const [changeTemplateId, setChangeTemplateId] = useState<number>(0);
  const changeRequestId = Number(useParams().id);
  const { search } = useLocation();
  const queryParams = new URLSearchParams(search);
  const isCreateMode = queryParams.get('action') === EntityAction.CREATE;
  const isViewMode = queryParams.get('action') === EntityAction.VIEW;

  const canEditChange = useCheckPermissons([
    { module: PermissionActionModule.CHANGE_SDP, permission: PermissionAction.CHANGE_UPDATE },
    { module: PermissionActionModule.CHANGE_SDP, permission: PermissionAction.CHANGE_DELETE },
  ]);

  const detailQueryConfig = ChangeRequestApi.findWithId(changeRequestId);
  const { data: changeRequestResponse, error } = useFetch(detailQueryConfig, {
    enabled: !!changeRequestId,
  });

  const changeRequest = changeRequestResponse?.data || DEFAULT_CHANGE_REQUEST;
  const [currentStage, setCurrentStage] = useState<ChangeStageType>(ChangeStageTypeEnum.Enum.SUBMISSION_PLANNING);

  const { data: statusListResponse } = useFetch(ChangeStatusApi.findAllStatusInSameStageByStatusId(changeRequest.changeStatusId || 0), {
    enabled: true,
  });
  const roleTabRef = useRef<ChangeRequestDetailRoleRef>(null);

  const { mutate: updateChangeStatus } = useMutate(ChangeRequestApi.processChangeRequestCoordinatorTransition, {
    successNotification: `Process successfully`,
    onSuccess: (response) => {
      if (response.data?.stage) {
        setCurrentStage(response.data.stage);
        roleTabRef.current?.reload();
      }
    },
    errorNotification: (data) => ({ message: data.message }),
  });

  useEffect(() => {
    if (changeRequestResponse?.data?.stage) {
      setCurrentStage(changeRequestResponse.data.stage);
    }
  }, [changeRequestResponse]);

  if (changeRequestId !== 0) {
    if (!changeRequestResponse && !error) {
      return null;
    }
    if (error) {
      return <ErrorPage />;
    }
  }

  return (
    <Flex direction='column' justify='flex-end'>
      <HeaderTitleComponent
        title={''}
        rightSection={
          canEditChange && changeRequestId ? (
            <KanbanSelect
              mb={0}
              placeholder='Actions'
              data={statusListResponse?.data?.map((it) => ({ value: String(it.id || 'Unknow'), label: it.name }))}
              renderOption={(val) => {
                return (
                  <Box
                    h={'100%'}
                    w={'100%'}
                    onClick={() => {
                      updateChangeStatus({ changeRequestId, data: { changeStatusId: Number(val.option.value) } });
                    }}>
                    <KanbanText>{val.option.label} </KanbanText>
                  </Box>
                );
              }}
            />
          ) : (
            <></>
          )
        }
      />
      {isViewMode && (
        <Box w='100%' mx='auto' px='md' ta='center'>
          <StageProgress currentStage={currentStage} flowStages={changeRequest.flowStages || []} />
        </Box>
      )}

      <KanbanTabs
        configs={{
          value: activeTab,
          onChange: (value) => {
            if (value) {
              setActiveTab(value as ChangeRquestTabsEnum);
            }
          },
        }}
        tabs={{
          [ChangeRquestTabsEnum.DETAIL]: {
            content: <ChangeRequestDetailTab changeRequest={changeRequest} setChangeTemplateId={setChangeTemplateId} />,
            title: 'Change Detail',
          },
          [ChangeRquestTabsEnum.DOCUMENT]: {
            content: <ChangeRequestDocumentTab changeTemplateId={changeTemplateId} />,
            title: 'Documents',
            disabled: isCreateMode,
          },
          [ChangeRquestTabsEnum.WORKFLOW]: {
            content: <WorkflowPage />,
            title: 'Workflow',
            disabled: isCreateMode,
          },
          [ChangeRquestTabsEnum.CHANGE_ROLE]: {
            content: (
              <ChangeRequestDetailRole
                changeTemplateId={changeTemplateId}
                activeTab={activeTab}
                changeRequest={changeRequest}
                ref={roleTabRef}
                currentStage={currentStage}
              />
            ),
            title: 'Approval Levels',
            disabled: isCreateMode,
          },
          ...(currentStage === 'REVIEW_CLOSE' && {
            [ChangeRquestTabsEnum.REVIEW]: {
              content: <ReviewTab changeRequestId={changeRequestId} isViewMode={isViewMode} />,
              title: 'Review',
              disabled: isCreateMode,
            },
          }),
        }}
      />
    </Flex>
  );
};

export default ChangeRequestDetailPage;
