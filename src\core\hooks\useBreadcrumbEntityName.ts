// hooks/useBreadcrumbName.ts
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { setEntityName } from '@slices/BreadcrumbSlice';

export function useBreadcrumbEntityName(name?: string) {
  const dispatch = useDispatch();

  useEffect(() => {
    if (name) {
      dispatch(setEntityName(name));
    }
    return () => {
      dispatch(setEntityName(undefined));
    };
  }, [dispatch, name]);
}
