import { <PERSON>, Anchor, Button, Checkbox } from '@mantine/core';
import BulkAdd from './PicklistValueBulkAdd';
import { CustomFieldModel, PICK_LIST_OPTION_LENGTH, PicklistOption } from '@models/CustomField';
import { UseFormReturn, useWatch } from 'react-hook-form';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { IconCircleMinus } from '@tabler/icons-react';
import { KanbanTable, KanbanInput, KanbanIconButton, ColumnType, KanbanTableProps, KanbanText } from 'kanban-design-system';
import { OnDragEndResponder } from '@hello-pangea/dnd';
import { COMMON_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { VIRTUALIZER_ESTIMATE_SIZE } from '@common/constants/TableConsstants';

type Props = {
  form: UseFormReturn<CustomFieldModel>;
};
const DEFAULT_COLUMN_TITLE = 'Default';

const PicklistValueSection: React.FC<Props> = ({ form }) => {
  const isMultiple = form.watch('isMultiple') ?? false;
  const [showBulkAdd, setShowBulkAdd] = useState(false);
  const picklistOptions = useWatch({ control: form.control, name: 'picklistOptions' });
  const [tableData, setTableData] = useState<PicklistOption[]>([]);

  useEffect(() => {
    setTableData(picklistOptions || []);
  }, [picklistOptions]);

  const onChange = useCallback(
    (updated: PicklistOption[]) => {
      setTableData(updated);
      form.setValue('picklistOptions', updated);
    },
    [form, setTableData],
  );

  const handleValueChange = useCallback(
    (position: number, newValue: string) => {
      const updated = tableData.map((item) => (item.position === position ? { ...item, value: newValue } : item));
      onChange(updated);
    },
    [tableData, onChange],
  );

  const handleDefaultChange = useCallback(
    (position: number) => {
      const clickedItem = tableData.find((item) => item.position === position);

      const updated = tableData.map((item) => ({
        ...item,
        isDefault: item.position === position ? !clickedItem?.isDefault : false,
      }));

      onChange(updated);
    },
    [tableData, onChange],
  );

  const handleRemove = useCallback(
    (position: number) => {
      const updated = tableData
        .filter((item) => item.position !== position)
        .map((item, idx) => ({
          ...item,
          position: idx,
        }));
      onChange(updated);
      form.trigger('picklistOptions');
    },
    [tableData, onChange, form],
  );

  const handleReorder: OnDragEndResponder = useCallback(
    ({ destination, source }) => {
      if (!destination) {
        return;
      }

      const updated = [...tableData];
      const [movedItem] = updated.splice(source.index, 1);
      updated.splice(destination.index, 0, movedItem);

      const reordered = updated.map((item, idx) => ({ ...item, position: idx }));
      onChange(reordered);
    },
    [tableData, onChange],
  );

  const handleAddRow = () => {
    const newData = [
      ...tableData,
      {
        position: tableData.length,
        value: '',
        isDefault: false,
      },
    ];

    const reindexed = newData.map((item, index) => ({
      ...item,
      position: index,
    }));

    setTableData(reindexed);
    form.setValue('picklistOptions', reindexed);
  };

  const columns = useMemo((): ColumnType<PicklistOption>[] => {
    return [
      {
        name: 'isDefault',
        title: DEFAULT_COLUMN_TITLE,
        width: '5%',
        customRender: (_data, rowData) => (
          <Checkbox
            checked={rowData.isDefault}
            onChange={() => handleDefaultChange(rowData.position)}
            styles={{
              input: { borderRadius: '50%' },
            }}
          />
        ),
      },
      {
        name: 'value',
        title: 'Value',
        customRender: (_data, rowData) => (
          <KanbanInput
            maxLength={COMMON_MAX_LENGTH}
            value={rowData.value}
            onChange={(e) => handleValueChange(rowData.position, e.currentTarget.value)}
            onBlur={() => form.trigger(`picklistOptions.${rowData.position}.value`)}
            error={form.formState.errors.picklistOptions?.[rowData.position]?.value?.message}
          />
        ),
      },
      {
        name: 'action',
        title: '',
        width: 50,
        customRender: (_data, rowData) => (
          <KanbanIconButton onClick={() => handleRemove(rowData.position)} size='sm' color='red' variant='transparent'>
            <IconCircleMinus />
          </KanbanIconButton>
        ),
      },
    ];
  }, [form, handleDefaultChange, handleRemove, handleValueChange]);

  const tableProps: KanbanTableProps<PicklistOption> = useMemo(() => {
    const cols = isMultiple ? columns.filter((item) => item.title !== DEFAULT_COLUMN_TITLE) : columns;
    return {
      columns: cols,
      data: tableData,
    };
  }, [columns, isMultiple, tableData]);

  return (
    <>
      <Box mb='md'>
        {tableData.length < PICK_LIST_OPTION_LENGTH && (
          <Anchor component='button' size='sm' mb={8} onClick={() => setShowBulkAdd(true)}>
            Bulk add
          </Anchor>
        )}

        <BulkAdd form={form} tableData={tableData} setTableData={setTableData} opened={showBulkAdd} onClose={() => setShowBulkAdd(false)} />
        <KanbanText size='sm' c='dimmed' mt={4}>
          Please add at least one option. You can add up to {PICK_LIST_OPTION_LENGTH} options.
        </KanbanText>
      </Box>

      <KanbanTable
        virtualizer={{
          enable: true,
          estimateSize: () => VIRTUALIZER_ESTIMATE_SIZE,
        }}
        key={isMultiple ? 1 : 2}
        rowOrderable={{
          enable: true,
          onDragEnd: handleReorder,
          getNodeDraggingVirtual: (data) => {
            return <>{data.value}</>;
          },
        }}
        {...tableProps}
      />

      {tableData.length < PICK_LIST_OPTION_LENGTH && (
        <Button onClick={handleAddRow} variant='outline' color='blue' mb='md'>
          Add Row
        </Button>
      )}
    </>
  );
};

export default PicklistValueSection;
