$primary-color: var(--mantine-color-primary-7);
$border-color: var(--mantine-color-gray-3);
$text-color: var(--mantine-color-gray-7);

.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  background-color: var(--mantine-color-white);
  z-index: 1001;
  border-right: 1px solid $border-color;
  padding: var(--mantine-spacing-xs);
}

.logoContainer {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  width: 100%;
}

.menuItemWrapper {
  width: 100%;
}

.menuItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--mantine-spacing-xs);
  cursor: pointer;

  &.active {
    &[data-level="0"] {
      background-color: var(--mantine-color-gray-1);
      border-radius: var(--mantine-radius-md);
    }
  }
}

.navLinkWrapper {
  display: flex;
  align-items: center;
  width: 100%;
}

.navLink {
  display: flex;
  align-items: center;
  gap: var(--mantine-spacing-xs);
  text-decoration: none;
  color: $text-color;
  min-width: 0;
  width: 100%;
  transition: color 0.2s ease;

  &.active {
    color: $primary-color;
  }

  &:hover {
    color: $primary-color;
  }
}

.submenu {
  margin-top: var(--mantine-spacing-xs)/2;

  &[data-level="0"] {
    padding-left: var(--mantine-spacing-md);
  }

  &[data-level="1"] {
    padding-left: 27px;
  }
}