import { BaseURL } from '@common/constants/BaseUrl';
import { createRequest } from './Utils';
import { createPageSchema, createResponseSchema } from '@core/schema/Common';
import { TableAffactedSafeType } from 'kanban-design-system';
import { GroupRoleSchema, GroupSchema, UserGroupSchema, UserGroupSchemaSelect } from '@core/schema/Group';
import { GroupModel, UserGroupModel } from '@models/GroupRoleUserModel';
import { tableAffectedToMultiColumnFilterPaginationRequestModel } from '@common/utils/KanbanTableUtils';

export class GroupApi {
  static findAllGroups(data: TableAffactedSafeType) {
    return createRequest({
      url: `${BaseURL.group}`,
      method: 'POST',
      schema: createResponseSchema(createPageSchema(GroupSchema)),
      data: tableAffectedToMultiColumnFilterPaginationRequestModel(data),
      params: {},
    });
  }

  static getDetail(id: number) {
    return createRequest({
      url: `${BaseURL.group}/${id}`,
      method: 'GET',
      schema: createResponseSchema(GroupSchema),
    });
  }

  static getSameNameDetail(id: number, name?: string) {
    return createRequest({
      url: `${BaseURL.group}/${id}/check-name?name=${name}`,
      method: 'GET',
      schema: createResponseSchema(GroupSchema || null),
    });
  }
  static saveOrUpdate(data: GroupModel) {
    return createRequest({
      url: `${BaseURL.group}/${data.id}`,
      method: 'POST',
      schema: createResponseSchema(GroupSchema),
      data,
    });
  }

  //Group roles
  static findPagingRolesByGroupIdAndOtherRoles(groupId: number, data: TableAffactedSafeType) {
    return createRequest({
      url: `${BaseURL.group}/${groupId}/roles/all`,
      method: 'POST',
      schema: createResponseSchema(createPageSchema(GroupRoleSchema)),
      data: tableAffectedToMultiColumnFilterPaginationRequestModel(data),
    });
  }

  static findPagingRolesByGroupId(groupId: number, data: TableAffactedSafeType) {
    return createRequest({
      url: `${BaseURL.group}/${groupId}/roles`,
      method: 'POST',
      schema: createResponseSchema(createPageSchema(GroupRoleSchema)),
      data: tableAffectedToMultiColumnFilterPaginationRequestModel(data),
    });
  }

  //Group users
  static findPagingUsersByGroupIdAndOtherUsers(groupId: number, data: TableAffactedSafeType) {
    return createRequest({
      url: `${BaseURL.group}/${groupId}/users/all`,
      method: 'POST',
      schema: createResponseSchema(createPageSchema(UserGroupSchemaSelect)),
      data: tableAffectedToMultiColumnFilterPaginationRequestModel(data),
    });
  }

  static findPagingUsersByGroupId(groupId: number, data: TableAffactedSafeType<UserGroupModel>) {
    return createRequest({
      url: `${BaseURL.group}/${groupId}/users`,
      method: 'POST',
      schema: createResponseSchema(createPageSchema(UserGroupSchema)),
      data: tableAffectedToMultiColumnFilterPaginationRequestModel(data),
    });
  }

  //delete
  static deleteGroupByIds(data: number[]) {
    return createRequest({
      url: BaseURL.group,
      method: 'DELETE',
      schema: createResponseSchema(createPageSchema(GroupSchema)),
      data,
    });
  }
}
