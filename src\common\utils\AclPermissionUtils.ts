import {
  DEFAULT_RESOURCE_ID,
  DEFAULT_RESOURCE_PARENT_ID,
  DEFAULT_TYPE,
  PermissionAction,
  PermissionActionModule,
} from '@common/constants/AclPermissionConstants';
import { AclPermission } from '@core/schema/AclPermission';

//ft/role utils
export const isAuthorized = (userPermissions: AclPermission[], requirePermissions: AclPermission[], allMatchPermissions?: boolean) => {
  if (requirePermissions.length === 0) {
    return true;
  }

  const uniqueUserPermissions = userPermissions.map((it) => getAclPermissionId(it));

  const isSuperAdmin = userPermissions.some(
    (it) => PermissionActionModule.ADMINISTRATOR === it.module && PermissionAction.SUPER_ADMIN === it.permission,
  );

  const uniqueRequiredPermissions = requirePermissions.map((it) => getAclPermissionId(it));

  if (allMatchPermissions === undefined) {
    allMatchPermissions = true;
  }
  const isAuthorized = () =>
    allMatchPermissions
      ? uniqueRequiredPermissions.every((it) => uniqueUserPermissions.includes(it))
      : uniqueRequiredPermissions.some((it) => uniqueUserPermissions.includes(it));
  return isSuperAdmin || isAuthorized();
};

export const getAclPermissionId = (aclPermission: AclPermission) => {
  return `${aclPermission.permission}:${aclPermission.module}:${aclPermission.type || DEFAULT_TYPE}:${aclPermission.resourceId || DEFAULT_RESOURCE_ID}:${aclPermission.resourceParentId || DEFAULT_RESOURCE_PARENT_ID}`;
};
