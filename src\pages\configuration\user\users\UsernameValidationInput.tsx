import { useState, useEffect } from 'react';
import { UseFormReturn } from 'react-hook-form';
import React from 'react';
import useFetch from '@core/hooks/useFetch';
import { UserApi } from '@api/UserApi';
import { KanbanInput } from 'kanban-design-system';
import { UserModel } from '@models/UserModel';
import { USERNAME_REGEX } from '@common/constants/RegexConstant';

interface UsernameValidationInputProps {
  form: UseFormReturn<UserModel>;
  id: number;
}

const MIN_USERNAME_LENGTH = 3;
const MAX_USERNAME_LENGTH = 30;

export const UsernameValidationInput: React.FC<UsernameValidationInputProps> = ({ form, id }) => {
  const [usernameToCheck, setUsernameToCheck] = useState<string>();
  const [isUsernameTaken, setIsUsernameTaken] = useState<boolean | undefined>(undefined);

  const { data, refetch } = useFetch(UserApi.existsByUsername(id, usernameToCheck || ''), {
    enabled: !!usernameToCheck && USERNAME_REGEX.test(usernameToCheck),
  });

  const { clearErrors, setError } = form;

  useEffect(() => {
    if (data) {
      setIsUsernameTaken(Boolean(data.data));
    }
  }, [data]);

  useEffect(() => {
    if (isUsernameTaken) {
      setError('userName', { type: 'manual', message: 'This Username is existed' });
    } else {
      clearErrors('userName');
    }
  }, [isUsernameTaken, setError, clearErrors]);

  const handleUsernameBlur = (event: React.FocusEvent<HTMLInputElement>) => {
    const lowercased = event.target.value.trim().toLowerCase();
    if (lowercased && lowercased !== usernameToCheck) {
      form.setValue('userName', lowercased);
      setUsernameToCheck(lowercased);
      refetch();
    }
  };

  return (
    <KanbanInput
      disabled={!!id}
      withAsterisk
      label='Username'
      {...form.register('userName')}
      minLength={MIN_USERNAME_LENGTH}
      maxLength={MAX_USERNAME_LENGTH}
      onBlur={handleUsernameBlur}
      required
      error={form.formState.errors.userName?.message}
    />
  );
};
