import { BaseURL } from '@common/constants/BaseUrl';
import { TableAffactedSafeType } from 'kanban-design-system';
import { createResponseSchema, createPageSchema, ResponseData, Page } from '@core/schema/Common';
import { RequestConfig } from '@core/api';
import { ChangeRoleUserReponse, ChangeRoleUserReponseSchema } from '@core/schema/User';
import { PaginationRequest } from './Type';
import { tableAffectedToMultiColumnFilterPaginationRequestModel } from '@common/utils/TableUtils';

export class ChangeFlowNodeApi {
  static findAllApprovalUsersById(
    id: number,
    pagination: TableAffactedSafeType,
  ): RequestConfig<ResponseData<Page<ChangeRoleUserReponse>>, PaginationRequest> {
    return {
      url: `${BaseURL.changeflowNodes}/${id}/users`,
      method: 'POST',
      schema: createResponseSchema(createPageSchema(ChangeRoleUserReponseSchema)),
      data: tableAffectedToMultiColumnFilterPaginationRequestModel(pagination),
    };
  }
}
