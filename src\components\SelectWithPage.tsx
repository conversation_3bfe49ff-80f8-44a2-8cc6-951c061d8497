import { ActionIcon, Combobox, ComboboxItem, Flex, Loader, ScrollArea, useCombobox } from '@mantine/core';
import { IconCheck, IconX } from '@tabler/icons-react';
import { KanbanInput, useDebounceCallback } from 'kanban-design-system';
import React, { useEffect, useRef, useState } from 'react';

export type SelectWithPageProps = {
  handleScrollToBottom: () => void;
  options: ComboboxItem[];
  onChange?: (value?: string, data?: ComboboxItem) => void;
  onSearch?: (value?: string) => void;
  onBlur?: () => void;
  value?: ComboboxItem;
  isLoading?: boolean;
  mah?: number;
  label?: string;
  required?: boolean;
  disabled?: boolean;
  debounceTime?: number;
  rangeBottom?: number;
  isUnselectedWithSeach?: boolean;
  description?: string;
  readOnly?: boolean;
  error?: string;
  clearable?: boolean;
  onClear?: () => void;
};

export const SelectWithPage = (props: SelectWithPageProps) => {
  const {
    debounceTime,
    description,
    disabled,
    isLoading,
    isUnselectedWithSeach: isUnSelectedWithSeach,
    label,
    mah,
    rangeBottom,
    readOnly,
    required,
  } = props;

  const combobox = useCombobox({
    onDropdownClose: () => combobox.resetSelectedOption(),
  });

  const onSearch = (value: string) => {
    if (props.onSearch) {
      props.onSearch(value);
    }
  };

  const debouncedSearch = useDebounceCallback(onSearch, debounceTime ?? 300);

  const [value, setValue] = useState<ComboboxItem | undefined>(props.value);
  const [valueSearch, setValueSearch] = useState<string | undefined>(undefined);

  const viewport = useRef<HTMLDivElement>(null);
  const handleScrollPositionChange = () => {
    if (viewport?.current) {
      const { clientHeight, scrollHeight, scrollTop } = viewport.current;
      const isScrollToBottom = Math.abs(scrollHeight - (scrollTop + clientHeight + (rangeBottom || 0))) <= 1;
      if (isScrollToBottom) {
        props.handleScrollToBottom();
      }
    }
  };

  useEffect(() => {
    setValue(props.value);
  }, [props.value]);

  return (
    <Combobox
      store={combobox}
      readOnly={readOnly}
      onOptionSubmit={(val) => {
        setValueSearch(undefined);
        const optionSelected = props.options.find((obj) => obj.value === val);
        setValue(optionSelected);
        if (props.onChange) {
          props.onChange(val, optionSelected);
        }

        combobox.closeDropdown();
      }}>
      <Combobox.Target>
        <KanbanInput
          description={description}
          readOnly={readOnly}
          onBlur={() => {
            setValueSearch(undefined);
            if (props.onBlur) {
              props.onBlur();
            }
            if (combobox.dropdownOpened) {
              combobox.closeDropdown();
            }
          }}
          onChange={(val) => {
            setValueSearch(val.target.value);
            debouncedSearch(val.target.value);
            //when value search change reset value selected
            if (!combobox.dropdownOpened) {
              combobox.openDropdown();
            }
            if (props.onChange && isUnSelectedWithSeach) {
              props.onChange(undefined, undefined);
            }
          }}
          rightSection={
            isLoading ? (
              <Loader size={10} />
            ) : props.clearable && !readOnly && value ? (
              <ActionIcon
                variant='subtle'
                size='sm'
                onClick={(e) => {
                  e.stopPropagation();
                  setValue(undefined);
                  setValueSearch('');
                  props.onChange?.(undefined, undefined);
                  props.onClear?.();
                }}>
                <IconX size={14} />
              </ActionIcon>
            ) : (
              <Combobox.Chevron />
            )
          }
          onClick={!readOnly ? () => combobox.toggleDropdown() : undefined}
          value={valueSearch !== undefined ? valueSearch : value?.label || ''}
          label={label}
          required={required}
          disabled={disabled}
          error={props.error}
        />
      </Combobox.Target>

      <Combobox.Dropdown>
        <ScrollArea.Autosize mah={mah ? mah : 150} type='scroll' viewportRef={viewport} onScrollPositionChange={handleScrollPositionChange}>
          <Combobox.Options>
            {(!props.options || !props.options.length) && <Combobox.Empty> {isLoading ? 'Loading...' : 'Nothing found'}</Combobox.Empty>}
            {props.options.map((item) => (
              <Combobox.Option value={item.value} key={item.value}>
                <Flex align='center'>
                  {value?.value === item.value && <IconCheck size={16} style={{ marginRight: 8 }} />}
                  {item.label}
                </Flex>
              </Combobox.Option>
            ))}
          </Combobox.Options>
        </ScrollArea.Autosize>
      </Combobox.Dropdown>
    </Combobox>
  );
};
