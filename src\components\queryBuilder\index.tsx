import React, { ReactNode, useCallback, useMemo } from 'react';
import { QueryBuilderMantine } from '@react-querybuilder/mantine';
import { QueryBuilderDnD } from '@react-querybuilder/dnd';
import * as ReactDnD from 'react-dnd';
import * as ReactDndHtml5Backend from 'react-dnd-html5-backend';
import { Card, Flex, MantineProvider, ScrollArea, Space, Text } from '@mantine/core';
import QueryBuilder, {
  ActionWithRulesAndAddersProps,
  CombinatorSelectorProps,
  Field,
  findPath,
  FullCombinator,
  FullField,
  FullOperator,
  getParentPath,
  insert,
  move,
  Path,
  QueryBuilderProps,
  remove,
  RuleGroupType,
  RuleType,
  ValidationResult,
  ValueEditorProps,
} from 'react-querybuilder';
import { KanbanButton, KanbanIconButton, KanbanText, KanbanTitle } from 'kanban-design-system';
import { IconTrash, IconCirclePlus, IconInputSearch } from '@tabler/icons-react';
import 'react-querybuilder/dist/query-builder-layout.css';
import './Styles.scss';
import '@mantine/core/styles.css';
import { isArray, isEqual } from 'lodash';
import { QueryBuilderCombinatorEnum } from './QueryBuilderCombinatorEnum';
import ValueEditorComponent from './ValueEditorComponent';
import { OPERATOR_LABELS, QueryBuilderOperatorEnum } from './QueryBuilderOperatorEnum';
import { MoveDirectionEnum } from './MoveDirectionEnum';
import FieldSelector from './FieldSelector';
import { QueryRuleGroupType } from '@core/schema/RuleGroupCondition';
export type QueryBuilderField = Omit<Field, 'operators'> & {
  operators?: QueryBuilderOperatorEnum[];
  name: string;
  label: string;
  valueEditorType?: ValueEditorType;
};
export type QueryBuilderComponentProps = Omit<
  QueryBuilderProps<any, FullField, FullOperator, FullCombinator>,
  | 'onAddRule'
  | 'onAddGroup'
  | 'onMoveRule'
  | 'onMoveGroup'
  | 'onRemove'
  | 'onQueryChange'
  | 'onLog'
  | 'getDefaultField'
  | 'getDefaultOperator'
  | 'getDefaultValue'
  | 'getOperators'
  | 'getValueEditorType'
  | 'getValueEditorSeparator'
  | 'getValueSources'
  | 'getInputType'
  | 'getValues'
  | 'getRuleClassname'
  | 'getRuleGroupClassname'
  | 'context'
  | 'operators'
  | 'fields'
> & {
  onChange: (value: RuleGroupType<RuleType<string, string, any, string>, string>) => void;
  value: RuleGroupType<RuleType<string, string, any, string>, string>;
  baseRule?: RuleType;
  maxLevel?: number;
  fields: QueryBuilderField[];
  operators: string[];
  error?: string;
};
type OperatorOption = { name: string; label: string };
type ValueEditorType = 'multiselect';
export const EMPTY_RULE_GROUP: QueryRuleGroupType = { combinator: QueryBuilderCombinatorEnum.AND, rules: [] };
export const DEFAULT_BASE_RULE: RuleType = { field: '', operator: '=', value: '' };
const DEFAULT_MAX_LEVEL: number = 5;
const CustomCombinatorSelector: React.FC<CombinatorSelectorProps> = ({ value }) => <KanbanText>{value?.toUpperCase()}</KanbanText>;
const validator = (rule: RuleType): ValidationResult => {
  const { operator, value } = rule;
  let isValid = false;
  if (typeof value === 'string') {
    isValid = value.trim() !== '';
    if (
      operator === QueryBuilderOperatorEnum.IS_NOT_NULL ||
      operator === QueryBuilderOperatorEnum.IS_NOT_NULL_OR_NOT_EMPTY ||
      operator === QueryBuilderOperatorEnum.IS_NULL ||
      operator === QueryBuilderOperatorEnum.IS_NULL_OR_EMPTY
    ) {
      isValid = true;
    }
  } else if (Array.isArray(value)) {
    isValid = value.length > 0;
  } else if (typeof value === 'number') {
    isValid = !isNaN(value);
  }

  return {
    valid: isValid,
    reasons: isValid ? [] : ['This field is invalid or empty'],
  };
};

export const validateQuery = (query: RuleGroupType): boolean => {
  if (!query.rules || query.rules.length === 0) {
    return false;
  }
  return query.rules.every((rule) => {
    if ('rules' in rule) {
      return validateQuery(rule);
    } else {
      return validator(rule).valid;
    }
  });
};
// convert RuleGroup into string
const ruleToString = (rule: RuleType<string, string, any, string>, fields: QueryBuilderField[], operatorOptions: OperatorOption[]): ReactNode => {
  const field = fields.find((field) => field.name === rule.field);
  const fieldLabel = field?.label || rule.field;
  const operatorLabel = (operatorOptions.find((op) => op.name === rule.operator)?.label || rule.operator).toUpperCase();
  let valuePart: string | ReactNode = '';
  const fieldValues = field && isArray(field.values) ? field.values : [];

  if (
    rule.operator === QueryBuilderOperatorEnum.IS ||
    rule.operator === QueryBuilderOperatorEnum.IS_NOT ||
    rule.operator === QueryBuilderOperatorEnum.CONTAINS ||
    rule.operator === QueryBuilderOperatorEnum.DOES_NOT_CONTAIN
  ) {
    valuePart = `'${rule.value}'`;
    if (!isNaN(Number(rule.value)) && fieldValues) {
      const fieldValueLabel = fieldValues.find((v) => v.value === rule.value)?.label || rule.value;
      valuePart = `'${fieldValueLabel}'`;
    }
  } else if (rule.operator === QueryBuilderOperatorEnum.IS_ONE_OF || rule.operator === QueryBuilderOperatorEnum.IS_NOT_ONE_OF) {
    const values = String(rule.value).split(',');
    valuePart = `(${values.map((val) => `'${val.trim()}'`).join(', ')})`;
    if (field?.valueEditorType === 'multiselect' && field !== undefined && isArray(field?.values)) {
      valuePart = `(${values.map((val) => `'${fieldValues.find((v) => v.value === val)?.label}'`).join(', ')})`;
    }
  } else if (
    rule.operator === QueryBuilderOperatorEnum.IS_NULL ||
    rule.operator === QueryBuilderOperatorEnum.IS_NULL_OR_EMPTY ||
    rule.operator === QueryBuilderOperatorEnum.IS_NOT_NULL ||
    rule.operator === QueryBuilderOperatorEnum.IS_NOT_NULL_OR_NOT_EMPTY
  ) {
    valuePart = '';
  } else {
    valuePart = rule.value;
  }
  return (
    <Text component='span'>
      <Text component='span' fw='bold'>
        {fieldLabel}
      </Text>
      {` ${operatorLabel} `}
      <Text component='span' c='var(--mantine-color-green-9)'>
        {valuePart}
      </Text>
    </Text>
  );
};
export const groupToString = (
  group: RuleGroupType<RuleType<string, string, any, string>, string>,
  fields: QueryBuilderField[],
  operatorOptions: OperatorOption[],
): ReactNode => {
  if (group.rules.length === 0) {
    return <Text component='span'>No Criteria selected</Text>;
  }

  const ruleComponents: ReactNode[] = [];
  for (const rule of group.rules) {
    if ('rules' in rule) {
      const subGroupComponent = groupToString(rule as RuleGroupType<RuleType<string, string, any, string>, string>, fields, operatorOptions);
      ruleComponents.push(<Text component='span'>({subGroupComponent})</Text>);
    } else if (rule.field) {
      const ruleComponent = ruleToString(rule as RuleType<string, string, any, string>, fields, operatorOptions);
      ruleComponents.push(ruleComponent);
    }
  }

  const combinator = group.combinator.toUpperCase();
  return (
    <Text component='span'>
      {ruleComponents.map((component, index) => (
        <Text key={index} component='span'>
          {index > 0 && ` ${combinator} `}
          {component}
        </Text>
      ))}
    </Text>
  );
};

const QueryBuilderComponent: React.FC<QueryBuilderComponentProps> = ({
  baseRule = DEFAULT_BASE_RULE,
  controlElements,
  disabled,
  error,
  fields,
  maxLevel = DEFAULT_MAX_LEVEL,
  onChange,
  operators,
  value,
  ...otherProps
}) => {
  const operatorOptions = useMemo(() => operators.map((e) => ({ name: e, label: OPERATOR_LABELS[e as QueryBuilderOperatorEnum] })), [operators]);

  const fieldOptions = useMemo(() => {
    return fields.map((field) => ({
      ...field,
      validator: validator,
      operators: field.operators
        ? field.operators.map((e) => ({
            label: OPERATOR_LABELS[e as QueryBuilderOperatorEnum],
            name: e.toString(),
            id: e.toString(),
          }))
        : operatorOptions,
    })) satisfies Field[];
  }, [fields, operatorOptions]);

  const handleRemoveRule = useCallback(
    (level: number, path: Path, _currentRule: RuleType, parentPath: Path) => {
      if (path.length === 1 && path[0] === 0) {
        return;
      }
      if (level > 1 && (findPath(parentPath, value) as RuleGroupType).rules.length === 2) {
        const otherRule = [...parentPath, 1 - path[path.length - 1]];
        const movedQuery = move(value, otherRule, parentPath);
        parentPath = [...parentPath.slice(0, -1), parentPath[parentPath.length - 1] + 1];
        onChange(remove(movedQuery, parentPath));
        return;
      }
      onChange(remove(value, path));
    },
    [onChange, value],
  );

  const handleAddRule = useCallback(
    (combinator: QueryBuilderCombinatorEnum, path: Path, currentRule: RuleType) => {
      const parentPath = getParentPath(path);
      const parentGroup = findPath(parentPath, value) as RuleGroupType;
      const newGroup = { combinator, rules: [currentRule, baseRule] };
      const nextPath = [...parentPath, path[path.length - 1] + 1];
      if (value.combinator !== combinator && isArray(path) && path.length === 1 && value.rules.length === 1) {
        onChange({ rules: [currentRule, baseRule], combinator });
        return;
      }
      onChange(parentGroup.combinator === combinator ? insert(value, baseRule, nextPath) : insert(value, newGroup, path, { replace: true }));
    },
    [baseRule, onChange, value],
  );

  const MemoizedValueEditor = useCallback(
    (props: ValueEditorProps) => (
      <ScrollArea.Autosize w={800} mah={100}>
        <ValueEditorComponent {...props} isViewMode={!!disabled} />
      </ScrollArea.Autosize>
    ),
    [disabled],
  );

  const removeRuleAction: React.FC<ActionWithRulesAndAddersProps> = useCallback(
    ({ level, path, ruleOrGroup }) => {
      const currentRule = ruleOrGroup as RuleType;
      const parentPath = getParentPath(path);
      const isRootRule = isEqual(path, [0]);
      return (
        <>
          <KanbanIconButton
            variant='transparent'
            disabled={isRootRule || disabled}
            onClick={() => handleRemoveRule(level, path, currentRule, parentPath)}>
            <IconTrash color='red' size='1rem' />
          </KanbanIconButton>
          {level < maxLevel && (
            <>
              <KanbanButton
                disabled={disabled}
                leftSection={<IconCirclePlus size='1rem' />}
                variant='subtle'
                size='sm'
                miw='6rem'
                onClick={() => {
                  handleAddRule(QueryBuilderCombinatorEnum.AND, path, currentRule);
                }}>
                AND
              </KanbanButton>
              <KanbanButton
                disabled={disabled}
                variant='subtle'
                size='sm'
                miw='5rem'
                leftSection={<IconCirclePlus size='1rem' />}
                onClick={() => handleAddRule(QueryBuilderCombinatorEnum.OR, path, currentRule)}>
                OR
              </KanbanButton>
            </>
          )}
        </>
      );
    },
    [disabled, maxLevel, handleRemoveRule, handleAddRule],
  );
  const onMoveRule = useCallback(
    (
      rule: RuleType<string, string, any, string>,
      fromPath: Path,
      toPath: Path | MoveDirectionEnum,
      query: RuleGroupType,
    ): RuleGroupType | boolean => {
      try {
        let updatedQuery = { ...query };

        const parentPath = getParentPath(fromPath);
        const parentGroup = findPath(parentPath, updatedQuery) as RuleGroupType;
        if (!parentGroup) {
          throw new Error('Parent group not found.');
        }
        updatedQuery = move(updatedQuery, fromPath, toPath);
        if (fromPath.length === toPath.length && isEqual(getParentPath(fromPath), getParentPath(toPath as Path))) {
          return updatedQuery;
        }
        if (fromPath.length > 1 && parentGroup.rules.length === 2) {
          if (isArray(toPath) && toPath[toPath.length - 1] <= parentPath[toPath.length - 1]) {
            const otherPath = [...getParentPath(toPath), toPath[toPath.length - 1] + 1];
            for (let index = otherPath.length; index < fromPath.length; index++) {
              if (index === fromPath.length - 1) {
                otherPath.push(0);
              } else {
                otherPath.push(fromPath[index]);
              }
            }
            updatedQuery = move(updatedQuery, otherPath, MoveDirectionEnum.UP);
            const oldParentPath = [...otherPath.slice(0, -2), otherPath[otherPath.length - 2] + 1];
            for (let index = oldParentPath.length; index < parentPath.length; index++) {
              oldParentPath.push(parentPath[index]);
            }
            updatedQuery = remove(updatedQuery, oldParentPath);
          } else {
            const otherPath = [...parentPath, 0];
            updatedQuery = move(updatedQuery, otherPath, MoveDirectionEnum.DOWN);
            updatedQuery = remove(updatedQuery, parentPath);
          }
        }
        return updatedQuery;
      } catch (e) {
        console.error('Error in onMoveRule:', e);
        return false;
      }
    },
    [],
  );

  const customControlElements = useMemo(
    () => ({
      ...controlElements,
      removeRuleAction,
      addGroupAction: () => <Space h='xs' />,
      addRuleAction: null,
      removeGroupAction: null,
      combinatorSelector: CustomCombinatorSelector,
      valueEditor: MemoizedValueEditor,
      fieldSelector: FieldSelector,
    }),
    [controlElements, removeRuleAction, MemoizedValueEditor],
  );
  return (
    <>
      <ScrollArea.Autosize mah={1500}>
        <QueryBuilderDnD dnd={{ ...ReactDnD, ...ReactDndHtml5Backend }}>
          <MantineProvider>
            <QueryBuilderMantine>
              <QueryBuilder
                query={value}
                fields={fieldOptions}
                onQueryChange={onChange}
                showCombinatorsBetweenRules
                onMoveRule={onMoveRule}
                controlElements={customControlElements}
                disabled={disabled}
                controlClassnames={{ queryBuilder: 'justifiedLayout' }}
                operators={operatorOptions}
                {...otherProps}
              />
            </QueryBuilderMantine>
          </MantineProvider>
        </QueryBuilderDnD>
      </ScrollArea.Autosize>
      {error && <KanbanText c={'red'}>{error}</KanbanText>}
      <Card p='xs' mt='xs' bg='var(--mantine-color-gray-1)' radius='sm'>
        <KanbanTitle order={6}>
          <Flex align='center'>
            <IconInputSearch />
            Preview
          </Flex>
        </KanbanTitle>
        <KanbanText style={{ wordWrap: 'break-word', overflowWrap: 'break-word' }}>{groupToString(value, fields, operatorOptions)}</KanbanText>
      </Card>
    </>
  );
};

export default QueryBuilderComponent;
