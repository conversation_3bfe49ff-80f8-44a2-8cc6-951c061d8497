import React, { useState } from 'react';
import { FileButton, Popover, Tabs, TextInput } from '@mantine/core';
import { KanbanButton } from 'kanban-design-system';
import { IconFile, IconPlus } from '@tabler/icons-react';
import { ACCEPTED_FILE_TYPES, MAX_SIZE_BYTES_25 } from '@common/constants/ChangeRequestConstants';
import { Controller } from 'react-hook-form';
import { COMMON_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { ChangeRequestDocumentEnums } from '@models/ChangeRequestDocumentGroupModel';

type Props = {
  opened: boolean;
  nameDocument: string;
  setOpened: (o: boolean) => void;
  disableUpload?: boolean;
};

export const UploaderFilePopover = ({ disableUpload, nameDocument, opened, setOpened }: Props) => {
  const [linkDoc, setLinkDoc] = useState({
    documentName: '',
    documentUrl: '',
  });

  return (
    <Popover opened={opened} onChange={setOpened} width={300} position='bottom-start' trapFocus withArrow>
      <Popover.Target>
        <KanbanButton disabled={disableUpload} size='xs' variant='filled' leftSection={<IconPlus size={14} />} onClick={() => setOpened(!opened)}>
          Add a document
        </KanbanButton>
      </Popover.Target>
      <Popover.Dropdown>
        <Tabs defaultValue='link'>
          <Tabs.List grow>
            <Tabs.Tab value='link'>Embed Link</Tabs.Tab>
            <Tabs.Tab value='upload'>Upload file</Tabs.Tab>
          </Tabs.List>
          <Controller
            name={nameDocument}
            render={({ field }) => (
              <>
                <Tabs.Panel value='link' pt='xs'>
                  <TextInput
                    label='Document name'
                    value={linkDoc.documentName}
                    maxLength={COMMON_MAX_LENGTH}
                    onChange={(e) => setLinkDoc((prev) => ({ ...prev, documentName: e.target.value }))}
                    size='xs'
                  />
                  <TextInput
                    label='Document URL'
                    maxLength={COMMON_MAX_LENGTH}
                    value={linkDoc.documentUrl}
                    onChange={(e) => setLinkDoc((prev) => ({ ...prev, documentUrl: e.target.value }))}
                    size='xs'
                    mt='sm'
                  />
                  <KanbanButton
                    fullWidth
                    mt='md'
                    size='xs'
                    onClick={() => {
                      field.onChange({
                        ...field.value,
                        ...linkDoc,
                        type: ChangeRequestDocumentEnums.DocumentType.Enum.URL,
                      });
                      setOpened(false);
                    }}>
                    Link
                  </KanbanButton>
                </Tabs.Panel>
                <Tabs.Panel value='upload' pt='xs'>
                  <FileButton
                    accept={ACCEPTED_FILE_TYPES}
                    onChange={(file: File | null) => {
                      if (!file) {
                        return;
                      }

                      if (file.size > MAX_SIZE_BYTES_25) {
                        return;
                      }

                      field.onChange({
                        ...field.value,
                        tempId: crypto.randomUUID(),
                        type: ChangeRequestDocumentEnums.DocumentType.Enum.FILE,
                        documentName: file.name,
                        file,
                      });
                    }}>
                    {(props) => (
                      <KanbanButton {...props} leftSection={<IconFile size={20} />} fullWidth size='xs' variant='filled'>
                        Choose a file
                      </KanbanButton>
                    )}
                  </FileButton>
                </Tabs.Panel>
              </>
            )}
          />
        </Tabs>
      </Popover.Dropdown>
    </Popover>
  );
};
