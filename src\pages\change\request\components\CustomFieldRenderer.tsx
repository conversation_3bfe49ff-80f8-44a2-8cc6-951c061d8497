import React, { useCallback, useEffect, useRef } from 'react';
import { IconAlertCircle, IconCalendar } from '@tabler/icons-react';
import { useEditor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { Box, ComboboxItem, Group, Input, Tooltip } from '@mantine/core';
import { DateTimePicker } from '@mantine/dates';
import { RichTextEditor } from '@mantine/tiptap';
import { TemplateCustomFieldTypeEnum } from '@core/schema/ChangeTemplate';
import styles from '@resources/styles/ChangeTemplateBuilder.module.scss';
import {
  getDefaultTableAffected,
  KanbanInput,
  KanbanMultiSelect,
  KanbanNumberInput,
  KanbanSelect,
  KanbanText,
  KanbanTextarea,
  useDebounceCallback,
  useSafetyRenderStateRef,
} from 'kanban-design-system';

import { UseFormRegister, Control, Controller, FieldErrors, ControllerRenderProps, useFormContext, useWatch } from 'react-hook-form';
import { CustomFieldApi } from '@api/CustomFieldApi';
import { useState } from 'react';
import { DRAG_TAG } from '@common/constants/ChangeTemplateConstants';
import { PicklistOption } from '@models/CustomField';
import useInfiniteFetchCore from '@core/hooks/useInfiniteFetchCore';
import { FetchNextPageOptions, InfiniteData, InfiniteQueryObserverResult } from '@tanstack/react-query';
import { Page, ResponseData } from '@core/schema/Common';
import Underline from '@tiptap/extension-underline';
import { MAX_VALUE_MULTI_LINE_LENGTH, MAX_VALUE_NUMBER, MAX_VALUE_SINGLE_LINE_LENGTH } from '@common/constants/ValidationConstant';
import { useDebouncedValue } from '@mantine/hooks';
import { ChangeRequestFieldItem, ChangeRequestModel } from '@core/schema/ChangeRequest';
import { DebounceTime } from '@common/constants/DebounceTimeConstant';
import { DELAY_TIME_500S, NUMBER_COLUMN_PER_PAGE } from '@common/constants/CustomFieldRendererConstants';
import { DATE_TIME_AMPM_FORMAT } from '@common/constants/DateTimeConstants';
import clsx from 'clsx';

interface FieldRendererProps {
  changeRequestField: ChangeRequestFieldItem;
  fieldIndex: number;
  register: UseFormRegister<ChangeRequestModel>;
  errors: FieldErrors<ChangeRequestModel>;
  control: Control<ChangeRequestModel>;
  action: boolean;
}

const appendPickListFieldDataList = (prevList: PicklistOption[], newList?: PicklistOption[] | null) => {
  const combinedList = [...(prevList || []), ...(newList || [])];
  const filteredList = combinedList.filter((option) => option.value?.trim());
  filteredList.sort((a, b) => a.position - b.position);
  const uniqueMap = new Map<string, PicklistOption>();
  for (const option of filteredList) {
    uniqueMap.set(option.value, option);
  }
  return Array.from(uniqueMap.values()).map((option) => ({
    value: option.value,
    label: option.value,
  }));
};

const CustomFieldRenderer: React.FC<FieldRendererProps> = ({ action, changeRequestField, control, errors, fieldIndex: index, register }) => {
  const [isFocused, setIsFocused] = useState(false);
  const { trigger } = useFormContext<ChangeRequestModel>();
  const required = useWatch({ name: `fields.${index}.required` }) as boolean;
  const minConstraint: number = useWatch({ control, name: `fields.${index}.fieldConstraint.min` }) || 0;
  const maxConstraint: number = useWatch({ control, name: `fields.${index}.fieldConstraint.max` }) || 0;
  const handleValidate = useCallback(async () => {
    await Promise.all([trigger(`fields.${index}.fieldConstraint.defaultValue`), trigger(`fields.${index}.picklistOptions`)]);
  }, [index, trigger]);
  const [picklistFieldDataLst, setPicklistFieldDataLst] = useState<ComboboxItem[]>(
    appendPickListFieldDataList([], changeRequestField.picklistOptions),
  );
  const [searchPickList, setSearchPickList] = useState('');
  const [searchPickListDebounced] = useDebouncedValue(searchPickList, DebounceTime.MILLISECOND_300);
  const pageNum = useSafetyRenderStateRef(1);

  const { data: pickListFieldResponse, fetchNextPage } = useInfiniteFetchCore(
    CustomFieldApi.findPickListOptions(changeRequestField.customFieldId, changeRequestField.isReference || false, {
      ...getDefaultTableAffected(),
      page: pageNum.current,
      sortedBy: 'name',
      rowsPerPage: NUMBER_COLUMN_PER_PAGE,
      advancedFilterMapping: {
        name: {
          filterOption: 'contains',
          value: {
            fromValue: searchPickListDebounced,
          },
        },
      },
    }),
    {
      getPageParam: (requestConfig) => requestConfig.data?.page ?? 0,
      getNextPageParam: (lastPage) => {
        if (lastPage?.data?.number !== undefined && lastPage?.data?.totalPages !== undefined) {
          const nextPage = lastPage.data.number + 1;
          return nextPage < lastPage.data.totalPages ? nextPage : undefined;
        }
        return undefined;
      },
    },
    {
      enabled:
        changeRequestField.customFieldType === TemplateCustomFieldTypeEnum.enum.PICKLIST &&
        (changeRequestField.picklistOptions?.length === 0 || isFocused),
      refetchOnWindowFocus: false,
      showLoading: false,
    },
  );
  const handleLoadPickListFieldDataList = useCallback(() => {
    if (pickListFieldResponse && pickListFieldResponse.pages) {
      const pages = pickListFieldResponse.pages;

      setPicklistFieldDataLst((prev) => {
        return appendPickListFieldDataList(
          prev.map((it, index) => ({ value: it.value, position: index, isDefault: false })),
          pages.flatMap(
            (page) =>
              page.data?.content?.map((item) => ({
                ...item,
              })) || [],
          ),
        );
      });
    }
  }, [pickListFieldResponse]);
  const getFieldError = useCallback(
    (fieldValue: any) => {
      const constraint = errors.fields?.[index]?.fieldConstraint;
      let currentMaxConstraint = maxConstraint;
      const isMultiLine = changeRequestField.type === TemplateCustomFieldTypeEnum.Enum.MULTI_LINE;
      const isNumber = changeRequestField.type === TemplateCustomFieldTypeEnum.Enum.NUMBER;
      const isSingleLine = changeRequestField.type === TemplateCustomFieldTypeEnum.Enum.SINGLE_LINE;
      const maxConstraintDefault = isMultiLine
        ? MAX_VALUE_MULTI_LINE_LENGTH
        : isNumber
          ? MAX_VALUE_NUMBER
          : isSingleLine
            ? MAX_VALUE_SINGLE_LINE_LENGTH
            : MAX_VALUE_MULTI_LINE_LENGTH;

      if (maxConstraintDefault && (!currentMaxConstraint || currentMaxConstraint > maxConstraintDefault)) {
        currentMaxConstraint = maxConstraintDefault;
      }
      const fieldType = changeRequestField.type;
      const isHasError = !!constraint?.defaultValue?.message;
      const subjectDescription = fieldType === TemplateCustomFieldTypeEnum.Enum.NUMBER ? `Value range` : `Value length`;
      if (fieldValue !== undefined && fieldValue !== null && fieldValue !== '') {
        if (isNumber) {
          const num = Number(fieldValue);
          if (!isNaN(num)) {
            if (minConstraint !== null && currentMaxConstraint !== null && (num < minConstraint || num > currentMaxConstraint)) {
              return `Invalid value. ${subjectDescription} must be between ${minConstraint} and ${currentMaxConstraint}.`;
            }
            if (currentMaxConstraint !== null && num > currentMaxConstraint) {
              return `Invalid value. ${subjectDescription} must not exceed ${currentMaxConstraint}.`;
            }
            if (minConstraint !== null && num < minConstraint) {
              return `Invalid value. ${subjectDescription} must be at least ${minConstraint}.`;
            }
            if (currentMaxConstraint !== null && num > currentMaxConstraint) {
              return `Invalid value. ${subjectDescription} must not exceed ${currentMaxConstraint}.`;
            }
          }
        } else if (isSingleLine || isMultiLine) {
          const length = String(fieldValue).length;
          if (minConstraint !== null && currentMaxConstraint !== null && (length < minConstraint || length > currentMaxConstraint)) {
            return `Invalid value. ${subjectDescription} must be between ${minConstraint} and ${currentMaxConstraint}.`;
          }
          if (minConstraint !== null && length < minConstraint) {
            return `Invalid value. ${subjectDescription} must be at least ${minConstraint} characters.`;
          }
          if (currentMaxConstraint !== null && length > currentMaxConstraint) {
            return `Invalid value. ${subjectDescription} must not exceed ${currentMaxConstraint} characters.`;
          }
        }
      }
      if (!isHasError) {
        return '';
      }

      if (minConstraint === null && currentMaxConstraint !== null) {
        return `Invalid value. ${subjectDescription} must not exceed ${currentMaxConstraint}.`;
      }
      if (minConstraint !== null && currentMaxConstraint === null) {
        return `Invalid value. ${subjectDescription} must be at least ${minConstraint}.`;
      }
      if (minConstraint !== null && currentMaxConstraint !== null) {
        return `Invalid value. ${subjectDescription} must be between ${minConstraint} and ${currentMaxConstraint}.`;
      }

      return `Invalid value. ${subjectDescription} has an undefined range or length constraint.`;
    },

    [errors.fields, index, maxConstraint, changeRequestField.type, minConstraint],
  );
  useEffect(() => {
    handleLoadPickListFieldDataList();
  }, [handleLoadPickListFieldDataList]);

  useDebounceCallback(
    (opts?: FetchNextPageOptions): Promise<InfiniteQueryObserverResult<InfiniteData<ResponseData<Page<PicklistOption>>>>> => {
      return fetchNextPage(opts);
    },
    { delay: DELAY_TIME_500S, flushOnUnmount: true },
  );

  switch (changeRequestField.customFieldType) {
    case TemplateCustomFieldTypeEnum.enum.SINGLE_LINE:
      return (
        <Group className={`${styles.fieldGroup} `}>
          <FieldName field={changeRequestField} required={required} />
          <Controller
            name={`fields.${index}.fieldConstraint.defaultValue`}
            control={control}
            render={({ field, fieldState }) => {
              const errorMessage = getFieldError(field.value);
              const hasFormError = fieldState.error?.message;
              return (
                <KanbanInput
                  className={styles.inputField}
                  readOnly={action}
                  required={required}
                  {...field}
                  mb={0}
                  value={field.value || ''}
                  onBlur={() => {
                    field.onBlur?.();
                  }}
                  onChange={(e) => {
                    field.onChange(e);
                    if (hasFormError) {
                      handleValidate();
                    }
                  }}
                  error={hasFormError || !!errorMessage}
                  rightSection={!hasFormError && errorMessage ? <FieldError message={errorMessage} /> : null}
                />
              );
            }}
          />
        </Group>
      );
    case TemplateCustomFieldTypeEnum.enum.NUMBER:
      return (
        <Group className={`${styles.fieldGroup} `}>
          <FieldName field={changeRequestField} required={required} />
          <Controller
            name={`fields.${index}.fieldConstraint.defaultValue`}
            control={control}
            render={({ field, fieldState }) => {
              const errorMessage = getFieldError(field.value);
              const hasFormError = fieldState.error?.message;
              return (
                <KanbanNumberInput
                  className={styles.inputField}
                  readOnly={action}
                  required={required}
                  maxLength={10}
                  {...field}
                  mb={0}
                  value={field.value ? Number(field.value) : ''}
                  onBlur={() => {
                    field.onBlur();
                  }}
                  onChange={(value) => {
                    field.onChange(String(value));
                    if (hasFormError) {
                      handleValidate();
                    }
                  }}
                  error={hasFormError || !!errorMessage}
                  rightSection={!hasFormError && errorMessage ? <FieldError message={errorMessage} /> : null}
                />
              );
            }}
          />
        </Group>
      );
    case TemplateCustomFieldTypeEnum.enum.BREAK:
    case TemplateCustomFieldTypeEnum.enum.RICH_TEXT:
      return (
        <Controller
          name={`fields.${index}.fieldConstraint.defaultValue`}
          control={control}
          render={({ field: controllerField, fieldState }) => {
            const hasFormError = fieldState.error?.message;
            return (
              <RichTextFieldRenderer
                field={changeRequestField}
                readOnly={action}
                controllerField={controllerField}
                required={required}
                onBlur={() => {
                  controllerField.onBlur();
                }}
                onChange={(e) => {
                  controllerField.onChange(e);
                  if (hasFormError) {
                    handleValidate();
                  }
                }}
                error={hasFormError}
              />
            );
          }}
        />
      );

    case TemplateCustomFieldTypeEnum.enum.DATE:
      return (
        <Group align='center' justify='space-between' pr='xs' gap={'0'} className={styles.fieldGroup}>
          <FieldName field={changeRequestField} required={required} />

          <Controller
            name={`fields.${index}.fieldConstraint.defaultValue`}
            control={control}
            render={({ field: controllerField, fieldState }) => (
              <DateTimePicker
                valueFormat={DATE_TIME_AMPM_FORMAT}
                readOnly={action}
                className={styles.inputField}
                required={required}
                value={!controllerField.value || isNaN(Date.parse(controllerField.value)) ? null : new Date(controllerField.value)}
                clearable
                rightSection={
                  action ? (
                    <Group align='center' justify='flex-start'>
                      <IconCalendar size={18} stroke={1.5} />
                    </Group>
                  ) : undefined
                }
                onChange={(date) => {
                  controllerField.onChange(date ? date.toISOString() : null);
                  if (fieldState.error?.message) {
                    handleValidate();
                  }
                }}
                error={fieldState.error?.message}
              />
            )}
          />
        </Group>
      );

    case TemplateCustomFieldTypeEnum.enum.MULTI_LINE: {
      return (
        <Group align='center' justify='space-between' pr='xs' gap={'0'} className={styles.fieldGroup}>
          <FieldName field={changeRequestField} required={required} />
          <Controller
            name={`fields.${index}.fieldConstraint.defaultValue`}
            control={control}
            render={({ field, fieldState }) => {
              const errorMessage = getFieldError(field.value);
              const hasFormError = fieldState.error?.message;
              return (
                <KanbanTextarea
                  className={styles.inputField}
                  readOnly={action}
                  required={required}
                  styles={{
                    root: { height: '85%' },
                    wrapper: { height: '100%' },
                    input: { height: '100%' },
                  }}
                  {...field}
                  value={field.value || ''}
                  onBlur={() => {
                    field.onBlur?.();
                  }}
                  onChange={(value) => {
                    field.onChange(value);
                    if (hasFormError) {
                      handleValidate();
                    }
                  }}
                  error={hasFormError || !!errorMessage}
                  rightSection={!hasFormError && errorMessage ? <FieldError message={errorMessage} /> : null}
                />
              );
            }}
          />
        </Group>
      );
    }

    case TemplateCustomFieldTypeEnum.enum.PICKLIST: {
      return (
        <Group align='center' justify='space-between' pr='xs' gap={'0'} className={styles.fieldGroup}>
          <FieldName field={changeRequestField} required={required} />
          <Controller
            name={`fields.${index}.picklistOptions`}
            control={control}
            render={({ field: controllerField }) =>
              changeRequestField.isMultiple ? (
                <KanbanMultiSelect
                  data={picklistFieldDataLst}
                  searchable
                  readOnly={action}
                  required={required}
                  searchValue={searchPickList}
                  onSearchChange={setSearchPickList}
                  clearable
                  className={styles.inputField}
                  placeholder='Select options'
                  mb='0'
                  maxValues={undefined}
                  value={
                    controllerField.value?.filter((it) => picklistFieldDataLst.some((opt) => opt.value === it.value)).map((it) => it.value) || []
                  }
                  onChange={(values) => {
                    controllerField.onChange(values.map((it, index) => ({ isDefault: true, value: it, position: index }) as PicklistOption));
                    if (errors.fields?.[index]?.picklistOptions?.message) {
                      handleValidate();
                    }
                  }}
                  styles={{
                    root: { height: '90%' },
                    wrapper: { height: '85%' },
                    input: { height: '100%', overflowY: 'auto' },
                  }}
                  onFocus={() => setIsFocused(true)}
                  onBlur={() => {
                    setIsFocused(false);
                  }}
                  error={errors.fields?.[index]?.picklistOptions?.message}
                />
              ) : (
                <KanbanSelect
                  data={picklistFieldDataLst}
                  searchable
                  readOnly={action}
                  required={required}
                  searchValue={searchPickList}
                  onSearchChange={setSearchPickList}
                  clearable
                  className={styles.inputField}
                  placeholder='Select option'
                  mb='0'
                  value={controllerField.value?.[0]?.value || null}
                  onChange={(value) => {
                    controllerField.onChange(value ? [{ isDefault: true, value, position: 0 } as PicklistOption] : []);
                    if (errors.fields?.[index]?.picklistOptions?.message) {
                      handleValidate();
                    }
                  }}
                  onFocus={() => setIsFocused(true)}
                  onBlur={() => {
                    setIsFocused(false);
                  }}
                  error={errors.fields?.[index]?.picklistOptions?.message}
                />
              )
            }
          />
        </Group>
      );
    }

    default:
      return (
        <Group align='center' justify='space-between' pr='xs' gap={'0'} className={styles.fieldGroup}>
          <FieldName field={changeRequestField} required={required} />
          <KanbanInput
            placeholder={`Nhập ${changeRequestField.name?.toLowerCase()}...`}
            readOnly={action}
            className={styles.inputField}
            {...register(`fields.${index}.fieldConstraint.defaultValue`)}
          />
        </Group>
      );
  }
};

const RichTextFieldRenderer: React.FC<{
  field: ChangeRequestFieldItem;
  error?: string;
  controllerField: ControllerRenderProps<ChangeRequestModel, `fields.${number}.fieldConstraint.defaultValue`>;
  required: boolean;
  readOnly: boolean;
  onBlur?: () => void;
  onChange?: (value: string) => void;
}> = ({ controllerField, error, field, onBlur, onChange, readOnly, required }) => {
  const lastContentRef = useRef<string>(controllerField.value || '');

  const editor = useEditor({
    extensions: [StarterKit, Underline],
    content: controllerField.value || '',
    editable: !readOnly,
    onUpdate: ({ editor }) => {
      if (!readOnly) {
        const html = editor.getHTML();
        const text = editor.getText().trim();
        lastContentRef.current = text === '' ? '' : html;
        onChange?.(lastContentRef.current);
      }
    },
  });

  useEffect(() => {
    if (editor) {
      editor.setEditable(!readOnly);
    }
  }, [readOnly, editor]);

  useEffect(() => {
    if (editor && editor.getHTML() !== controllerField.value) {
      editor.commands.setContent(controllerField.value || '', false);
    }
  }, [controllerField.value, editor]);

  useEffect(() => {
    if (!editor) {
      return;
    }
    const handleBlur = () => {
      onBlur?.();
      controllerField.onChange(lastContentRef.current);
    };
    editor.on('blur', handleBlur);
    return () => {
      editor.off('blur', handleBlur);
    };
  }, [controllerField, editor, onBlur, onChange]);

  const isBreakField = field.customFieldType === TemplateCustomFieldTypeEnum.enum.BREAK;

  return (
    <Group align='center' justify='space-between' pr='xs' gap={0} className={styles.fieldGroup}>
      {!isBreakField && <FieldName field={field} required={required} />}
      <Input.Wrapper
        required={required}
        error={error}
        errorProps={{ style: { marginTop: 5 } }}
        className={styles.inputField}
        style={{ flex: 1, height: '100%', maxWidth: isBreakField ? '100%' : undefined }}>
        <RichTextEditor
          editor={editor}
          className={styles.richTextEditor}
          maw={isBreakField ? '100%' : ''}
          withTypographyStyles={false}
          styles={{
            root: {
              height: '95%',
              border: error ? `1px solid var(--mantine-color-red-5)` : undefined,
              borderRadius: 4,
              overflowY: 'auto',
            },
            content: { height: '100%' },
            typographyStylesProvider: { height: '100%' },
          }}>
          <RichTextEditor.Toolbar sticky style={readOnly ? { pointerEvents: 'none' } : {}}>
            <RichTextEditor.ControlsGroup>
              <RichTextEditor.Bold />
              <RichTextEditor.Italic />
              <RichTextEditor.Underline />
              <RichTextEditor.Strikethrough />
              <RichTextEditor.ClearFormatting />
            </RichTextEditor.ControlsGroup>
            <RichTextEditor.ControlsGroup>
              <RichTextEditor.Blockquote />
              <RichTextEditor.Hr />
              <RichTextEditor.BulletList />
              <RichTextEditor.OrderedList />
            </RichTextEditor.ControlsGroup>
          </RichTextEditor.Toolbar>
          <RichTextEditor.Content />
        </RichTextEditor>
      </Input.Wrapper>
    </Group>
  );
};

const FieldName: React.FC<{ field: ChangeRequestFieldItem; required: boolean }> = ({ field, required }) => {
  return (
    <Tooltip label={field.name} multiline maw={'30%'} position='top-start'>
      <Group className={styles.fieldName} justify='flex-start ' pr='xs' gap={'0'} align='center' w={'120px'}>
        <KanbanText fw={'500'} c={required ? 'red' : 'white'}>
          *
        </KanbanText>
        <KanbanText size='xs' className={clsx(styles.fieldName, styles.dragHandle, DRAG_TAG)} lineClamp={3}>
          {field.name}
        </KanbanText>
      </Group>
    </Tooltip>
  );
};

const FieldError: React.FC<{ message?: string }> = ({ message }) => {
  return (
    message && (
      <Tooltip label={message} color='red' withArrow position='right'>
        <Box ml={6} mr={2} style={{ display: 'flex', alignItems: 'center' }}>
          <IconAlertCircle color='red' size={18} />
        </Box>
      </Tooltip>
    )
  );
};
export default React.memo(CustomFieldRenderer);
