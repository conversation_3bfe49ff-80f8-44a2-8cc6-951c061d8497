import { SortType } from '@common/constants/SortType';
import { z } from 'zod';

export const PageRequestModelSchema = z.object({
  propertiesSearch: z.array(z.string()).optional(),
  page: z.number().optional(),
  size: z.number().optional(),
  search: z.string().optional(),
  sortBy: z.string().optional(),
  sortOrder: z.nativeEnum(SortType).optional(),
  isReverse: z.boolean().optional(),
});

export type PageRequestModel = z.infer<typeof PageRequestModelSchema>;
