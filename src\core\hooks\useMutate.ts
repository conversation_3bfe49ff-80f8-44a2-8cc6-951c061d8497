import { callRequest, RequestConfig } from '@core/api/BaseApi';
import { useMutation, UseMutationOptions } from '@tanstack/react-query';
import { useMemo } from 'react';
import { AppConfig, NotificationConfig } from './AppConfigTypes';
import { useKanbanModals } from 'kanban-design-system';
import { defaultErrorNotification, showErrorNotification, showSuccessNotification } from './Utils';
import { ZIndexConfirm } from '@common/constants/ZIndexConstants';

function defaultSuccessNotification(): NotificationConfig {
  return {
    enable: true,
    title: `Action success`,
    message: 'Action successfully',
  };
}

type MutateAppConfig<TData, TVariables> = Omit<AppConfig<TData>, 'withSignal'> & UseMutationOptions<TData, Error, TVariables>;

function useMutate<TData, TVariables>(requestFn: (variables: TVariables) => RequestConfig<TData>, appConfig?: MutateAppConfig<TData, TVariables>) {
  const {
    confirm = { enable: false },
    errorNotification = defaultErrorNotification,
    onError,
    onSuccess,
    showLoading = true,
    successNotification = defaultSuccessNotification,
    throwParsedError = false,
    ...otherConfig
  } = appConfig || {};
  const modelProvider = useKanbanModals();
  const mutateResult = useMutation<TData, Error, TVariables>({
    mutationFn: (variables: TVariables) => callRequest(requestFn(variables), { showLoading, throwParsedError }),
    onSuccess: (data, ...rest) => {
      onSuccess && onSuccess(data, ...rest);
      showSuccessNotification(successNotification, data);
    },
    onError: (error, ...rest) => {
      onError && onError(error, ...rest);
      showErrorNotification(errorNotification, error);
    },
    networkMode: 'always',
    ...otherConfig,
  });
  const mutateFn = useMemo<(variables: TVariables, appConfig?: MutateAppConfig<TData, TVariables>) => Promise<TData>>(() => {
    if (confirm.enable === false) {
      return mutateResult.mutateAsync;
    }
    return (variables: TVariables, appConfig?: MutateAppConfig<TData, TVariables>) => {
      const effectiveConfirm = {
        ...confirm,
        ...appConfig?.confirm,
        modalProps: {
          ...confirm.modalProps,
          ...appConfig?.confirm?.modalProps,
        },
      };
      return new Promise<TData>((resolve) => {
        const id = modelProvider.openConfirmModal({
          ...effectiveConfirm,
          modalProps: {
            showFullScreenAction: false,
            showCloseIcon: false,
            ...effectiveConfirm.modalProps,
            zIndex: ZIndexConfirm,
          },
          onConfirm() {
            modelProvider.closeModal(id);
            resolve(mutateResult.mutateAsync(variables));
          },
        });
      });
    };
  }, [confirm, modelProvider, mutateResult]);

  return { ...mutateResult, mutate: mutateFn };
}

export default useMutate;
