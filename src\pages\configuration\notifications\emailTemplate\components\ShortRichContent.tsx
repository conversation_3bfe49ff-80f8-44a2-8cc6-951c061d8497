import React, { useCallback, useEffect, useState } from 'react';
import styles from '../EmailTemplate.module.scss';
import { Popover, Text } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';

import { DocumentNode, MentionNode, ShortRichNode, TextNode } from '@models/EmailTemplateModel';
import { KanbanText } from 'kanban-design-system';
import { JsonKeyConstants as JKC, JsonKeyConstants } from '@common/constants/JsonKeyConstants';
import { TextLengthConstants } from '@common/constants/TextLengthConstants';

const renderNode = (node: TextNode | MentionNode): React.ReactNode => {
  if (node.type === JKC.MENTION_TYPE) {
    const { deleted, id, label } = node.attrs;
    return (
      <Text
        component='span'
        className={styles.mentionNode}
        style={{
          textDecoration: deleted ? 'line-through' : 'none',
        }}>
        ${label || id}
      </Text>
    );
  }
  if (node.type === JKC.TEXT_TYPE) {
    return <Text component='span'>{node.text}</Text>;
  }
  return <></>;
};

const flattenContent = (json: DocumentNode): React.ReactNode[] => {
  const result: React.ReactNode[] = [];
  let keyIndex = 0;
  json.forEach((para) => {
    para.content?.forEach((node) => {
      result.push(<React.Fragment key={keyIndex++}>{renderNode(node)}</React.Fragment>);
    });
  });
  return result;
};

const getTrimmedJson = (json: DocumentNode, maxLength: number = TextLengthConstants.SHORT_RICH_MAX_LENGTH): DocumentNode => {
  const result: DocumentNode = [];
  let currentLength = 0;
  let reachedLimit = false;

  const getTextLength = (text: string | undefined) => text?.length || 0;

  const trimText = (text: string, limit: number): string => {
    return `${text.slice(0, limit)}...`;
  };

  for (const paragraph of json) {
    if (reachedLimit) {
      break;
    }

    const newContent: ShortRichNode[] = [];

    for (const node of paragraph.content || []) {
      if (reachedLimit) {
        break;
      }

      const remaining = maxLength - currentLength;

      if (node.type === JsonKeyConstants.TEXT_TYPE) {
        const text = node.text || '';
        const length = getTextLength(text);

        if (length <= remaining) {
          newContent.push(node);
          currentLength += length;
        } else {
          newContent.push({
            ...node,
            text: trimText(text, remaining),
          });
          reachedLimit = true;
        }
      } else if (node.type === JsonKeyConstants.MENTION_TYPE) {
        const { id, label } = node.attrs;
        const mentionText = label || id;
        // add "$" in front
        const length = 1 + getTextLength(mentionText);

        if (length <= remaining) {
          newContent.push(node);
          currentLength += length;
        } else {
          newContent.push({
            ...node,
            attrs: {
              ...node.attrs,
              label: trimText(mentionText, remaining),
            },
          });
          reachedLimit = true;
        }
      }
    }

    if (newContent.length > 0) {
      result.push({
        type: JsonKeyConstants.PARAGRAPH_TYPE,
        content: newContent,
      });
    }
  }

  return result;
};

export const ShortRichContent = ({ content }: { content: DocumentNode }) => {
  const [opened, { close, open }] = useDisclosure(false);
  const [clickOpened, setClickOpened] = useState(false);

  useEffect(() => {
    setClickOpened(false);
    close();
  }, [close]);

  const handleOpen = useCallback(() => {
    setClickOpened(!opened);
    if (opened) {
      close();
    } else {
      open();
    }
  }, [close, open, opened]);

  const fullContent = flattenContent(content);
  const previewContentTrimmed = getTrimmedJson(content);
  const previewContent = flattenContent(previewContentTrimmed);

  return (
    <Popover width='30%' position='top-start' withArrow shadow='md' opened={opened} onClose={close}>
      <Popover.Target>
        <Text size='sm' h={'100%'} onClick={handleOpen} onMouseEnter={clickOpened ? undefined : open} onMouseLeave={clickOpened ? undefined : close}>
          {previewContent}
        </Text>
      </Popover.Target>
      <Popover.Dropdown bg='black' c='white' onMouseEnter={clickOpened ? undefined : open} onMouseLeave={clickOpened ? undefined : close}>
        <KanbanText size='sm' style={{ maxHeight: '200px', overflowY: 'auto', width: '100%' }}>
          {fullContent}
        </KanbanText>
      </Popover.Dropdown>
    </Popover>
  );
};
