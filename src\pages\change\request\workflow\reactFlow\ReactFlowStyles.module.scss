/* ReactFlow Custom Styles */
:global(.react-flow__node) {
  cursor: grab;
}

:global(.react-flow__pane) {
  cursor: default;
}

:global(.react-flow__node:hover) {
  z-index: 1000;
}

:global(.react-flow__node.selected) {
  z-index: 1001;
}

/* Fixed handles - không di chuyển khi hover */
:global(.react-flow__handle) {
  border: 2px solid white;
  background: #6b7280;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  transition: none !important;
  transform: none !important;
  position: absolute !important;
}

:global(.react-flow__handle.fixed-handle) {
  border: 2px solid white;
  background: #6b7280 !important;
  width: 24px !important;
  height: 24px !important;
  border-radius: 50%;
  transition: none !important;
  transform: none !important;
  position: absolute !important;
}

:global(.react-flow__handle.fixed-handle:hover) {
  background: #6b7280 !important;
  transform: none !important;
  transition: none !important;
}

:global(.react-flow__handle-connecting) {
  background: #3b82f6 !important;
  transform: none !important;
  transition: none !important;
}

:global(.react-flow__edge-path) {
  transition: all 0.2s ease-in-out;
}

:global(.react-flow__edge.selected .react-flow__edge-path) {
  stroke-width: 3;
  filter: drop-shadow(0 0 6px rgba(59, 130, 246, 0.5));
}

:global(.react-flow__connection-line) {
  stroke: #3b82f6;
  stroke-width: 2;
  stroke-dasharray: 8, 4;
}

:global(.react-flow__background) {
  background: #f9fafb;
}

:global(.react-flow__controls) {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

:global(.react-flow__controls button) {
  background: white;
  border: none;
  border-bottom: 1px solid #e5e7eb;
  color: #374151;
  transition: all 0.2s ease-in-out;
}

:global(.react-flow__controls button:hover) {
  background: #f3f4f6;
  color: #111827;
}

:global(.react-flow__controls button:last-child) {
  border-bottom: none;
}

/* Đảm bảo không có animation trên handles */
:global(.react-flow__handle *) {
  transition: none !important;
  transform: none !important;
}

/* Custom node hover animations */
@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 1;
  }

  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

.nodePulse::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid #3b82f6;
  border-radius: 50%;
  animation: pulse-ring 0s ease-out infinite;
}

/* Tooltip styles */
:global(.react-flow__node .mantine-Tooltip-tooltip) {
  font-size: 11px;
  padding: 4px 6px;
  background: #1f2937;
  color: white;
  border-radius: 4px;
}