.editor {
  padding: 0.4rem 1rem;
  border-radius: 0.3rem;
  background-color: var(--mantine-color-white);
  border: 0.1rem solid var(--mantine-color-gray-4);
  font-size: 0.875rem;
  color: 	var(--mantine-color-dark-6);
  outline: none;
  resize: none;

  p {
    margin-block-start: 0;
    margin-block-end: 0;
  }
}

.paragraph {
  margin: 0;
  line-height: 1.5;
  color: inherit;
}

.mentionNode {
  color: var(--mantine-color-blue-6);
  background-color: var(--mantine-color-white);
  padding: 0.2rem 0.5rem;
  border-radius: 0.5rem;

  &[deleted="true"] {
    text-decoration: line-through;
  }
}

.errors {
  color: var(--mantine-color-error);
  margin-top: calc(var(--mantine-spacing-xs) / 2);
  font-size: var(--input-error-size, calc(var(--mantine-font-size-sm) - calc(.125rem * var(--mantine-scale))));
}

:global(.custom-content-rte-content-100 .ProseMirror) {
  min-height: 50px;
}

:global(.custom-content-rte-content-300 .ProseMirror) {
  min-height: 300px;
}