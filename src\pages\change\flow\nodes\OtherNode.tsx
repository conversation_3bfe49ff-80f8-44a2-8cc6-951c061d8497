import React, { useEffect, useMemo, useState } from 'react';
import { Box, ComboboxItem } from '@mantine/core';
import { Handle, Position, useStore } from 'reactflow';
import { ActionIcon } from '@mantine/core';
import { KanbanText } from 'kanban-design-system';
import { IconMail } from '@tabler/icons-react';
import clsx from 'clsx';
import styles from './Node.module.scss';
import { ChangeFlowNodeType, ChangeFlowNotificationModel } from '@models/ChangeFlowModel';
import { useDisclosure } from '@mantine/hooks';
import { NotificationModal } from '../notify/NotificationModal';
import { useNotificationForm } from '../notify/useNotificationForm';
import useMutate from '@core/hooks/useMutate';
import { NotificationsEmailTemplateApi } from '@api/NotificationsEmailTemplateApi';
import { EntityAction } from '@common/constants/EntityActionConstants';

interface OtherNodeData {
  name: string;
  type: ChangeFlowNodeType;
  notifications: ChangeFlowNotificationModel[];
  otherNodeOptions: ComboboxItem[];
  updateNode?: (nodeId: string, notifications: ChangeFlowNotificationModel[]) => void;
}

interface OtherNodeProps {
  data: OtherNodeData;
  id: string;
}

const STATUS_LIST = ['Accept', 'Reject'] as const;
type Status = (typeof STATUS_LIST)[number];

const getStatusId = (id: string, status: Status) => `${id}${status}`;

const OtherNode: React.FC<OtherNodeProps> = ({ data, id }) => {
  const edges = useStore((state) => state.edges);
  const [selectedStatus, setSelectedStatus] = useState<Status | null>(null);
  const [opened, { close, open }] = useDisclosure(false);

  const {
    notifyError,
    notifyItems,
    prepareNotification,
    resetNotificationForm,
    selectedTemplate,
    setNotifyError,
    setNotifyItems,
    setSelectedTemplate,
  } = useNotificationForm();

  useEffect(() => {
    if (!opened) {
      setSelectedStatus(null);
      resetNotificationForm();
    }
  }, [opened, resetNotificationForm]);

  const openModal = (status: Status) => {
    const noti = data.notifications?.find((v) => v.statusId === getStatusId(id, status));
    setSelectedStatus(status);
    setSelectedTemplate(noti?.emailTemplateId ?? null);

    const newNotifyItems = [...(noti?.notifyNode ?? []), ...(noti?.notifyTo ?? [])];
    setNotifyItems((prev) => {
      if (prev.length !== newNotifyItems.length || !prev.every((item, index) => item === newNotifyItems[index])) {
        return newNotifyItems;
      }
      return prev;
    });

    open();
  };

  const updateNotificationsAndClose = (statusId: string) => {
    const result = prepareNotification(data.otherNodeOptions, statusId);
    if (!result) {
      return;
    }

    const updated = result.emailTemplateId
      ? [...(data.notifications ?? []).filter((n) => n.statusId !== result.statusId), result]
      : (data.notifications ?? []).filter((n) => n.statusId !== result.statusId);

    data.updateNode?.(id, updated);
    close();
  };

  const { mutate: checkTemplateExists } = useMutate(NotificationsEmailTemplateApi.findById, {
    successNotification: { enable: false },
    onSuccess: (response) => {
      if (response.data && selectedStatus) {
        updateNotificationsAndClose(getStatusId(id, selectedStatus));
      }
    },
  });

  const selectedNotification = useMemo(() => {
    if (!selectedStatus) {
      return undefined;
    }
    return data.notifications?.find((v) => v.statusId === getStatusId(id, selectedStatus));
  }, [selectedStatus, data.notifications, id]);

  const handleSave = () => {
    if (!selectedStatus) {
      return;
    }
    if (selectedTemplate) {
      checkTemplateExists(selectedTemplate);
    } else {
      updateNotificationsAndClose(getStatusId(id, selectedStatus));
    }
  };

  return (
    <Box
      onDoubleClick={(e) => {
        if (opened) {
          e.preventDefault();
          e.stopPropagation();
        }
      }}
      className={styles.box}>
      <Handle isConnectableStart={false} type='target' position={Position.Top} id={`${id}`} className={styles.handleTop} />
      <Box className={clsx(styles.nameBox, data.type === 'CAB' ? styles.cab : styles.approval)}>{data.name}</Box>
      <Box p='sm'>
        {STATUS_LIST.map((status) => {
          const isConfigured = !!data.notifications?.find((n) => n.statusId === getStatusId(id, status))?.emailTemplateId;
          return (
            <Box key={status} className={styles.body}>
              <KanbanText ml='sm'>{status}</KanbanText>
              <ActionIcon
                className={styles.mailBox}
                variant='subtle'
                color={isConfigured ? 'yellow' : 'gray'}
                size='md'
                style={{
                  transform: 'translateY(-50%)',
                }}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  openModal(status);
                }}>
                <IconMail size={18} />
              </ActionIcon>
              <Handle
                type='source'
                position={Position.Right}
                id={`${status}`}
                className={clsx(styles.handle, styles.right, {
                  [styles.reject]: status === 'Accept',
                })}
                isValidConnection={(conn) => conn.source !== conn.target && !edges.some((e) => e.source === id && e.sourceHandle === `${status}`)}
              />
            </Box>
          );
        })}
      </Box>

      <NotificationModal
        opened={opened}
        onClose={close}
        onSave={handleSave}
        initialMode={selectedNotification ? EntityAction.EDIT : EntityAction.CREATE}
        notification={selectedNotification}
        variableOptions={data.otherNodeOptions}
        selectedTemplate={selectedTemplate}
        setSelectedTemplate={setSelectedTemplate}
        notifyItems={notifyItems}
        setNotifyItems={setNotifyItems}
        notifyError={notifyError}
        setNotifyError={setNotifyError}
      />
    </Box>
  );
};

export default OtherNode;
