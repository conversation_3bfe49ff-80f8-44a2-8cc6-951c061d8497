import React from 'react';
import { Box, Group, Badge, Stack, ScrollAreaAutosize } from '@mantine/core';
import { KanbanText, KanbanTextarea } from 'kanban-design-system';
import { ChangeRequestApprovalApi } from '@api/ChangeRequestApprovalApi';
import useFetch from '@core/hooks/useFetch';
import { ChangeRequestApprovalStatus } from '@core/schema/ChangeRequestApproval';
import styled from '@pages/change/request/ChangeRequestDetail.module.scss';

export type ChangeRequestApprovalResultListProps = {
  changeRequestApprovalId: number;
};

const statusColorMap: Record<ChangeRequestApprovalStatus, string> = {
  ACCEPT: 'green',
  REJECT: 'red',
  PENDING_APPROVAL: 'yellow',
  TOBE_SEND: 'gray',
};

export const ChangeRequestApprovalResultList: React.FC<ChangeRequestApprovalResultListProps> = ({ changeRequestApprovalId }) => {
  const { data: results } = useFetch(ChangeRequestApprovalApi.findAllResults(changeRequestApprovalId), {
    enabled: !!changeRequestApprovalId,
  });

  const approvals = results?.data || [];

  if (!approvals.length) {
    return (
      <KanbanText c='dimmed' mt='md'>
        No approval results found
      </KanbanText>
    );
  }

  return (
    <ScrollAreaAutosize>
      <Stack gap='md' p='md' mb='md'>
        {approvals.map(({ approvalComment, approvedUser, createdDate, id, status }) => (
          <Box key={id} p='sm' style={{ border: '1px solid #ddd', borderRadius: 6 }}>
            {/* Top row: createdDate, approvedUser, status */}
            <Group mb='xs'>
              <KanbanText size='sm' fw={500}>
                {createdDate ? new Date(createdDate).toLocaleString() : '-'}
              </KanbanText>
              <KanbanText size='sm' c='dimmed' flex={1} ta='center'>
                {approvedUser || '-'}
              </KanbanText>
              <Badge color={statusColorMap[status] || 'blue'} variant='filled' size='sm' miw={90} ta='center'>
                {status}
              </Badge>
            </Group>

            <KanbanTextarea value={approvalComment || 'No comment'} readOnly autosize minRows={2} className={styled.textareaComment} />
          </Box>
        ))}
      </Stack>
    </ScrollAreaAutosize>
  );
};
