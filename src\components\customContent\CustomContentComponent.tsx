import Document from '@tiptap/extension-document';
import Paragraph from '@tiptap/extension-paragraph';
import { Editor, ReactRenderer, useEditor } from '@tiptap/react';
import React, { useEffect, useMemo, useRef } from 'react';
import { MentionList, MentionListActions, MentionListProps } from './MentionList';
import '@mantine/tiptap/styles.css';
import { KanbanText } from 'kanban-design-system';
import keyboardKey from 'keyboard-key';
import styles from './CustomContent.module.scss';
import tippy, { GetReferenceClientRect, Instance, Props as TippyProps } from 'tippy.js';
import CharacterCount from '@tiptap/extension-character-count';
import { Box, Flex } from '@mantine/core';
import { RichTextEditor } from '@mantine/tiptap';
import { CustomMention } from '@components/customContent/CustomMention';
import { JsonKeyConstants } from '@common/constants/JsonKeyConstants';
import { DocumentNode } from '@models/EmailTemplateModel';
import { getActualContentLength, getRemaining, parseEditorContent } from '@common/utils/JsonTextUtils';
import { LINE_BREAK_REGEX } from '@common/constants/RegexConstant';
import { parseStringToJson } from '@common/utils/JsonUtils';
import StarterKit, { StarterKitOptions } from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
const getCode = keyboardKey.getCode;

const INPUT_ALLOWED_KEY_CODES = new Set<number>([
  keyboardKey.Backspace,
  keyboardKey.Delete,
  keyboardKey.ArrowLeft,
  keyboardKey.ArrowRight,
  keyboardKey.ArrowUp,
  keyboardKey.ArrowDown,
]);

interface EditorProps {
  value: string | DocumentNode;
  onChange?: (val: string) => void;
  label?: string;
  disabled: boolean;
  contentMaxLength: number;
  docsToolbar?: boolean;
  clientRect?: () => DOMRect | null;
  editor?: Editor;
  id?: string;
  event?: { key?: string };
  minHeightRte?: number;
  singleLineMode?: boolean;
  withAsterisk?: boolean;
}

const createExtensionsConfig = (singleLineMode: boolean): Partial<StarterKitOptions> => ({
  bold: !singleLineMode ? {} : false,
  italic: !singleLineMode ? {} : false,
  strike: !singleLineMode ? {} : false,
  blockquote: !singleLineMode ? {} : false,
  bulletList: !singleLineMode ? {} : false,
  orderedList: !singleLineMode ? {} : false,
  listItem: !singleLineMode ? {} : false,
  horizontalRule: !singleLineMode ? {} : false,
  history: { depth: 50 },
});

export const CustomContentComponent: React.FC<EditorProps & { error?: string }> = ({
  contentMaxLength,
  disabled = false,
  docsToolbar,
  error,
  label,
  minHeightRte = 100,
  onChange,
  singleLineMode = false,
  value,
  withAsterisk,
}) => {
  const isMentionActive = useRef(false);

  // Memoize extensions to avoid recreation
  const extensions = useMemo(
    () => [
      Document,
      ...(!singleLineMode ? [Underline] : []),
      Paragraph.configure({ HTMLAttributes: { class: styles.paragraph } }),
      StarterKit.configure(createExtensionsConfig(singleLineMode)),
      CharacterCount.configure({ limit: contentMaxLength }),
      CustomMention.configure({
        HTMLAttributes: { class: styles.mentionNode },
        suggestion: {
          char: '$',
          render: () => {
            let component: ReactRenderer<MentionListActions, MentionListProps & React.RefAttributes<MentionListActions>> | undefined;
            let popup: Instance[] | undefined;
            return {
              onStart: (props) => {
                isMentionActive.current = true;
                component = new ReactRenderer(MentionList, { props: { ...props }, editor: props.editor });
                if (!props.clientRect) {
                  return;
                }
                const rect: GetReferenceClientRect = () => {
                  const rect = props.clientRect && props.clientRect();
                  return rect ? rect : new DOMRect();
                };
                const tippyProps: Partial<TippyProps> = {
                  getReferenceClientRect: rect,
                  appendTo: () => document.body,
                  content: component.element,
                  showOnCreate: true,
                  interactive: true,
                  trigger: 'manual',
                  placement: 'bottom-start',
                };
                popup = tippy('body', tippyProps);
              },
              onUpdate: (props) => {
                component?.updateProps(props);
                if (props.clientRect && popup) {
                  const rect: GetReferenceClientRect = () => props.clientRect?.() ?? new DOMRect();
                  popup[0].setProps({ getReferenceClientRect: rect });
                }
              },
              onKeyDown: (props) => {
                if (getCode(props.event) === keyboardKey.Escape) {
                  popup?.[0].hide();
                  return true;
                }
                return component?.ref?.onKeyDown(props) ?? false;
              },
              onExit: () => {
                isMentionActive.current = false;
                popup?.[0].destroy();
                component?.destroy();
              },
            };
          },
          command: ({ editor, props, range }) => {
            const currentLength = getActualContentLength(editor);
            const mentionLength = 1 + (props.label?.length || 0);
            if (currentLength + mentionLength > contentMaxLength) {
              return false;
            }

            editor
              .chain()
              .focus()
              .deleteRange(range)
              .insertContent({ type: JsonKeyConstants.MENTION_TYPE, attrs: { id: props.id, label: props.label } })
              .run();
          },
        },
      }),
    ],
    [contentMaxLength, singleLineMode],
  );

  const editor = useEditor({
    editable: !disabled,
    content: value,
    editorProps: {
      attributes: { class: styles.editor },
      handleDOMEvents: {
        paste: (view, event: ClipboardEvent) => {
          if (!editor || isMentionActive.current) {
            return false;
          }

          const pastedText = event.clipboardData?.getData('text/plain') || '';
          const remainingLength = getRemaining(editor, contentMaxLength);
          const currentText = view.state.doc.textBetween(0, view.state.doc.content.size, '\n');

          if (remainingLength <= 0) {
            event.preventDefault();
            return true;
          }

          if (singleLineMode) {
            const sanitizedText = pastedText.replace(LINE_BREAK_REGEX, ' ').slice(0, remainingLength);
            editor.commands.insertContent(sanitizedText);
            event.preventDefault();
            return true;
          }

          if (currentText.length > contentMaxLength) {
            const trimmedText = currentText.slice(0, contentMaxLength);
            editor.commands.setContent(trimmedText);
            event.preventDefault();
            return true;
          }

          if (pastedText.length > remainingLength) {
            const textToInsert = pastedText.slice(0, remainingLength);
            editor.commands.insertContent(textToInsert);
            event.preventDefault();
            return true;
          }
          return false;
        },
        keydown: (view, event) => {
          if (!editor || event.ctrlKey || event.metaKey) {
            return false;
          }

          const code = getCode(event);
          const remaining = getRemaining(editor, contentMaxLength);

          let shouldPreventDefault = false;

          if (remaining < 0 && keyboardKey.Enter === code) {
            return true;
          } else if (singleLineMode && code === keyboardKey.Enter && !isMentionActive.current) {
            shouldPreventDefault = true;
          } else if (remaining <= 0 && code !== undefined && !INPUT_ALLOWED_KEY_CODES.has(code)) {
            shouldPreventDefault = true;
          } else if (code === keyboardKey.Backspace) {
            const { from, to } = view.state.selection;
            const hasSelection = from !== to;
            if (!hasSelection && from > 1) {
              const nodeBefore = view.state.selection.$from.nodeBefore;

              if (nodeBefore && nodeBefore.type.name === JsonKeyConstants.MENTION_TYPE) {
                const start = from - nodeBefore.nodeSize;
                const tr = view.state.tr.delete(start, from);
                view.dispatch(tr);
                shouldPreventDefault = true;
              }
            }
          }

          if (shouldPreventDefault) {
            event.preventDefault();
            return true;
          }

          return false;
        },
      },
    },
    extensions,
    onBlur: onChange
      ? ({ editor }) => {
          const json = editor.getJSON().content;
          onChange(parseEditorContent(json));
        }
      : undefined,
  });

  const rteStyles = useMemo(
    () => ({
      root: { height: '100%' },
      content: {
        border: error ? 'calc(.0625rem * var(--mantine-scale)) solid var(--mantine-color-error)' : undefined,
      },
      typographyStylesProvider: { height: '100%' },
    }),
    [error],
  );

  // Controlled hydration
  useEffect(() => {
    if (editor) {
      if (typeof value === 'string') {
        editor.commands.setContent(parseStringToJson(value));
      } else {
        editor.commands.setContent(value);
      }
    }
  }, [editor, value]);

  return (
    <Box>
      {label && (
        <Flex direction='row' mt={10}>
          <KanbanText fw={500} mb='0.25rem'>
            {label}
          </KanbanText>
          {withAsterisk && <KanbanText c='red'>&nbsp;*</KanbanText>}
        </Flex>
      )}
      <Box>
        <RichTextEditor editor={editor} className={styles.richTextEditor} styles={rteStyles} withTypographyStyles={false}>
          {docsToolbar && (
            <RichTextEditor.Toolbar sticky>
              <RichTextEditor.ControlsGroup>
                <RichTextEditor.Bold />
                <RichTextEditor.Italic />
                <RichTextEditor.Underline />
                <RichTextEditor.Strikethrough />
                <RichTextEditor.ClearFormatting />
              </RichTextEditor.ControlsGroup>
              <RichTextEditor.ControlsGroup>
                <RichTextEditor.Blockquote />
                <RichTextEditor.Hr />
                <RichTextEditor.BulletList />
                <RichTextEditor.OrderedList />
              </RichTextEditor.ControlsGroup>
            </RichTextEditor.Toolbar>
          )}
          <RichTextEditor.Content className={`custom-content-rte-content-${minHeightRte}`} />
        </RichTextEditor>
      </Box>
      {error && <Box className={styles.errors}>{error}</Box>}
    </Box>
  );
};
