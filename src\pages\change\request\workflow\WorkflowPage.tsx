import { EntityAction } from '@common/constants/EntityActionConstants';
import { buildChangeDetailUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import { ColumnType, KanbanButton, KanbanIconButton, KanbanTable, KanbanTableProps, KanbanText, renderDateTime } from 'kanban-design-system';
import React, { useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import stylesCss from './WorkflowPage.module.scss';
import { ChangeRequestApi } from '@api/ChangeRequestApi';
import { ChangeWorkflow } from '@core/schema/ChangeWorkflowNode';
import useFetch from '@core/hooks/useFetch';
import { Flex, Tooltip } from '@mantine/core';
import { IconEdit, IconPlus, IconTrash } from '@tabler/icons-react';
import { IconEye } from '@tabler/icons-react';
import useMutate from '@core/hooks/useMutate';
import { deleteConfirm } from '@components/utils/ConfirmMessageUtils';
import { useCheckPermissons } from '@core/hooks/useCheckPermissons';
import { ACL_PERMISSIONS } from '@common/constants/AclPermissionConstants';

type WorkFlowPageProps = {
  id?: number;
};

const columns: ColumnType<ChangeWorkflow>[] = [
  {
    name: 'name',
    title: 'Name',
  },
  {
    name: 'createdDate',
    title: 'Created Date',
    customRender: renderDateTime,
    hidden: true,
  },
  {
    name: 'createdBy',
    title: 'Created By',
    hidden: true,
  },
  {
    name: 'modifiedDate',
    title: 'Modified Date',
    customRender: renderDateTime,
  },
  {
    name: 'modifiedBy',
    title: 'Modified By',
  },
];

export const WorkflowPage: React.FC<WorkFlowPageProps> = () => {
  const changeId = Number(useParams().id);
  const navigate = useNavigate();
  const { data: workflows, refetch: refetchList } = useFetch(ChangeRequestApi.findAllWorkflows(changeId), {
    enabled: !!changeId,
  });

  const { mutate: deleteWorkflowMutate } = useMutate(ChangeRequestApi.deleteWorkflow, {
    successNotification: 'Workflow deleted successfully',
    confirm: deleteConfirm(),
    onSuccess: () => {
      refetchList();
    },
  });

  const canEdit = useCheckPermissons([ACL_PERMISSIONS.CHANGE_UPDATE]);
  const canAdd = useCheckPermissons([ACL_PERMISSIONS.CHANGE_ADD]);
  const canDelete = useCheckPermissons([ACL_PERMISSIONS.CHANGE_ADD]);

  const tableProps: KanbanTableProps<ChangeWorkflow> = useMemo(() => {
    const tblProps: KanbanTableProps<ChangeWorkflow> = {
      columns: columns,
      data: workflows?.data ?? [],
      actions: {
        customAction: (data) => {
          return (
            <Flex gap='xs' align='center'>
              <Tooltip label='View'>
                <KanbanIconButton
                  variant='transparent'
                  size={'sm'}
                  onClick={() => {
                    navigate(buildChangeDetailUrl(ROUTE_PATH.CHANGE_REQUEST_WORKFLOW_DETAIL, changeId, data.id || 0, EntityAction.VIEW));
                  }}>
                  <IconEye />
                </KanbanIconButton>
              </Tooltip>
              {canEdit && (
                <Tooltip label='Edit'>
                  <KanbanIconButton
                    variant='transparent'
                    size={'sm'}
                    onClick={() => {
                      navigate(buildChangeDetailUrl(ROUTE_PATH.CHANGE_REQUEST_WORKFLOW_DETAIL, changeId, data.id || 0, EntityAction.EDIT));
                    }}>
                    <IconEdit />
                  </KanbanIconButton>
                </Tooltip>
              )}
              {canDelete && (
                <Tooltip label='Delete'>
                  <KanbanIconButton
                    variant='transparent'
                    color='red'
                    size={'sm'}
                    onClick={() => {
                      deleteWorkflowMutate({ changeId: changeId, workflowId: data.id || 0 }, { confirm: deleteConfirm([data.name || '']) });
                    }}>
                    <IconTrash />
                  </KanbanIconButton>
                </Tooltip>
              )}
            </Flex>
          );
        },
      },
    };
    return tblProps;
  }, [canDelete, canEdit, changeId, deleteWorkflowMutate, navigate, workflows?.data]);

  return (
    <div className={stylesCss.workflowPage}>
      {canAdd && (
        <KanbanButton
          size='xs'
          variant='light'
          onClick={() => navigate(buildChangeDetailUrl(ROUTE_PATH.CHANGE_REQUEST_WORKFLOW_DETAIL, changeId, 0, EntityAction.CREATE))}
          className={stylesCss.createButton}
          leftSection={<IconPlus size={10} />}>
          Create workflow
        </KanbanButton>
      )}
      {workflows?.data?.length !== 0 && <KanbanTable {...tableProps} />}
      {workflows?.data?.length === 0 && <KanbanText>No workflow yet. Click the button to create a new workflow</KanbanText>}
    </div>
  );
};
