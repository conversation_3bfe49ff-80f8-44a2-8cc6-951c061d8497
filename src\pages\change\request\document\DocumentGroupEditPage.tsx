import React, { useEffect, useMemo, useState } from 'react';
import { Box, Flex, Grid, ScrollArea, Stack } from '@mantine/core';
import { IconArrowLeft, IconPlus } from '@tabler/icons-react';
import { FormProvider, useFieldArray, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

import { KanbanButton, KanbanIconButton, KanbanTitle, KanbanAccordion } from 'kanban-design-system';

import DocumentGroupForm from './DocumentGroupForm';
import HeaderTitleComponent from '@components/HeaderTitleComponent';

import {
  ChangeRequestDocumentGroupCoordinatorRequest,
  ChangeRequestDocumentGroupForm,
  ChangeRequestDocumentGroupFormWrapper,
  ChangeRequestDocumentGroupFormWrapperSchema,
} from '@models/ChangeRequestDocumentGroupModel';
import { CommonTabProps } from '@pages/change/request/document/index';
import useMutate from '@core/hooks/useMutate';
import { ChangeRequestDocumentGroupApi } from '@api/ChangeRequestDocumentGroupApi';
import { buildDetailUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import { EntityAction } from '@common/constants/EntityActionConstants';
import { useNavigate } from 'react-router-dom';
import AccordionControlEdit from './components/AccordionControlEdit';
import TableEdit from '@pages/change/request/document/components/table/TableEdit';
import { mapChangeRequestDocumentGroupModelToForm, mapFormToCoordinatorRequests } from '@pages/change/request/document/utils/DocumentGroupMapper';

export default function DocumentGroupEditPage({
  canSaveChange,
  changeRequestId,
  documentGroups,
  handleExistWithoutSave,
  hasMatchingRole,
  onRefetchDocumentGroups,
}: CommonTabProps) {
  const [isFormOpen, setIsFormOpen] = useState(false);
  const navigate = useNavigate();
  const [openedAccordions, setOpenedAccordions] = useState<string[]>([]);

  const form = useForm<ChangeRequestDocumentGroupFormWrapper>({
    resolver: zodResolver(ChangeRequestDocumentGroupFormWrapperSchema),
    mode: 'onBlur',
    defaultValues: {
      items: documentGroups ? mapChangeRequestDocumentGroupModelToForm(documentGroups) : [],
    },
  });

  const { control, getValues } = form;

  const {
    append,
    fields,
    remove: removeGroupDocument,
  } = useFieldArray({
    control,
    name: 'items',
    keyName: 'internalId',
  });

  useEffect(() => {
    if (documentGroups) {
      setOpenedAccordions(documentGroups.map((g) => g.id.toString()));
    }
  }, [documentGroups]);

  const { mutate: saveMutate } = useMutate(
    ({ changeRequestId, data }: { changeRequestId: number; data: ChangeRequestDocumentGroupCoordinatorRequest[] }) =>
      ChangeRequestDocumentGroupApi.saveOrUpdate(changeRequestId, data),
    {
      successNotification: `Documents saved successfully`,
      errorNotification: (data) => ({ message: data.message }),
      onSuccess: () => {
        onRefetchDocumentGroups();
        navigate(buildDetailUrl(ROUTE_PATH.CHANGE_REQUEST_DETAIL, changeRequestId, EntityAction.VIEW));
      },
    },
  );

  const handleSave = () => {
    const rawGroups = getValues('items') ?? [];
    const cleaned = mapFormToCoordinatorRequests(rawGroups);
    saveMutate({ changeRequestId: changeRequestId, data: cleaned });
  };

  const handleAddGroupDocument = (groupDocs: ChangeRequestDocumentGroupForm) => {
    const enhancedGroupDocs = {
      ...groupDocs,
      documentGroupName: groupDocs.documentGroupName,
      owners: groupDocs.owners,
    };

    append(enhancedGroupDocs);
  };

  const accordionData = useMemo(
    () =>
      fields.map((item, index) => ({
        key: item.documentGroupId,
        title: (
          <AccordionControlEdit
            fieldName={`items.${index}.documentGroupName`}
            index={index}
            handleRemoveGroup={(index) => removeGroupDocument(index)}
          />
        ),
        content: (
          <ScrollArea>
            <TableEdit itemName={`items.${index}`} hasMatchingRole={hasMatchingRole} />
          </ScrollArea>
        ),
      })),
    [fields, hasMatchingRole, removeGroupDocument],
  );

  return (
    <FormProvider {...form}>
      <form onSubmit={form.handleSubmit(handleSave)}>
        <Flex direction='column' justify='flex-end'>
          <HeaderTitleComponent
            title=''
            leftSection={
              <Flex align='center' gap='xs'>
                <KanbanIconButton size='sm' variant='subtle' onClick={handleExistWithoutSave}>
                  <IconArrowLeft />
                </KanbanIconButton>
                <KanbanTitle fz='h4'>Change documents</KanbanTitle>
              </Flex>
            }
            rightSection={
              <Flex align='center' gap='xs'>
                <KanbanButton size='xs' variant='subtle' onClick={handleExistWithoutSave}>
                  Cancel
                </KanbanButton>
                {canSaveChange && (
                  <KanbanButton size='xs' variant='filled' type='submit'>
                    Save
                  </KanbanButton>
                )}
              </Flex>
            }
          />

          <Stack w='100%'>
            <Box p='md'>
              <Grid justify='space-between' align='center' mb='md'>
                <KanbanTitle order={3}>Document</KanbanTitle>
                {!isFormOpen && (
                  <KanbanButton size='xs' variant='default' leftSection={<IconPlus size={14} />} onClick={() => setIsFormOpen(true)}>
                    Add document category
                  </KanbanButton>
                )}
              </Grid>

              <Grid gutter='md'>
                <Grid.Col span={isFormOpen ? 9 : 12}>
                  <KanbanAccordion
                    multiple
                    chevronPosition='left'
                    onChange={(value) => setOpenedAccordions(value ? ([] as string[]).concat(value) : [])}
                    value={openedAccordions}
                    data={accordionData}
                  />
                </Grid.Col>

                {isFormOpen && (
                  <DocumentGroupForm
                    isFormOpen={isFormOpen}
                    handleAddGroup={handleAddGroupDocument}
                    handleCloseModalAdd={() => setIsFormOpen(false)}
                  />
                )}
              </Grid>
            </Box>
          </Stack>
        </Flex>
      </form>
    </FormProvider>
  );
}
