import { BaseURL } from '@common/constants/BaseUrl';
import { createRequest } from './Utils';
import { createListSchema, createPageSchema, createResponseSchema } from '@core/schema/Common';
import { TableAffactedSafeType } from 'kanban-design-system';
import { RoleDetailModel, RoleDetailSchema } from '@models/RoleModel';
import { tableAffectedToMultiColumnFilterPaginationRequestModel } from '@common/utils/KanbanTableUtils';
import { AclPermissionSchema } from '@core/schema/AclPermission';

export class RoleApi {
  static findAllRoles(data: TableAffactedSafeType) {
    return createRequest({
      url: `${BaseURL.role}`,
      method: 'POST',
      schema: createResponseSchema(createPageSchema(RoleDetailSchema)),
      data: tableAffectedToMultiColumnFilterPaginationRequestModel(data),
    });
  }

  static getDetail(id: number) {
    return createRequest({
      url: `${BaseURL.role}/${id}`,
      method: 'GET',
      schema: createResponseSchema(RoleDetailSchema),
    });
  }

  static getSameNameDetail(id: number, name?: string) {
    return createRequest({
      url: `${BaseURL.role}/${id}/check-name?name=${name}`,
      method: 'GET',
      schema: createResponseSchema(RoleDetailSchema || null),
    });
  }

  static saveOrUpdate(data: RoleDetailModel) {
    return createRequest({
      url: `${BaseURL.role}/${data.id}`,
      method: 'POST',
      schema: createResponseSchema(RoleDetailSchema),
      data,
    });
  }

  static deleteRoleByIds(data: number[]) {
    return createRequest({
      url: BaseURL.role,
      method: 'DELETE',
      schema: createResponseSchema(createPageSchema(RoleDetailSchema)),
      data,
    });
  }

  static findAllPermissionByRoleId(id: number) {
    return createRequest({
      url: `${BaseURL.role}/${id}/permissions`,
      method: 'GET',
      schema: createResponseSchema(createListSchema(AclPermissionSchema)),
    });
  }
}
