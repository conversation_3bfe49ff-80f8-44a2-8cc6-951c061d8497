import React, { FC, useState } from 'react';
import { Paper, Stack, Collapse, Button, Box, Group, Divider } from '@mantine/core';
import { IconChevronDown, IconChevronUp } from '@tabler/icons-react';
import { CHANGE_STAGE_LABEL, ExtendedChangeStageType, ExtendedChangeStageTypeEnum } from '@common/constants/ChageStageType';
import { KanbanTitle } from 'kanban-design-system';
import classes from './NodeSideBar.module.scss';

interface NodeSidebarProps {
  availableStages: ExtendedChangeStageType[];
}

export const NodeSidebar: FC<NodeSidebarProps> = ({ availableStages }) => {
  const [opened, setOpened] = useState(true);

  const handleDragStart = (event: React.DragEvent, nodeType: string) => {
    event.dataTransfer.setData('application/reactflow', nodeType);
    event.dataTransfer.effectAllowed = 'move';
    event.stopPropagation();
  };

  return (
    <Box>
      <Paper shadow='md' withBorder className={classes.sidebarContainer}>
        <Group className={classes.sidebarHeader}>
          <KanbanTitle order={5}>Nodes</KanbanTitle>
          <Button size='xs' variant='subtle' onClick={() => setOpened((o) => !o)}>
            {opened ? <IconChevronUp size={16} /> : <IconChevronDown size={16} />}
          </Button>
        </Group>

        {opened && (
          <Collapse in={opened}>
            <Stack className={classes.nodeList}>
              {availableStages
                .filter((stage) => stage !== 'CAB_NODE' && stage !== 'APPROVAL_NODE')
                .map((stage) => (
                  <Paper key={stage} draggable className={classes.nodeItem} onDragStart={(e) => handleDragStart(e, stage)}>
                    Stage {CHANGE_STAGE_LABEL[stage]}
                  </Paper>
                ))}

              <Divider className={classes.divider} />

              <Paper
                draggable
                className={classes.approvalNode}
                onDragStart={(e) => handleDragStart(e, ExtendedChangeStageTypeEnum.Enum.APPROVAL_NODE)}>
                Approval Node
              </Paper>
              {availableStages.includes('CAB_NODE') && (
                <Paper draggable className={classes.cabNode} onDragStart={(e) => handleDragStart(e, ExtendedChangeStageTypeEnum.Enum.CAB_NODE)}>
                  CAB Node
                </Paper>
              )}
            </Stack>
          </Collapse>
        )}
      </Paper>
    </Box>
  );
};
