declare module 'node-sqlparser' {
  interface ColumnRef {
    type: 'column_ref';
    table: string;
    column: string;
  }

  interface BinaryExpr {
    type: 'binary_expr';
    left: ASTNode;
    operator: string;
    right: ASTNode;
  }

  interface ASTNode {
    type: string;
    [key: string]: any;
  }

  interface SelectQuery {
    type: 'select';
    distinct: boolean | null;
    columns: string | ColumnRef[];
    from: Array<{
      db: string;
      table: string;
      as: string | null;
    }>;
    where?: BinaryExpr;
    groupby?: ColumnRef[];
    having?: BinaryExpr;
    orderby?: Array<{
      expr: ColumnRef;
      type: 'ASC' | 'DESC';
    }>;
    limit?: {
      value: number;
      offset?: number;
    };
    params?: any[];
  }

  export function parse(sql: string, options?: { database: 'plsql' }): SelectQuery;

  export function stringify(ast: SelectQuery): string;
}
