.flowContainer {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  cursor: default;

  &.panningMode {
    cursor: grab;
  }
}

.reactFlow {
  cursor: default;
  width: 100%;
  height: 100%;
}

.controlPanel {
  display: flex;
  flex-direction: row;
  gap: 8px;
  background-color: rgba(255, 255, 255, 0.95);
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  margin: 16px;
  padding: 8px;
  align-items: center;
}

// Thêm vào AmtReactFlow.module.scss
.middleMousePanning {
  cursor: grabbing !important;

  * {
    cursor: grabbing !important;
  }
}

.flowContainer {
  position: relative;

  &.panningMode {
    cursor: grab;
  }

  &.middleMousePanning {
    cursor: grabbing;
    user-select: none;
  }
}