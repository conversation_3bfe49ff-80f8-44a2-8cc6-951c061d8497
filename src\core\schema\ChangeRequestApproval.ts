import { z } from 'zod';
import { AuditSchema } from './Common';

export const ChangeRequestApprovalStatusEnum = z.enum(['ACCEPT', 'REJECT', 'PENDING_APPROVAL', 'TOBE_SEND']);
export type ChangeRequestApprovalStatus = z.infer<typeof ChangeRequestApprovalStatusEnum>;
export const ChangeRequestApprovalSchema = z
  .object({
    id: z.number(),
    changeRequestRoleUserId: z.number().nullish(),
    overAllStatus: ChangeRequestApprovalStatusEnum,
    lastApprovalDate: z.string().nullish(),
    comment: z.string().nullish(),
  })
  .merge(AuditSchema);

export type ChangeRequestApproval = z.infer<typeof ChangeRequestApprovalSchema>;

export const ChangeRequestApprovalResultSchema = z.object({
  id: z.number().nullish(),
  changeRequestApprovalId: z.number().nullish(),
  approvedUser: z.string().nullish(),
  createdDate: z.string().nullish(),
  createdBy: z.string().nullish(),
  approvalComment: z.string().nullish(),
  status: ChangeRequestApprovalStatusEnum,
});

export type ChangeRequestApprovalResult = z.infer<typeof ChangeRequestApprovalResultSchema>;
