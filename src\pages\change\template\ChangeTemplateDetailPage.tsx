import React, { useCallback, useEffect, useRef, useState } from 'react';
import { IconArrowLeft } from '@tabler/icons-react';

import { Box, ComboboxItem, Flex, Group, Paper, Tabs } from '@mantine/core';

import { Controller, FormProvider, useForm } from 'react-hook-form';
import ChangeTemplateBuilder, { isFieldResizable } from './components/ChangeTemplateBuilder';
import { useNavigate, useParams } from 'react-router-dom';
import { callRequest } from '@core/api';
import { ChangeTemplateApi } from '@api/ChangeTemplateApi';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import { useDisclosure } from '@mantine/hooks';
import { buildUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import {
  getDefaultTableAffected,
  KanbanButton,
  KanbanIconButton,
  KanbanInput,
  KanbanSelect,
  KanbanSwitch,
  KanbanText,
  KanbanTitle,
  SortOrder,
} from 'kanban-design-system';
import { ChangeTemplate, ChangeTemplateModelSchema, TemplateCustomFieldTypeEnum, TemplateFieldItem } from '@core/schema/ChangeTemplate';
import { BREAK_FIELD_MIN_HEIGHT, CHANGE_TABS, FIELD_MAX_HEIGHT } from '@common/constants/ChangeTemplateConstants';
import { zodResolver } from '@hookform/resolvers/zod';
import UnsaveConfirmModal from '@components/utils/ConfirmMessageUtils';
import { COMMON_DESCRIPTION_MAX_LENGTH, COMMON_MAX_DROP_DOWN_HEIGHT, COMMON_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { Layout } from 'react-grid-layout';
import { buildNavigateState } from '@core/hooks/useSavedFilterCycle';
import { useQueryClient } from '@tanstack/react-query';
import { useBreadcrumbEntityName } from '@core/hooks/useBreadcrumbEntityName';
import { ChangeFlowApi } from '@api/ChangeFlowApi';
import { ChangeFlowModel } from '@models/ChangeFlowModel';
import { getQueryKey } from '@common/utils/QueryUtils';

const DEFAULT_CHANGE_TEMPLATE: ChangeTemplate = {
  id: 0,
  name: '',
  description: '',
  isActive: true,
  fields: [],
};

const ChangeTemplateDetailPage: React.FC = () => {
  const methods = useForm<ChangeTemplate>({
    defaultValues: DEFAULT_CHANGE_TEMPLATE,
    resolver: zodResolver(ChangeTemplateModelSchema),
    mode: 'onBlur',
  });
  const { clearErrors, control, formState, getValues, register, reset, setError, trigger, watch } = methods;

  const templateId = Number(useParams().id);
  const detailQueryConfig = ChangeTemplateApi.getDetail(templateId);
  const { data: changeTemplateResponse } = useFetch(detailQueryConfig, {
    enabled: !!templateId,
  });
  const [searchTerm] = useState('');
  const [changeFlowOptions, setChangeFlowOptions] = useState<ComboboxItem[]>();

  const changeTemplate = changeTemplateResponse?.data || DEFAULT_CHANGE_TEMPLATE;
  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);
  useBreadcrumbEntityName(changeTemplate.name);

  const isUpdateMode = !!templateId;
  const navigate = useNavigate();
  const [layouts, setLayouts] = useState<Layout[]>([]);

  const updateLayoutsFromFields = useCallback((fields: TemplateFieldItem[]) => {
    const appendLayouts = fields
      .map((sourceItem) => {
        if (!sourceItem) {
          console.error('Source item is undefined or null.');
          return;
        }

        return {
          i: sourceItem.customFieldId.toString(),
          x: sourceItem.horizontalCoordinate || 0, // Initial X based on default column (0 for left)
          y: sourceItem.verticalCoordinate || 0, // Initial Y based on previous item
          w: sourceItem.width || 1, // Use calculated width for layout
          h: sourceItem.height || 1, // Use calculated height for layout
          minW: sourceItem.width || 1,
          maxW: sourceItem.width || 1,
          minH: sourceItem.customFieldType !== TemplateCustomFieldTypeEnum.Enum.BREAK ? 1 : BREAK_FIELD_MIN_HEIGHT,
          maxH: FIELD_MAX_HEIGHT,
          isDraggable: true,
          isResizable: isFieldResizable(sourceItem), // Use helper function
        };
      })
      .filter((it) => !!it);
    if (appendLayouts) {
      setLayouts((prev) => {
        return [...prev, ...appendLayouts];
      });
    }
  }, []);

  const shouldUpdateLayout = useRef(true);
  const { data: changeFlowRes } = useFetch(
    ChangeFlowApi.findAll({
      ...getDefaultTableAffected(),
      advancedFilterMapping: {
        ['name']: {
          filterOption: 'contains',
          value: {
            fromValue: searchTerm,
          },
        },
      },
      sortedBy: 'name',
      sortOrder: SortOrder.ASC,
      rowsPerPage: 200,
    }),
    {
      enabled: true,
      showLoading: false,
    },
  );

  useEffect(() => {
    if (changeFlowRes?.data?.content) {
      const changeFlowsData = (changeFlowRes?.data?.content || [])?.map((r: ChangeFlowModel) => ({
        value: r.id?.toString() || '',
        label: r.name,
      }));
      setChangeFlowOptions(() => {
        const data = [...(changeFlowsData || [])];
        const changeFlowId = getValues('changeFlowId');
        const changeFlowName = getValues('changeFlowName');
        if (changeFlowId && !data.some((us) => us.value === String(changeFlowId))) {
          data.unshift({ value: String(changeFlowId) || '', label: changeFlowName || '' });
        }
        return data;
      });
    }
  }, [changeFlowRes?.data?.content, getValues]);
  useEffect(() => {
    if (changeTemplateResponse?.data && templateId) {
      reset({
        id: changeTemplate.id,
        name: changeTemplate.name,
        description: changeTemplate.description,
        fields: changeTemplate.fields,
        notice: changeTemplate.notice,
        isActive: changeTemplate.isActive,
        changeFlowId: changeTemplate.changeFlowId,
        changeFlowName: changeTemplate.changeFlowName,
      });

      if (shouldUpdateLayout.current && changeTemplate.fields && changeTemplate.fields.length !== 0) {
        updateLayoutsFromFields(changeTemplate.fields || []);
        shouldUpdateLayout.current = false;
      }
    }
  }, [
    changeTemplate.changeFlowId,
    changeTemplate.changeFlowName,
    changeTemplate.description,
    changeTemplate.fields,
    changeTemplate.id,
    changeTemplate.isActive,
    changeTemplate.name,
    changeTemplate.notice,
    changeTemplateResponse,
    reset,
    templateId,
    updateLayoutsFromFields,
  ]);

  // Add activeTab state
  const [activeTab, setActiveTab] = React.useState<string | null>('DETAIL');
  const queryClient = useQueryClient();

  const [savedSuccess, setSavedSuccess] = useState(false);
  const handleNavigateToList = useCallback(() => {
    navigate(buildUrl(ROUTE_PATH.CONFIGURATION_CHANGE_CHANGE_TEMPLATE), { state: buildNavigateState({ fromDetail: true }) });
  }, [navigate]);
  const { mutate: saveMutate } = useMutate(ChangeTemplateApi.saveOrUpdate, {
    successNotification: `Change Template saved successfully`,
    errorNotification: (data) => ({ message: data.message }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: getQueryKey(detailQueryConfig) });
      setSavedSuccess(true);
    },
  });

  useEffect(() => {
    if (savedSuccess) {
      handleNavigateToList();
    }
  });
  const handleSave = useCallback(async () => {
    await trigger();
    saveMutate({ ...getValues(), id: templateId });
  }, [getValues, saveMutate, templateId, trigger]);

  const validateNameOnBlur = async (name?: string) => {
    const isValidZod = !formState.errors.name;

    if (isValidZod && name) {
      const checkNameResponse = await callRequest(ChangeTemplateApi.getSameNameDetail(templateId, name), { showLoading: false });
      if (checkNameResponse.data) {
        setError('name', { type: 'api', message: 'Template Name existed' });
        return false;
      }
      clearErrors('name');
    }
    return true;
  };
  const handleExistWithoutSave = useCallback(() => {
    const touched = formState.isDirty;
    if (touched) {
      openModal();
    } else {
      handleNavigateToList();
    }
  }, [formState.isDirty, handleNavigateToList, openModal]);

  return (
    <FormProvider {...methods}>
      <UnsaveConfirmModal opened={openedModal} onClose={closeModal} onConfirm={handleNavigateToList} />

      <Box p='md'>
        <HeaderTitleComponent
          title={''}
          rightSection={
            <Flex direction={'row'} align={'center'} gap={'xs'}>
              <KanbanButton size='xs' variant='subtle' onClick={handleExistWithoutSave}>
                Cancel
              </KanbanButton>
              <KanbanButton size='xs' variant='filled' type='submit' onClick={handleSave}>
                Save
              </KanbanButton>
            </Flex>
          }
          leftSection={
            <Flex direction={'row'} align={'center'} gap={'xs'}>
              <KanbanIconButton size='sm' variant='subtle' onClick={handleExistWithoutSave}>
                <IconArrowLeft />
              </KanbanIconButton>
              <KanbanTitle fz={'h4'}>{isUpdateMode ? 'Edit Change Template' : 'Add Change Template'}</KanbanTitle>
            </Flex>
          }
        />

        {/* Hiển thị các field cơ bản */}
        <Paper withBorder p='md' mb='lg'>
          <KanbanInput
            label='Template Name'
            {...register('name', {
              onBlur: async (e) => {
                validateNameOnBlur(e.target.value);
              },
              onChange: () => {
                if (formState.errors.name) {
                  clearErrors('name');
                }
              },
            })}
            error={formState.errors['name']?.message}
            maxLength={COMMON_MAX_LENGTH}
            mb='xs'
            withAsterisk
          />
          <KanbanInput
            label='Description'
            {...register('description')}
            error={formState.errors['description']?.message}
            maxLength={COMMON_DESCRIPTION_MAX_LENGTH}
          />
          <Controller
            control={control}
            name={'changeFlowId'}
            render={({ field }) => (
              <KanbanSelect
                data={changeFlowOptions}
                label={'Follows Changeflow'}
                searchable
                clearable
                value={field.value ? field.value?.toString() : ''}
                onChange={(value) => {
                  field.onChange(value);
                }}
                maxDropdownHeight={COMMON_MAX_DROP_DOWN_HEIGHT}
                style={{ flexGrow: 1 }}
              />
            )}
          />

          <Group justify='flex-start' gap={0} align='center'>
            <KanbanText fw='500' w={'55px'}>
              {watch('isActive') ? 'Active' : 'Inactive'}
            </KanbanText>

            <Controller
              control={control}
              name='isActive'
              render={({ field }) => (
                <KanbanSwitch
                  checked={field.value === null ? false : field.value}
                  onChange={(e) => field.onChange(e.currentTarget.checked)}
                  mt={0}
                  styles={{
                    root: {
                      width: 'fit-content',
                    },
                  }}
                />
              )}
            />
          </Group>
        </Paper>

        <Tabs value={activeTab} onChange={setActiveTab} mb='md'>
          <Tabs.List>
            <Tabs.Tab value={CHANGE_TABS.DETAIL}>Change Details</Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value={CHANGE_TABS.DETAIL} pt='md'>
            <ChangeTemplateBuilder layouts={layouts} setLayouts={setLayouts} />
          </Tabs.Panel>
        </Tabs>
      </Box>
    </FormProvider>
  );
};

export default ChangeTemplateDetailPage;
