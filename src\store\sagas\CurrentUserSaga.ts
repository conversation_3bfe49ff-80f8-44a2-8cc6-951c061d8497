import { User<PERSON>pi } from '@api/UserApi';
import { callRequest } from '@core/api';
import { ResponseData } from '@core/schema/Common';
import { UserDetail } from '@core/schema/UserDetails';
import { currentUserSlice } from '@slices/CurrentUserSlice';
import { call, put, takeEvery } from 'redux-saga/effects';
function* fetchData() {
  yield put(
    currentUserSlice.actions.setValue({
      isFetching: true,
    }),
  );
  try {
    const response: ResponseData<UserDetail> = yield call(() => callRequest(UserApi.me()));
    yield put(
      currentUserSlice.actions.setValue({
        isFetching: false,
        userInfo: response.data,
      }),
    );
  } catch (ex) {
    yield put(
      currentUserSlice.actions.setValue({
        isFetching: false,
      }),
    );
  }
}

export function* currentUserSaga() {
  yield takeEvery(currentUserSlice.actions.fetchData, fetchData);
}
