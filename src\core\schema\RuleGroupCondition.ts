import { QueryBuilderCombinatorEnum } from '@components/queryBuilder/QueryBuilderCombinatorEnum';
import { QueryBuilderOperatorEnum } from '@components/queryBuilder/QueryBuilderOperatorEnum';
import { z } from 'zod';

export const QueryRuleTypeSchema = z.object({
  field: z.string(),
  operator: z.nativeEnum(QueryBuilderOperatorEnum),
  value: z.any(),
});
export type QueryRuleType = z.infer<typeof QueryRuleTypeSchema>;

const BaseRuleGroupTypeSchema = z.object({
  combinator: z.nativeEnum(QueryBuilderCombinatorEnum),
});

export type QueryRuleGroupType = z.infer<typeof BaseRuleGroupTypeSchema> & {
  rules: (QueryRuleGroupType | QueryRuleType)[];
};

export const QueryRuleGroupTypeSchema: z.ZodType<QueryRuleGroupType> = BaseRuleGroupTypeSchema.extend({
  rules: z.lazy(() => z.array(z.union([QueryRuleGroupTypeSchema, QueryRuleTypeSchema]))),
});
