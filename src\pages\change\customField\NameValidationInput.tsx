import { useState, useEffect } from 'react';
import { UseFormReturn } from 'react-hook-form';
import React from 'react';
import useFetch from '@core/hooks/useFetch';
import { KanbanInput } from 'kanban-design-system';
import { CUSTOM_FIELD_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { CustomFieldModel } from '@models/CustomField';
import { CustomFieldApi } from '@api/CustomFieldApi';

interface NameValidationInputProps {
  form: UseFormReturn<CustomFieldModel>;
  id: number;
  name?: string;
}

export const NameValidationInput: React.FC<NameValidationInputProps> = ({ form, id, name }) => {
  const [nameToCheck, setNameToCheck] = useState<string | undefined>();
  const [isNameTaken, setIsNameTaken] = useState<boolean | undefined>(undefined);

  const { data, refetch } = useFetch(CustomFieldApi.existsByName(id, nameToCheck || ''), {
    enabled: !!nameToCheck && nameToCheck.trim() !== '',
  });

  const { clearErrors, setError } = form;

  useEffect(() => {
    if (name) {
      setNameToCheck(name);
    }
  }, [name]);

  useEffect(() => {
    if (data?.data) {
      setIsNameTaken(data.data);
    }
  }, [data?.data]);

  useEffect(() => {
    if (isNameTaken) {
      setError('name', { type: 'manual', message: 'This name is existed' });
    } else {
      clearErrors('name');
    }
  }, [isNameTaken, setError, clearErrors]);

  const handleNameBlur = (event: React.FocusEvent<HTMLInputElement>) => {
    const name = event.target.value.trim();
    if (name && name !== nameToCheck) {
      form.setValue('name', name);
      setNameToCheck(name);
      refetch();
    }
  };

  return (
    <KanbanInput
      withAsterisk
      label='Name'
      {...form.register('name')}
      maxLength={CUSTOM_FIELD_MAX_LENGTH}
      onBlur={handleNameBlur}
      required
      error={form.formState.errors.name?.message}
    />
  );
};
