// eslint-disable-next-line filenames-simple/naming-convention
import { defineConfig, loadEnv } from '@rsbuild/core';
import { pluginReact } from '@rsbuild/plugin-react';
import { pluginSass } from '@rsbuild/plugin-sass';
import { pluginSvgr } from '@rsbuild/plugin-svgr';
import { pluginEslint } from '@rsbuild/plugin-eslint';
import { pluginTypeCheck } from '@rsbuild/plugin-type-check';
const { publicVars, rawPublicVars } = loadEnv({ prefixes: ['REACT_APP_'] });
const PORT = Number(process.env.PORT);

const GENERATE_SOURCEMAP = process.env.GENERATE_SOURCEMAP === 'true';
const DISABLE_ESLINT_PLUGIN = process.env.DISABLE_ESLINT_PLUGIN === 'true';
const DISABLE_TYPE_CHECK = process.env.DISABLE_TYPE_CHECK === 'true';

export default defineConfig({
  html: {
    template: './public/index.html',
  },
  plugins: [
    pluginReact(),
    pluginTypeCheck({ enable: !DISABLE_TYPE_CHECK }),
    pluginSass(),
    pluginSvgr({ mixedImport: true }),
    pluginEslint({ enable: !DISABLE_ESLINT_PLUGIN }),
  ],
  output: {
    distPath: {
      root: 'build',
    },
    polyfill: 'usage',
    sourceMap: GENERATE_SOURCEMAP,
  },
  source: {
    define: {
      ...publicVars,
      'process.env': JSON.stringify(rawPublicVars),
    },
  },
  server: {
    port: PORT,
  },
});
