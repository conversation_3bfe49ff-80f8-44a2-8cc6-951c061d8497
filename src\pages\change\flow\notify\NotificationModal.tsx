import React, { useEffect, useState } from 'react';
import { Box } from '@mantine/core';
import AmtModal from '@components/AmtModal';
import { KanbanButton } from 'kanban-design-system';
import { TemplateNotificationForm, TemplateNotificationFormProps } from './TemplateNotificationForm';
import { INPUT_REQUIRE } from '@core/message/MesageConstant';
import { EntityAction } from '@common/constants/EntityActionConstants';

type StrippedFormProps = Omit<TemplateNotificationFormProps, 'setMode'>;

interface NotificationModalProps extends StrippedFormProps {
  opened: boolean;
  onClose: () => void;
  onSave: () => void;
  initialMode: EntityAction;
}

export const NotificationModal: React.FC<NotificationModalProps> = ({ initialMode, onClose, onSave, opened, ...props }) => {
  const [mode, setMode] = useState<EntityAction>(initialMode);

  useEffect(() => {
    if (opened) {
      setMode(initialMode);
    }
  }, [opened, initialMode]);

  const handleSave = () => {
    if (props.selectedTemplate && props.notifyItems.length === 0) {
      props.setNotifyError(INPUT_REQUIRE);
    } else if (!props.notifyError) {
      onSave();
    }
  };

  return (
    <AmtModal
      size='80%'
      opened={opened}
      onClose={onClose}
      title={mode === EntityAction.EDIT ? 'Edit Notify' : 'Add Notify'}
      centered
      actions={
        <KanbanButton variant='filled' onClick={handleSave}>
          Save
        </KanbanButton>
      }>
      <Box onClick={(e) => e.stopPropagation()}>
        <TemplateNotificationForm {...props} setMode={setMode} />
      </Box>
    </AmtModal>
  );
};
