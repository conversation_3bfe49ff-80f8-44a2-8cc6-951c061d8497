import { KanbanButton, KanbanIconButton, KanbanSelect, KanbanTextarea, KanbanTitle } from 'kanban-design-system';

import React, { useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { useDisclosure } from '@mantine/hooks';
import { Box, Flex, SimpleGrid } from '@mantine/core';
import { zodResolver } from '@hookform/resolvers/zod';

import customStyled from '@resources/styles/Common.module.scss';
import { IconArrowLeft } from '@tabler/icons-react';
import { Controller, useForm, UseFormReturn, useWatch } from 'react-hook-form';
import UnsaveConfirmModal from '@components/utils/ConfirmMessageUtils';

import { buildUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import { COMMON_DESCRIPTION_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { CustomFieldModel, CustomFieldModelSchema } from '@models/CustomField';
import { CustomFieldApi } from '@api/CustomFieldApi';
import { NameValidationInput } from './NameValidationInput';
import { CUSTOM_FIELD_TYPE_LABEL, CustomFieldTypeEnum } from '@common/constants/CustomFieldType';
import { CustomFieldRenderer } from './CustomFieldInformation';
import equal from 'fast-deep-equal';
import { trimStringFields } from '@common/utils/Helpers';
import { buildNavigateState } from '@core/hooks/useSavedFilterCycle';
import { useBreadcrumbEntityName } from '@core/hooks/useBreadcrumbEntityName';
type DetailPageProps = {
  idProp?: number;
};

const DEFAULT_FORM_VALUE: CustomFieldModel = {
  id: null,
  name: '',
  description: '',
  type: 'SINGLE_LINE',
  isMultiple: false,
  isReference: false,
};

const SaveButton = ({ form }: { form: UseFormReturn<CustomFieldModel> }) => {
  const navigate = useNavigate();
  const { mutate: saveUserMutate } = useMutate(CustomFieldApi.save, {
    successNotification: 'Custom Field saved successfully',
    onSuccess: () => {
      navigate(buildUrl(ROUTE_PATH.CONFIGURATION_CHANGE_CUSTOM_FIELD), { state: buildNavigateState({ fromDetail: true }) });
    },
  });

  const onSubmit = useCallback(() => {
    form.trigger();

    const parsedData = CustomFieldModelSchema.safeParse(form.getValues());
    if (parsedData.success) {
      const trimmed = trimStringFields(parsedData.data, false);
      saveUserMutate(trimmed);
    }
  }, [form, saveUserMutate]);

  return (
    <KanbanButton size='xs' onClick={onSubmit}>
      Save
    </KanbanButton>
  );
};

export type StringMapEntry<T> = {
  [key: string]: T;
};
export const CustomFieldDetailtPage: React.FC<DetailPageProps> = () => {
  const id = Number(useParams().id);
  const { data: customFieldResponse } = useFetch(CustomFieldApi.findById(id), { enabled: !!id });
  const [customField, setCustomField] = useState<CustomFieldModel>(DEFAULT_FORM_VALUE);
  const [openedModal, { close: closeModal, open: openModal }] = useDisclosure(false);
  useBreadcrumbEntityName(customField.name);
  const navigate = useNavigate();

  const form = useForm<CustomFieldModel>({
    defaultValues: DEFAULT_FORM_VALUE,
    resolver: zodResolver(CustomFieldModelSchema),
    mode: 'onChange',
  });

  const { control } = form;
  const type = useWatch({ control: form.control, name: 'type' });

  useEffect(() => {
    if (id && customFieldResponse?.data) {
      setCustomField(customFieldResponse.data);
      form.reset({ ...customFieldResponse.data });
    }
  }, [form, id, customFieldResponse]);

  const handleExistWithoutSave = () => {
    const currentValues = form.getValues();

    if (!equal(currentValues, customField)) {
      openModal();
    } else {
      navigate(buildUrl(ROUTE_PATH.CONFIGURATION_CHANGE_CUSTOM_FIELD), { state: buildNavigateState({ fromDetail: true }) });
    }
  };

  return (
    <Box>
      <HeaderTitleComponent
        title=''
        rightSection={
          <Flex direction={'row'} align={'center'} gap={'xs'}>
            <KanbanButton size='xs' variant='light' onClick={handleExistWithoutSave}>
              Cancel
            </KanbanButton>
            <SaveButton form={form} />
          </Flex>
        }
        leftSection={
          <Flex direction={'row'} align={'center'} gap={'xs'}>
            <KanbanIconButton size='sm' variant='subtle' onClick={handleExistWithoutSave}>
              <IconArrowLeft />
            </KanbanIconButton>
            <KanbanTitle fz={'h4'}>{id ? 'Edit Custom Field' : 'Add Custom Field'}</KanbanTitle>
          </Flex>
        }
      />
      <Box className={customStyled.elementBorder}>
        <HeaderTitleComponent title='Change - New Field' />
        <SimpleGrid cols={2} spacing='md'>
          <NameValidationInput form={form} id={id} name={customFieldResponse?.data?.name} />

          <Controller
            name='type'
            control={control}
            render={({ field }) => (
              <KanbanSelect
                disabled={!!id}
                label='Field Type'
                allowDeselect={false}
                required
                data={CustomFieldTypeEnum.options.map((key) => ({ value: key, label: CUSTOM_FIELD_TYPE_LABEL[key] }))}
                {...field}
                onChange={(value) => {
                  field.onChange(value);
                  form.setValue('picklistOptions', []);
                  form.setValue('fieldConstraint', undefined);
                  form.setValue('condition', undefined);
                }}
              />
            )}
          />
        </SimpleGrid>
        <KanbanTextarea minRows={4} maxLength={COMMON_DESCRIPTION_MAX_LENGTH} label='Description' {...form.register('description')} />
      </Box>

      <CustomFieldRenderer form={form} type={type} />

      <UnsaveConfirmModal
        opened={openedModal}
        onClose={closeModal}
        onConfirm={() => navigate(buildUrl(ROUTE_PATH.CONFIGURATION_CHANGE_CUSTOM_FIELD), { state: buildNavigateState({ fromDetail: true }) })}
      />
    </Box>
  );
};
export default CustomFieldDetailtPage;
