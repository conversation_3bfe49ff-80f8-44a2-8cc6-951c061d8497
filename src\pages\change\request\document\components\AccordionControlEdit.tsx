import React, { useState } from 'react';
import { Box, Flex } from '@mantine/core';
import { KanbanInput, KanbanText } from 'kanban-design-system';
import { IconTrash, IconEdit } from '@tabler/icons-react';
import { useController, useFormContext } from 'react-hook-form';
import { ChangeRequestDocumentGroupFormWrapper } from '@models/ChangeRequestDocumentGroupModel';
import keyboardKey from 'keyboard-key';
import { COMMON_MAX_LENGTH } from '@common/constants/ValidationConstant';
const getCode = keyboardKey.getCode;

interface Props {
  index: number;
  fieldName: `items.${number}.documentGroupName`;
  handleRemoveGroup: (indexToRemove: number) => void;
}

const AccordionControlEdit = ({ fieldName, handleRemoveGroup, index }: Props) => {
  const { control, trigger } = useFormContext<ChangeRequestDocumentGroupFormWrapper>();
  const [isEditing, setIsEditing] = useState(false);

  const {
    field,
    fieldState: { error },
  } = useController({ name: fieldName, control });

  return (
    <Flex justify='space-between' align='center' w='100%'>
      <Flex align='center' gap='xs'>
        {isEditing ? (
          <KanbanInput
            {...field}
            maxLength={COMMON_MAX_LENGTH}
            error={error?.message}
            autoFocus
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
            onBlur={() => {
              const trimmed = field.value.trim();
              field.onChange(trimmed);
              field.onBlur();
              if (trimmed) {
                setIsEditing(false);
              }
            }}
            onKeyDown={(e) => {
              if (keyboardKey.Enter === getCode(e)) {
                e.preventDefault();
                const trimmed = field.value.trim();
                field.onChange(trimmed);
                if (trimmed) {
                  setIsEditing(false);
                }
              }
            }}
            onChange={(e) => {
              field.onChange(e);
              trigger(field.name);
            }}
          />
        ) : (
          <>
            <KanbanText fw={500}>{field.value}</KanbanText>
            <Box
              variant='transparent'
              onClick={(e) => {
                e.stopPropagation();
                setIsEditing(true);
              }}>
              <IconEdit size={18} color='blue' />
            </Box>
          </>
        )}
      </Flex>
      <Box
        component='span'
        onClick={(e) => {
          e.stopPropagation();
          handleRemoveGroup(index);
        }}>
        <IconTrash color='red' />
      </Box>
    </Flex>
  );
};

export default AccordionControlEdit;
