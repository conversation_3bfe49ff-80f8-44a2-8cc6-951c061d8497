import { createPageSchema } from '@core/schema/Common';
import { Flex, Pill, PillGroup } from '@mantine/core';
import { IconReload } from '@tabler/icons-react';
import { KanbanButton, KanbanText, KanbanTooltip } from 'kanban-design-system';
import React from 'react';
import { z } from 'zod';
import customStyled from '@resources/styles/Common.module.scss';

interface AmtPillProps {
  values: Record<string, string>;

  pageResonse?: z.infer<ReturnType<typeof createPageSchema>> | null;
  onChangePage?: (val: number) => void;
  onRemoveItem?: (key: string) => void;
}
const AmtPillsComponent = ({ onChangePage, onRemoveItem, pageResonse, values }: AmtPillProps) => {
  return (
    <>
      {pageResonse && !pageResonse.last && (
        <KanbanButton size='xs' variant='light' onClick={onChangePage}>
          <IconReload size={16} style={{ marginRight: 5 }} />
        </KanbanButton>
      )}
      <Flex direction={'row'} align={'center'} gap={'xs'} mt='xs'>
        <PillGroup>
          {Object.keys(values).map((key) => (
            <KanbanTooltip key={key} label={values[key]} position='top-start'>
              <Pill
                onRemove={() => {
                  if (onRemoveItem) {
                    onRemoveItem(key);
                  }
                }}
                key={key}
                withRemoveButton
                size='md'
                bg='white'
                bd='1px solid var(--mantine-color-primary-2)'>
                <KanbanText className={customStyled.clipText} maw={'100px'}>
                  {values[key]}
                </KanbanText>
              </Pill>
            </KanbanTooltip>
          ))}
        </PillGroup>
      </Flex>
    </>
  );
};
export default AmtPillsComponent;
