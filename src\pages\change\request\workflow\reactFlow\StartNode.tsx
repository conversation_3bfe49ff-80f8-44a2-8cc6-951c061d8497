import { Box, Text } from '@mantine/core';
import { Handle, Position } from '@xyflow/react';
import React from 'react';
import stylesCss from './StartNode.module.scss';

interface StartNodeProps {
  id: string;
  data: {
    label: string;
    icon: string;
  };
  selected?: boolean;
}

function StartNode({ data, selected }: StartNodeProps) {
  const iconSrc = data.icon;

  return (
    <Box className={`${stylesCss.startNode} ${selected ? stylesCss.selected : ''}`}>
      {/* Main Icon */}
      <img src={iconSrc} alt={data.label} className={stylesCss.nodeIcon} />
      {/* Node Labels */}
      <Text lineClamp={2} size='sm' className={stylesCss.nodeLabel}>
        {data.label}
      </Text>
      {/* Connection Handles */}
      <Handle type='source' position={Position.Right} className={`${stylesCss.handle} ${stylesCss.sourceHandle}`} id='source-right' />
    </Box>
  );
}

export default StartNode;
