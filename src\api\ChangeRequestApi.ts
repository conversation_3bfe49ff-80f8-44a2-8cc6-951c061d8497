import { BaseURL } from '@common/constants/BaseUrl';
import { RequestConfig } from '@core/api';
import { PaginationRequest } from './Type';
import { tableAffectedToMultiColumnFilterPaginationRequestModel } from '@common/utils/TableUtils';
import { TableAffactedSafeType } from 'kanban-design-system';
import { ResponseData, createResponseSchema, Page, createPageSchema } from '@core/schema/Common';
import {
  ChangeRequest,
  ChangeRequestModel,
  ChangeRequestModelSchema,
  ChangeRequestSchema,
  ChangeRequestTransitionModel,
} from '@core/schema/ChangeRequest';
import qs from 'qs';
import { ChangeRequestNote, ChangeRequestNoteSchema } from '@core/schema/ChangeRequestNote';
import { createRequest } from '@api/Utils';
import { ChangeWorkflow } from '@core/schema/ChangeWorkflowNode';
import { WorkflowData } from '@pages/change/request/workflow/WorkflowDetailPage';
import { User, UserSchema } from '@core/schema/User';
import { ChangeRequestRole, ChangeRequestApprovalRoleListSchema } from '@core/schema/ChangeRequestRole';
import { z } from 'zod';
import { ChangeRequestApprovalSchema } from '@core/schema/ChangeRequestApproval';
import { ApprovalNotificationFormModel, ApprovalNotificationFormSchema } from '@models/ApprovalNotificationFormModel';

export class ChangeRequestApi {
  static findAll(pagination: TableAffactedSafeType): RequestConfig<ResponseData<Page<ChangeRequest>>, PaginationRequest> {
    return {
      url: `${BaseURL.changeRequest}`,
      method: 'POST',
      schema: createResponseSchema(createPageSchema(ChangeRequestSchema)),
      data: tableAffectedToMultiColumnFilterPaginationRequestModel(pagination),
    };
  }

  static deleteById(id: number): RequestConfig<ResponseData<void>> {
    return {
      url: `${BaseURL.changeRequest}/${id}`,
      method: 'DELETE',
    };
  }

  static deleteByIdIn(ids: number[]): RequestConfig<ResponseData<void>> {
    return {
      url: `${BaseURL.changeRequest}`,
      method: 'DELETE',
      params: { ids },
      paramsSerializer: (params) => qs.stringify(params, { arrayFormat: 'repeat' }),
    };
  }

  static findAllNote(pagination: TableAffactedSafeType, id: number): RequestConfig<ResponseData<Page<ChangeRequestNote>>, PaginationRequest> {
    return {
      url: `${BaseURL.changeRequest}/${id}/notes`,
      method: 'POST',
      schema: createResponseSchema(createPageSchema(ChangeRequestNoteSchema)),
      data: tableAffectedToMultiColumnFilterPaginationRequestModel(pagination),
    };
  }

  static upsertNote(data: ChangeRequestNote) {
    return createRequest({
      url: `${BaseURL.changeRequest}/${data.changeRequestId}/notes`,
      method: 'PUT',
      schema: createResponseSchema(ChangeRequestNoteSchema),
      data,
    });
  }

  static findAllWorkflows(changeId: number): RequestConfig<ResponseData<ChangeWorkflow[]>> {
    return {
      url: `${BaseURL.changeRequest}/${changeId}/workflows`,
      method: 'GET',
    };
  }

  static saveWorkflow(data: WorkflowData): RequestConfig<ResponseData<ChangeWorkflow[]>> {
    return {
      url: `${BaseURL.changeRequest}/${data.changeId}/workflows`,
      method: 'POST',
      data: data,
    };
  }

  static findDetailWorkflow(changeId: number, workflowId: number): RequestConfig<ResponseData<ChangeWorkflow>> {
    return {
      url: `${BaseURL.changeRequest}/${changeId}/workflows/${workflowId}`,
      method: 'GET',
    };
  }

  static deleteWorkflow(data: { changeId: number; workflowId: number }): RequestConfig<ResponseData<number>> {
    return {
      url: `${BaseURL.changeRequest}/${data.changeId}/workflows/${data.workflowId}`,
      method: 'DELETE',
    };
  }

  static findAllUsersCab(pagination: TableAffactedSafeType, changeId: number): RequestConfig<ResponseData<Page<User>>, PaginationRequest> {
    return {
      url: `${BaseURL.changeRequest}/${changeId}/cabs`,
      method: 'POST',
      schema: createResponseSchema(createPageSchema(UserSchema)),
      data: tableAffectedToMultiColumnFilterPaginationRequestModel(pagination),
    };
  }

  static upsertChangeRequest(data: ChangeRequest) {
    return createRequest({
      url: `${BaseURL.changeRequest}/${data.id}`,
      method: 'PUT',
      schema: createResponseSchema(ChangeRequestSchema),
      data,
    });
  }

  static getSameTitleDetail(id: number, title?: string) {
    return createRequest({
      url: `${BaseURL.changeRequest}/${id}/check-title?name=${title}`,
      method: 'GET',
      schema: createResponseSchema(ChangeRequestSchema || null),
    });
  }

  static findAllChangeRequestApprovalRoles(id: number) {
    return createRequest({
      url: `${BaseURL.changeRequest}/${id}/approval-roles`,
      method: 'GET',
      schema: createResponseSchema(ChangeRequestApprovalRoleListSchema),
    });
  }

  static saveListChangeRoles(props: { changeRequestId: number; data?: ChangeRequestRole[] | null }) {
    return createRequest({
      url: `${BaseURL.changeRequest}/${props.changeRequestId}/change-request-roles`,
      method: 'PUT',
      schema: createResponseSchema(z.string()),
      data: props.data,
    });
  }

  static findWithId(id: number) {
    return createRequest({
      url: `${BaseURL.changeRequest}/${id}`,
      method: 'GET',
      schema: createResponseSchema(ChangeRequestModelSchema),
    });
  }

  static saveOrUpdate(data: ChangeRequestModel) {
    return createRequest({
      url: `${BaseURL.changeRequest}/${data.id}`,
      method: 'PUT',
      schema: createResponseSchema(ChangeRequestModelSchema),
      data,
    });
  }

  static sendApprovalEmail(data: ApprovalNotificationFormModel) {
    return createRequest({
      url: `${BaseURL.mail}/send-approval-email`,
      method: 'POST',
      schema: createResponseSchema(ApprovalNotificationFormSchema),
      data: data,
    });
  }

  //change approval
  static findApprovalResults(changeRequestApprovalId: number) {
    return createRequest({
      url: `${BaseURL.changeRequest}/${changeRequestApprovalId}/approval-info`,
      method: 'GET',
      schema: createResponseSchema(ChangeRequestApprovalSchema),
    });
  }

  static processChangeRequestCoordinatorTransition({ changeRequestId, data }: { changeRequestId: number; data: ChangeRequestTransitionModel }) {
    return createRequest({
      url: `${BaseURL.changeRequest}/${changeRequestId}/status`,
      method: 'PATCH',
      schema: createResponseSchema(ChangeRequestModelSchema),
      data,
    });
  }
  static processChangeRequestApproverTransition(data: ChangeRequestTransitionModel) {
    return createRequest({
      url: `${BaseURL.changeRequest}/approval-requests/reply`,
      method: 'POST',
      schema: createResponseSchema(ChangeRequestModelSchema),
      data,
    });
  }
}
