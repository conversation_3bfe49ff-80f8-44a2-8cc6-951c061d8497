import { AdvancedFilterMappingType, ColumnType, getDefaultTableAffected, KanbanIconButton, KanbanTitle, renderDateTime } from 'kanban-design-system';
import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanButton } from 'kanban-design-system';
import { KanbanTable, KanbanTableProps, TableAffactedSafeType } from 'kanban-design-system';
import { Box, Flex, Tooltip } from '@mantine/core';
import { IconEdit, IconPlus, IconTrash } from '@tabler/icons-react';
import equal from 'fast-deep-equal';
import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import useFetch from '@core/hooks/useFetch';
import useMutate from '@core/hooks/useMutate';
import { DateRangeFilter, dateRangeKeys } from '@components/AmtDateRangeInput';
import { LocalStorageKey } from '@common/constants/LocalStorageKeyConstants';
import { buildDetailUrl, ROUTE_PATH } from '@common/utils/RouterUtils';
import { EntityAction } from '@common/constants/EntityActionConstants';
import { FilterTextInput } from '@components/AmtFilterTextInput';
import { deleteConfirm } from '@components/utils/ConfirmMessageUtils';
import { parseFilterWithDate } from '@common/utils/DateUtils';
import useSavedFilterCycle from '@core/hooks/useSavedFilterCycle';
import { useSavedColumns } from '@core/hooks/useSavedColumns';
import { AmtShortTextContent, AmtShortTextLink } from '@components/AmtShortTextContent';
import { ChangeDocument } from '@core/schema/ChangeDocument';
import { ChangeDocumentApi } from '@api/ChangeDocumentApi';

export type DocumentPageFilter = DateRangeFilter & {
  name?: string;
};

const initOrUpdatedFilterPayloads = (prev?: TableAffactedSafeType<ChangeDocument>, initFilters?: DocumentPageFilter) => {
  const prevFilter = prev || getDefaultTableAffected();
  if (initFilters) {
    const filterObj: AdvancedFilterMappingType<ChangeDocument> = {
      ['name']: {
        filterOption: 'contains',
        value: {
          fromValue: initFilters.name,
        },
      },
    };
    return { ...prevFilter, advancedFilterMapping: { ...prevFilter.advancedFilterMapping, ...filterObj } };
  }
  return prevFilter;
};

const columns: ColumnType<ChangeDocument>[] = [
  {
    name: 'name',
    title: 'Document Name',
    customRender: (_data, rowData) => {
      return (
        <AmtShortTextLink
          routePath={ROUTE_PATH.CONFIGURATION_CHANGE_DOCUMENT_DETAIL}
          entityId={rowData.id}
          data={rowData.name}
          disableShorten
          fw={500}
        />
      );
    },
  },
  {
    name: 'description',
    title: 'Description',
    customRender: (_data, rowData) => <AmtShortTextContent data={rowData.description || ''} />,
  },
  {
    name: 'changeLevels',
    title: 'Change Level',
    customRender: (_data, rowData) => {
      const levels = rowData.changeLevels;
      return (levels || []).join(', ');
    },
  },

  {
    name: 'createdDate',
    title: 'Created Date',
    hidden: true,
    customRender: renderDateTime,
  },
  {
    name: 'createdBy',
    title: 'Created By',
    hidden: true,
  },
  {
    name: 'modifiedDate',
    title: 'Modified Date',
    hidden: true,
    customRender: renderDateTime,
  },
  {
    name: 'modifiedBy',
    title: 'Modified By',
    hidden: true,
  },
];

export const DocumentManagementPage = () => {
  const [totalRecords, setTotalRecords] = useState(0);

  const navigate = useNavigate();

  const [tableAffectedChange, setTableAffectedChange] = useState<TableAffactedSafeType>();
  const [columnOrders, setColumnOrders] = useState<string[]>();
  const [inputFilters, setInputFilters] = useState<DocumentPageFilter>({});
  const [savedFilters, setSavedFilters] = useSavedFilterCycle({
    key: LocalStorageKey.SEARCH_VALUE_CHANGE_DOCUMENT_PAGE,
    deserialize: (str) => parseFilterWithDate<DocumentPageFilter>(str, dateRangeKeys),
    setInputFilters,
  });

  const savedColumns = useSavedColumns<ChangeDocument>(LocalStorageKey.COLUMN_DISPLAY_CHANGE_DOCUMENT_PAGE, columns, columnOrders);

  const { data, refetch: refetchList } = useFetch(ChangeDocumentApi.findAll(initOrUpdatedFilterPayloads(tableAffectedChange, savedFilters)), {
    enabled: !!tableAffectedChange,
  });

  useEffect(() => {
    if (data?.data?.content) {
      setTotalRecords(data.data.totalElements);
    }
  }, [data?.data]);

  const handleSearch = useCallback(
    (filters?: DocumentPageFilter) => {
      const appliedFilters = filters ?? inputFilters;
      setSavedFilters((prev) => ({ ...prev, ...appliedFilters }));
      setTableAffectedChange((prev) => {
        return initOrUpdatedFilterPayloads(prev, appliedFilters);
      });
    },
    [inputFilters, setSavedFilters],
  );

  const handleClearNameFilter = () => {
    const updatedFilters = { ...inputFilters, name: '' };
    handleSearch(updatedFilters);
    setInputFilters(updatedFilters);
  };

  const tableProps: KanbanTableProps<ChangeDocument> = useMemo(() => {
    const tblProps: KanbanTableProps<ChangeDocument> = {
      columns: savedColumns,
      data: data?.data?.content ?? [],
      customRowProps: (data) => ({
        onDoubleClick: () => {
          navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_CHANGE_DOCUMENT_DETAIL, data.id, EntityAction.EDIT));
        },
      }),

      serverside: {
        totalRows: totalRecords,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffectedChange, dataSet)) {
            setTableAffectedChange(dataSet);
          }
        },
      },
      columnOrderable: {
        onOrder: (data) => {
          setColumnOrders(data.map((it) => it.name));
        },
      },
      actions: {
        customAction: (data) => {
          return <ActionColumn document={data} refetch={refetchList} />;
        },
      },
      pagination: {
        enable: true,
      },
      showNumericalOrderColumn: true,
    };
    return tblProps;
  }, [savedColumns, data?.data?.content, totalRecords, navigate, tableAffectedChange, refetchList]);

  return (
    <Box>
      <HeaderTitleComponent
        title=''
        rightSection={
          <Flex>
            <KanbanButton
              size='xs'
              leftSection={<IconPlus />}
              onClick={() => {
                navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_CHANGE_DOCUMENT_DETAIL, 0, EntityAction.CREATE));
              }}>
              Add Document
            </KanbanButton>
          </Flex>
        }
        leftSection={<KanbanTitle fz={'h4'}>Document List</KanbanTitle>}
      />
      <Flex dir='row' gap='xs' align={'center'}>
        <FilterTextInput
          onClear={handleClearNameFilter}
          placeholder='Document Name'
          value={inputFilters.name ?? ''}
          onChange={(val) => {
            setInputFilters({ ...inputFilters, name: val.target.value });
          }}
          onBlur={() => {
            handleSearch(inputFilters);
          }}
        />
      </Flex>
      <KanbanTable {...tableProps} title='' />
    </Box>
  );
};

const ActionColumn = ({ document, refetch }: { document: ChangeDocument; refetch: () => void }) => {
  const navigate = useNavigate();

  const { mutate: deleteMutate } = useMutate(ChangeDocumentApi.deleteById, {
    successNotification: 'Document deleted successfully',
    confirm: deleteConfirm(),
    onSuccess: () => refetch(),
  });

  return (
    <Flex gap='xs' align='center'>
      <Tooltip label='Edit'>
        <KanbanIconButton
          variant='transparent'
          size={'sm'}
          onClick={() => {
            navigate(buildDetailUrl(ROUTE_PATH.CONFIGURATION_CHANGE_DOCUMENT_DETAIL, document.id, EntityAction.EDIT));
          }}>
          <IconEdit />
        </KanbanIconButton>
      </Tooltip>

      <Tooltip label='Delete'>
        <KanbanIconButton
          variant='transparent'
          color='red'
          size={'sm'}
          onClick={() =>
            deleteMutate(document.id, {
              confirm: deleteConfirm([document.name]),
            })
          }>
          <IconTrash />
        </KanbanIconButton>
      </Tooltip>
    </Flex>
  );
};

export default DocumentManagementPage;
