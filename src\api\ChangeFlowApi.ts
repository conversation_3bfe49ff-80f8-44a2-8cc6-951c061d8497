import { BaseURL } from '@common/constants/BaseUrl';
import { RequestConfig } from '@core/api';
import { PaginationRequest } from './Type';
import { createRequest } from './Utils';
import { tableAffectedToMultiColumnFilterPaginationRequestModel } from '@common/utils/TableUtils';
import { TableAffactedSafeType } from 'kanban-design-system';
import { ResponseData, createResponseSchema, Page, createPageSchema } from '@core/schema/Common';
import { z } from 'zod';
import { ChangeFlowModel } from '@models/ChangeFlowModel';
import qs from 'qs';
import { ChangeFlow, ChangeFlowSchema } from '@core/schema/ChangeFlowSchema';

export class ChangeFlowApi {
  static findAll(pagination: TableAffactedSafeType): RequestConfig<ResponseData<Page<ChangeFlow>>, PaginationRequest> {
    return {
      url: `${BaseURL.changeFlow}/all`,
      method: 'POST',
      schema: createResponseSchema(createPageSchema(ChangeFlowSchema)),
      data: tableAffectedToMultiColumnFilterPaginationRequestModel(pagination),
    };
  }

  static findById(id: number) {
    return createRequest({
      url: `${BaseURL.changeFlow}/:id`,
      method: 'GET',
      schema: createResponseSchema(ChangeFlowSchema),
      pathVariable: {
        id,
      },
    });
  }

  static save(user: ChangeFlowModel): RequestConfig<ResponseData<ChangeFlow>> {
    return {
      url: `${BaseURL.changeFlow}`,
      method: 'POST',
      schema: createResponseSchema(ChangeFlowSchema),
      data: user,
    };
  }

  static deleteById(id: number): RequestConfig<ResponseData<void>> {
    return {
      url: `${BaseURL.changeFlow}/${id}`,
      method: 'DELETE',
    };
  }

  static deleteByIdIn(ids: number[]): RequestConfig<ResponseData<void>> {
    return {
      url: `${BaseURL.changeFlow}`,
      method: 'DELETE',
      params: { ids },
      paramsSerializer: (params) => qs.stringify(params, { arrayFormat: 'repeat' }),
    };
  }

  static existsByName({ id, name }: { id: number | undefined; name: string }) {
    return createRequest({
      url: `${BaseURL.changeFlow}/${id}/exists?name=${name}`,
      method: 'GET',
      schema: createResponseSchema(z.boolean()),
    });
  }
}
