import { SuggestionKeyDownProps, SuggestionProps } from '@tiptap/suggestion';
import { forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import React from 'react';
import keyboardKey from 'keyboard-key';

import useFetch from '@core/hooks/useFetch';
import { Card, ScrollArea } from '@mantine/core';
import { MentionItem } from './MentionItem';
import { useDebounceCallback } from 'kanban-design-system';
import { CustomFieldApi } from '@api/CustomFieldApi';
import { DebounceTime } from '@common/constants/DebounceTimeConstant';
const getCode = keyboardKey.getCode;

export interface MentionListProps extends SuggestionProps {}

export interface MentionListActions {
  onKeyDown: (props: SuggestionKeyDownProps) => boolean;
}
export const MentionList = forwardRef<MentionListActions, MentionListProps>(({ command, query }, ref) => {
  const [nameSearch, setNameSearch] = useState('');
  const { data: listCustomObject } = useFetch(CustomFieldApi.findAllAvailableFieldsByName(nameSearch), {
    placeholderData: (prev) => prev,
    showLoading: false,
  });

  const debounceFunc = useDebounceCallback((query) => setNameSearch(query), DebounceTime.MILLISECOND_300);

  useEffect(() => {
    debounceFunc(query);
  }, [debounceFunc, query]);

  const allItems = useMemo(() => {
    return listCustomObject?.data || [];
  }, [listCustomObject?.data]);

  // Handle selection of an item
  const handleCommand = (index: number) => {
    const selectedItem = allItems[index];
    if (selectedItem) {
      command({ id: selectedItem.id, label: selectedItem.name });
    }
  };

  const [hoverIndex, setHoverIndex] = useState(0);
  useImperativeHandle(ref, () => ({
    onKeyDown: ({ event }) => {
      const keyCode = getCode(event);

      if (keyboardKey.ArrowUp === keyCode) {
        setHoverIndex((prev) => Math.max(prev - 1, 0));
        return true;
      }

      if (keyboardKey.ArrowDown === keyCode) {
        setHoverIndex((prev) => Math.min(prev + 1, allItems.length - 1));
        return true;
      }

      if (keyboardKey.Enter === keyCode) {
        handleCommand(hoverIndex);
        return true;
      }

      return false;
    },
  }));

  if (allItems.length === 0) {
    return null;
  }

  return (
    <Card>
      <ScrollArea.Autosize mah={300}>
        {allItems.map((item, index) => (
          <MentionItem
            key={item.id}
            isActive={hoverIndex === index}
            onMouseEnter={() => setHoverIndex(index)}
            onMouseDown={(e) => {
              e.preventDefault();
              handleCommand(index);
            }}>
            {item.name}
          </MentionItem>
        ))}
      </ScrollArea.Autosize>
    </Card>
  );
});

MentionList.displayName = 'MentionList';
