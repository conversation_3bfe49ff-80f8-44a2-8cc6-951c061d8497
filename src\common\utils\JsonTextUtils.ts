import { DocumentNode, ParagraphNode, ShortRichNode } from '@models/EmailTemplateModel';
import { JsonKeyConstants } from '@common/constants/JsonKeyConstants';
import { Editor, JSONContent } from '@tiptap/react';

export function removeMentionLabelAndStringify(doc: string | DocumentNode): string {
  let node: DocumentNode;

  if (typeof doc === 'string') {
    try {
      node = JSON.parse(doc) as DocumentNode;
    } catch {
      return doc;
    }
  } else {
    node = doc;
  }

  // Function to delete label in MentionNode
  function removeMentionLabelFromShortRichNode(n: ShortRichNode): ShortRichNode {
    if (JsonKeyConstants.MENTION_TYPE === n.type) {
      const { label: _label, mentionSuggestionChar: _mentionSuggestionChar, ...restAttrs } = n.attrs;
      return {
        type: n.type,
        attrs: restAttrs,
      };
    }

    return n;
  }

  function removeMentionLabelFromParagraphNode(p: ParagraphNode): ParagraphNode {
    if (!p.content) {
      return p;
    }
    return {
      ...p,
      content: p.content.map(removeMentionLabelFromShortRichNode),
    };
  }

  // Do this for all ParagraphNodes
  const cleaned: DocumentNode = node.map(removeMentionLabelFromParagraphNode);

  return JSON.stringify(cleaned);
}

export function parseEditorContent(jsonContent: JSONContent | undefined): string {
  if (!Array.isArray(jsonContent) || jsonContent.length === 0) {
    return '';
  }
  const isEmpty = jsonContent.every(
    (item) =>
      JsonKeyConstants.PARAGRAPH_TYPE === item.type &&
      (!item.content?.length || item.content.every((c: JSONContent) => JsonKeyConstants.TEXT_TYPE === c.type && !c.text?.trim())),
  );
  if (isEmpty) {
    return '';
  }
  return JSON.stringify(jsonContent);
}

export const getActualContentLength = (editor: Editor) => {
  let length = 0;
  let firstBlock = true;

  const doc = editor.state.doc;
  doc.descendants((node) => {
    if (node.isText) {
      length += node.text?.length || 0;
    } else if (node.type.name === JsonKeyConstants.MENTION_TYPE) {
      const label = node.attrs.label || '';
      length += 1 + label.length;
    } else if (JsonKeyConstants.PARAGRAPH_TYPE === node.type.name) {
      // Add 1 character to represent the line break between blocks,
      // but skip adding for the very first block (no line break before it).
      if (!firstBlock) {
        length += 1;
      }
      firstBlock = false;
    }
    return true;
  });

  return length;
};

export const getRemaining = (editor: Editor, contentMaxLength: number): number => {
  const charCount = getActualContentLength(editor);
  const { from, to } = editor.state.selection;
  const selectedLength = to - from;
  const safeSelectedLength = Math.min(selectedLength, charCount);
  return Math.max(0, contentMaxLength - (charCount - safeSelectedLength));
};
