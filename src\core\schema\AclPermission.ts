import { PermissionAction, PermissionActionModule, PermissionActionType } from '@common/constants/AclPermissionConstants';
import { z } from 'zod';

//ft/role model
//for validate
export const AclPermissionSchema = z.object({
  permission: z.nativeEnum(PermissionAction),
  module: z.nativeEnum(PermissionActionModule).default(PermissionActionModule.OTHER).nullish(),
  type: z.nativeEnum(PermissionActionType).default(PermissionActionType.MODULE).nullish(),
  id: z.number().nullish(),
  resourceParentId: z.number().nullish(),
  resourceId: z.number().nullish(),
  description: z.string().nullish(),
  isChecked: z.boolean().nullish(),
});

export type AclPermissionModel = z.infer<typeof AclPermissionSchema>;

//for init data
export class AclPermission {
  permission: PermissionAction;
  module?: PermissionActionModule | null;
  type?: PermissionActionType | null;
  id?: number | null;
  resourceId?: number | null;
  resourceParentId?: number | null;
  description?: string | null;
  //view group module/submodule 298 297

  constructor(model: AclPermissionModel) {
    this.id = model.id;
    this.permission = model.permission;
    this.module = model.module;
    this.type = model.type;
    this.resourceId = model.resourceId;
    this.resourceParentId = model.resourceParentId;
    this.description = model.description;
  }
}
