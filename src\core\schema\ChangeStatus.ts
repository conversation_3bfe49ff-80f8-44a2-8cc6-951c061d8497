import { ChangeStageTypeEnum } from '@common/constants/ChageStageType';
import { z } from 'zod';

export const ChangeStatusSchema = z.object({
  id: z.number(),
  name: z.string(),
  description: z.string().nullish(),
  action: z.string().nullish(),
  stage: ChangeStageTypeEnum,
  createdDate: z.string(),
  createdBy: z.string().optional(),
  modifiedBy: z.string().optional(),
  modifiedDate: z.string(),
});

export type ChangeStatus = z.infer<typeof ChangeStatusSchema>;

const ChangeStatusMinimalSchema = ChangeStatusSchema.pick({
  id: true,
  name: true,
  stage: true,
});

export type ChangeStatusMinimal = z.infer<typeof ChangeStatusMinimalSchema>;
