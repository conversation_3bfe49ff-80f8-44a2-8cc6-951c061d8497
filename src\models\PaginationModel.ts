import { SortType } from '@common/constants/SortType';
import { z } from 'zod';

export const PaginationModelSchema = z.object({
  page: z.number().optional(),
  size: z.number().optional(),
  search: z.string().optional(),
  sortBy: z.string().optional(),
  sortOrder: z.nativeEnum(SortType).optional(),
  isReverse: z.boolean().optional(),
});

export type PaginationModel = z.infer<typeof PaginationModelSchema>;
