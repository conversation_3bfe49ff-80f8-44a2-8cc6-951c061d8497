import React from 'react';
import { Box, Flex, Grid, Paper, Transition } from '@mantine/core';
import { KanbanButton, KanbanInput, KanbanTitle } from 'kanban-design-system';
import { IconX } from '@tabler/icons-react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { COMMON_MAX_LENGTH } from '@common/constants/ValidationConstant';

import UserSelectComponent from '@pages/change/request/document/components/selectBox/UserSelectComponent';
import { ChangeRequestDocumentGroupForm, ChangeRequestDocumentGroupFormSchema } from '@models/ChangeRequestDocumentGroupModel';

interface AddDocumentGroupFormProps {
  isFormOpen: boolean;
  handleAddGroup: (group: ChangeRequestDocumentGroupForm) => void;
  handleCloseModalAdd: () => void;
}

const DocumentGroupForm: React.FC<AddDocumentGroupFormProps> = ({ handleAddGroup, handleCloseModalAdd, isFormOpen }) => {
  const form = useForm<ChangeRequestDocumentGroupForm>({
    resolver: zodResolver(ChangeRequestDocumentGroupFormSchema),
    mode: 'onBlur',
    defaultValues: {
      documentGroupName: '',
      owners: [],
    },
  });

  const {
    control,
    formState: { errors },
    getValues,
    reset,
    trigger,
  } = form;

  const handleSave = async () => {
    const isValid = await trigger();
    if (!isValid) {
      return;
    }

    const data = getValues();
    handleAddGroup(data);
    handleCloseModalAdd();
    reset();
  };

  return (
    <Grid.Col span={3}>
      <Transition mounted={isFormOpen} transition='slide-left' timingFunction='ease'>
        {(styles) => (
          <Paper withBorder p='md' radius='md' style={styles}>
            {/* Header */}
            <Box bg='gray.1' px='sm' py={8} mb='sm' style={{ borderRadius: 8 }}>
              <Flex justify='space-between' align='center' mb='sm'>
                <KanbanTitle order={5} c='gray.7'>
                  Add document
                </KanbanTitle>
                <IconX size={20} onClick={handleCloseModalAdd} style={{ cursor: 'pointer' }} />
              </Flex>
            </Box>

            {/* Document Name */}
            <Controller
              name='documentGroupName'
              control={control}
              render={({ field }) => (
                <KanbanInput {...field} label='Document name' withAsterisk maxLength={COMMON_MAX_LENGTH} error={errors.documentGroupName?.message} />
              )}
            />

            {/* Uploaders */}
            <Controller
              name='owners'
              control={control}
              render={({ field }) => (
                <UserSelectComponent
                  label={'Owner'}
                  value={field.value.map((u) => ({
                    id: u.username,
                    name: u.displayName || '',
                  }))}
                  onChange={(val) =>
                    field.onChange(
                      val.map((v) => ({
                        username: v.id,
                        displayName: v.name,
                      })),
                    )
                  }
                  error={errors.owners?.message}
                  onBlur={field.onBlur}
                />
              )}
            />

            {/* Actions */}
            <Flex mt='md' gap='xs'>
              <KanbanButton size='xs' variant='light' onClick={handleCloseModalAdd}>
                Close
              </KanbanButton>
              <KanbanButton size='xs' onClick={handleSave}>
                Save
              </KanbanButton>
            </Flex>
          </Paper>
        )}
      </Transition>
    </Grid.Col>
  );
};

export default DocumentGroupForm;
