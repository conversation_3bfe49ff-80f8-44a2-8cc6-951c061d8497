import React, { useState } from 'react';
import { KanbanButton, KanbanIconButton, KanbanModal, KanbanSelect, KanbanTable, KanbanText, KanbanTextarea } from 'kanban-design-system';
import { Badge, Checkbox, Flex } from '@mantine/core';

import { IconMessage } from '@tabler/icons-react';
import { useFormContext } from 'react-hook-form';
import { ChangeRequestRoleList } from '@core/schema/ChangeRequestRole';
import { APPROVAL_ACTION_OPTIONS, ChangeRequestApprovalMode } from '@common/constants/ChangeRequestApprovalConstants';
import { statusConfig, WAITING_APPROVAL } from './approval/ChangeRequestApprovalComponent';
import ApprovalUserSelect from './ApprovalUserSelect';
import { ChangeRequestApi } from '@api/ChangeRequestApi';
import useMutate from '@core/hooks/useMutate';
import { ChangeRequestApprovalStatusEnum } from '@core/schema/ChangeRequestApproval';
import { ChangeRequestTransitionModel } from '@core/schema/ChangeRequest';
import { useDisclosure } from '@mantine/hooks';
import { useSelector } from 'react-redux';
import { getCurrentUser } from '@slices/CurrentUserSlice';
import { ChangeRequestRoleUser } from '@core/schema/ChangeRequestRoleUser';
import { MappedUser } from './ChangeRequestDetailRole';
import styled from '../ChangeRequestDetail.module.scss';
import { useChangeRequestApprovalContext } from './approval/ChangeRequestApprovalContext';

export const RenderCAB: React.FC = () => {
  const {
    cabGroupIdx,
    cabGroupUserIdx,
    changeFlowNodeId,
    control,
    handleCheckboxChange,
    handleOpenApprovalResults,
    mode,
    modeApproval,
    refetchChangeRequestRoles,
    roleIdx,
    workflowIdx,
  } = useChangeRequestApprovalContext();
  const userInfo = useSelector(getCurrentUser).userInfo;

  const { getValues } = useFormContext<ChangeRequestRoleList>();
  const roleUserPath = `roles.${roleIdx}.workflows.${workflowIdx}.groups.${cabGroupIdx}.users.${cabGroupUserIdx}` as const;
  const changeRequestRoleUser = getValues(`${roleUserPath}`) as ChangeRequestRoleUser;
  const changeNodeName = changeRequestRoleUser.changeNodeName;
  const approvalRequestId = changeRequestRoleUser.changeRequestApproval?.id;
  const changeRequestApproval = changeRequestRoleUser.changeRequestApproval || WAITING_APPROVAL;

  const changeApprovalStatusConfig = statusConfig[changeRequestApproval.overAllStatus];
  const [currentApproval, setCurrentApproval] = useState<ChangeRequestTransitionModel>({
    changeRequestApprovalId: 0,
    status: ChangeRequestApprovalStatusEnum.Enum.TOBE_SEND,
  });
  const [openedApprovalModal, { close: closeApprovalModal, open: openApprovalModal }] = useDisclosure(false);
  const { mutate: approvalRequest } = useMutate(ChangeRequestApi.processChangeRequestApproverTransition, {
    successNotification: `Proccess successfully`,
    errorNotification: (data) => ({ message: data.message }),
    onSuccess: () => {
      closeApprovalModal();
      refetchChangeRequestRoles();
    },
  });
  const handleApproval = () => {
    approvalRequest(currentApproval);
  };

  const isNotActiveAction =
    !approvalRequestId ||
    changeRequestApproval.overAllStatus !== ChangeRequestApprovalStatusEnum.Enum.PENDING_APPROVAL ||
    changeRequestRoleUser.username !== userInfo?.userName;
  const [comment, setComment] = useState('');

  return (
    <>
      <KanbanTable
        data={[changeRequestRoleUser]}
        columns={[
          {
            name: 'role',
            title: 'ID',
            width: '5%',
            customRender: (_, row) => (
              <Flex align='center' gap='xs'>
                <Checkbox
                  onChange={(e) => {
                    const selectedUser: MappedUser = {
                      username: row.username,
                      displayUsername: row.displayUsername,
                      usernameActive: row.usernameActive,
                    };
                    handleCheckboxChange(selectedUser, e.currentTarget.checked);
                  }}
                />
                <KanbanText className={styled.roleName}>{cabGroupUserIdx + 1}</KanbanText>
              </Flex>
            ),
          },
          {
            name: 'cab',
            title: 'CAB',
            customRender: () => (
              <ApprovalUserSelect
                workflowIdx={workflowIdx}
                roleIdx={roleIdx}
                control={control}
                mode={mode}
                cabGroupIdx={cabGroupIdx}
                cabGroupUserIdx={cabGroupUserIdx}
                changeFlowNodeId={changeFlowNodeId}
                modeApproval={modeApproval}
              />
            ),
          },
          {
            name: 'node',
            title: 'Node',
            customRender: () => <KanbanText>{changeNodeName}</KanbanText>,
          },
          ...(modeApproval === ChangeRequestApprovalMode.APPROVAL
            ? [
                {
                  name: 'status',
                  title: 'Status',
                  customRender: () => (
                    <Badge
                      color={changeApprovalStatusConfig.color}
                      radius='xs'
                      style={{ textTransform: 'none' }}
                      leftSection={changeApprovalStatusConfig.icon}>
                      {changeApprovalStatusConfig.label}
                    </Badge>
                  ),
                },
                {
                  name: 'sentOn',
                  title: 'Sent On',
                  customRender: () => (
                    <KanbanText>
                      {changeRequestApproval.lastApprovalDate ? new Date(changeRequestApproval.lastApprovalDate).toLocaleDateString() : '-'}
                    </KanbanText>
                  ),
                },
                {
                  name: 'actOn',
                  title: 'Act On',
                  customRender: () => (
                    <KanbanText>
                      {changeRequestApproval.createdDate ? new Date(changeRequestApproval.createdDate).toLocaleDateString() : '-'}
                    </KanbanText>
                  ),
                },
                {
                  name: 'comment',
                  title: 'Comment',
                  width: '10%',
                  customRender: () => (
                    <KanbanIconButton size='sx' variant='subtle' onClick={handleOpenApprovalResults}>
                      <IconMessage size={20} />
                    </KanbanIconButton>
                  ),
                },
                {
                  name: 'actions',
                  title: 'Actions',
                  width: '10%',
                  customRender: () => (
                    <KanbanSelect
                      mb={0}
                      placeholder='Actions'
                      data={APPROVAL_ACTION_OPTIONS}
                      disabled={isNotActiveAction}
                      onChange={(status) => {
                        if (status === ChangeRequestApprovalStatusEnum.Enum.ACCEPT || status === ChangeRequestApprovalStatusEnum.Enum.REJECT) {
                          setCurrentApproval({
                            changeRequestApprovalId: Number(approvalRequestId),
                            status: status,
                          });
                          openApprovalModal();
                        }
                      }}
                    />
                  ),
                },
              ]
            : []),
        ]}
        pagination={{ enable: false }}
        topBar={{ enable: false }}
        bottomBar={{ enable: false }}
      />
      <KanbanModal
        title=''
        size='80%'
        h='80%'
        opened={openedApprovalModal}
        actions={<KanbanButton onClick={handleApproval}>Confirm</KanbanButton>}
        onClose={closeApprovalModal}>
        <KanbanTextarea
          h='50%'
          value={comment}
          onChange={(e) => {
            const val = e.currentTarget.value;
            setComment(val);
            setCurrentApproval((prev) => ({
              ...prev,
              approvalComment: val,
            }));
          }}
          placeholder='Enter your comments here...'
        />
      </KanbanModal>
    </>
  );
};
