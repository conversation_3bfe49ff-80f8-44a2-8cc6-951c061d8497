import { z } from 'zod';
import { ChangeStageTypeEnum } from '@common/constants/ChageStageType';
import { AuditSchema } from '@core/schema/Common';
import { TemplateCustomFieldTypeEnum, TemplateFieldItem } from '@core/schema/ChangeTemplate';
import { CustomFieldSchema } from '@core/schema/CustomField';
import { ChangeRequestApprovalResultSchema } from './ChangeRequestApproval';

export const ChangeRequestFieldItemSchema = z
  .object({
    customFieldId: z.number(),
    required: z.boolean().nullish(),
    customFieldType: TemplateCustomFieldTypeEnum.nullish(),
    horizontalCoordinate: z.number().nullish(),
    verticalCoordinate: z.number().nullish(),
    width: z.number().nullish(),
    height: z.number().nullish(),
    isReusable: z.optional(z.boolean()).nullable(),
    isNew: z.optional(z.boolean()).nullable(),
  })
  .merge(CustomFieldSchema.omit({ id: true }));
export type ChangeRequestFieldItem = z.infer<typeof ChangeRequestFieldItemSchema>;

export const ChangeRequestSchema = z
  .object({
    id: z.number(),
    templateName: z.string().nullish(),
    notice: z.string().nullish(),
    changeTemplateId: z.number(),
    stage: ChangeStageTypeEnum,
    title: z.string(),
    description: z.string().nullish(),
    coordinator: z.string().nullish(),
    hasNote: z.boolean().nullish(),
  })
  .merge(AuditSchema);

export const ChangeRequestModelSchema = z
  .object({
    id: z.number(),
    changeTemplateId: z.number(),
    templateName: z.string().nullish(),
    notice: z.string().nullish(),
    stage: ChangeStageTypeEnum,
    title: z.string().nullish(),
    description: z.string().nullish(),
    coordinator: z.string().nullish(),
    fields: z.array(ChangeRequestFieldItemSchema).nullish(),
    changeStatusId: z.number().nullish(),
    changeFlowNodeId: z.string().nullish(),
    flowStages: z.array(ChangeStageTypeEnum).nullish(),
  })
  .merge(AuditSchema);

export type ChangeRequest = z.infer<typeof ChangeRequestSchema>;
export type ChangeRequestModel = z.infer<typeof ChangeRequestModelSchema>;

export function convertTemplateFieldsToChangeRequestFields(fields: TemplateFieldItem[]): ChangeRequestFieldItem[] {
  return fields.map(({ iconName: _, ...rest }) => ChangeRequestFieldItemSchema.parse(rest));
}

export const ChangeRequestTransitionSchema = z
  .object({
    changeStatusId: z.number().nullish(),
    changeStatusName: z.string().nullish(),
    approvedUser: z.string().nullish(),
    approvalComment: z.string().nullish(),
  })
  .merge(ChangeRequestApprovalResultSchema.partial());

export type ChangeRequestTransitionModel = z.infer<typeof ChangeRequestTransitionSchema>;
