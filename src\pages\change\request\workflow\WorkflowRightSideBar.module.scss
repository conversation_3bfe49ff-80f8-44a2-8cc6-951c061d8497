.addButtonContainer {
  justify-content: flex-end;
  position: absolute;
  top: 80px;
  right: 20px;
  z-index: 5;
}

.sidePanel {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 300px;
  z-index: 10;
  background: white;
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow-y: auto;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  padding: 16px;
}

.panelHeader {
  margin-bottom: 12px;
}

.panelContent {
  gap: 16px;
}

.searchInput {
  input {
    border: 2px solid #6200EE;
    border-radius: 4px;
  }
}

.nodeWorkflow {
  cursor: pointer;
  padding: 12px;
  border-radius: 8px;
  transition: all 0.2s ease-in-out;
  background-color: transparent;
  border: 1px solid transparent;

  &.hovered {
    background-color: #f8f9fa;
    border-color: #6200EE;
  }

  .nodeIcon {
    width: 24px;
    height: 24px;
  }

  .nodeContent {
    flex: 1;
  }

  .nodeTitle {
    // Custom styles for title
  }

  .nodeDescription {
    // Custom styles for description
  }
}

.noResults {
  text-align: center;
  padding: 48px 0;
}