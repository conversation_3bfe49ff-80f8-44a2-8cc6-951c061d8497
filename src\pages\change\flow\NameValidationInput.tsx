import React, { useState, useEffect } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { KanbanInput } from 'kanban-design-system';
import { COMMON_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { ChangeFlowModel } from '@models/ChangeFlowModel';
import useMutate from '@core/hooks/useMutate';
import { ChangeFlowApi } from '@api/ChangeFlowApi';

interface NameValidationInputProps {
  form: UseFormReturn<ChangeFlowModel>;
  id: number | undefined;
  name?: string;
}

export const NameValidationInput: React.FC<NameValidationInputProps> = ({ form, id, name }) => {
  const [nameToCheck, setNameToCheck] = useState<string>('');
  const [isNameTaken, setIsNameTaken] = useState<boolean | undefined>(undefined);

  const { data, mutate } = useMutate((params: { id: number | undefined; name: string }) => ChangeFlowApi.existsByName(params), {
    successNotification: { enable: false },
    errorNotification: { enable: false },
  });

  const { clearErrors, setError, setValue } = form;

  useEffect(() => {
    if (name && name.trim() !== nameToCheck) {
      setNameToCheck(name.trim());
      setIsNameTaken(undefined);
      clearErrors('name');
    }
  }, [name, nameToCheck, clearErrors, setValue, form]);

  useEffect(() => {
    if (data) {
      setIsNameTaken(data.data || false);
    }
  }, [data]);

  useEffect(() => {
    if (isNameTaken === true) {
      setError('name', { type: 'manual', message: 'This name is existed' });
    } else {
      clearErrors('name');
    }
  }, [isNameTaken, setError, clearErrors]);

  const handleNameBlur = (event: React.FocusEvent<HTMLInputElement>) => {
    const newName = event.target.value.trim();
    if (!newName) {
      setIsNameTaken(undefined);
      clearErrors('name');
      setNameToCheck('');
      setValue('name', '', { shouldValidate: false });
      return;
    }

    if (newName !== nameToCheck) {
      setNameToCheck(newName);
      setValue('name', newName, { shouldValidate: false });
      mutate({ id: id, name: newName });
    }
  };

  return (
    <KanbanInput
      withAsterisk
      label='Change Flow Name'
      {...form.register('name')}
      maxLength={COMMON_MAX_LENGTH}
      onBlur={handleNameBlur}
      required
      error={form.formState.errors.name?.message}
    />
  );
};
