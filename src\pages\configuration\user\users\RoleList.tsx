import React, { useEffect, useMemo, useState } from 'react';
import { getDefaultTableAffected, KanbanButton, KanbanIconButton, KanbanText } from 'kanban-design-system';
import { Box, Flex, Pill } from '@mantine/core';
import useFetch from '@core/hooks/useFetch';
import { useDisclosure } from '@mantine/hooks';
import { IconPlus, IconReload } from '@tabler/icons-react';
import { UserModel } from '@models/UserModel';
import { UseFormReturn } from 'react-hook-form';
import { UserRole } from '@core/schema/Role';
import { LOAD_ITEM_MAX_LENGTH } from '@common/constants/ValidationConstant';
import { ItemCheck, DEFAULT_ITEM_CHECK } from '@core/schema/Common';
import UserRoleTableComponent from './component/RoleTableComponent';
import { UserApi } from '@api/UserApi';
import AmtModal from '@components/AmtModal';

interface RoleListWithPaginationProps {
  username: string;
  form: UseFormReturn<UserModel>;
}

export const RoleListWithPagination: React.FC<RoleListWithPaginationProps> = ({ form, username }) => {
  const [roles, setRoles] = useState<UserRole[]>([]);
  const [page, setPage] = useState(1);

  const [selectedRoles, setSelectedRoles] = useState<ItemCheck<UserRole>>(DEFAULT_ITEM_CHECK);
  const [toInsertRoles, setToInsertRoles] = useState<ItemCheck<UserRole>>(DEFAULT_ITEM_CHECK);
  const [toDeleteRoles, setToDeleteRoles] = useState<ItemCheck<UserRole>>(DEFAULT_ITEM_CHECK);

  const [tempToInsertRoles, setTempToInsertRoles] = useState<ItemCheck<UserRole>>({});
  const [tempToDeleteRoles, setTempToDeleteRoles] = useState<ItemCheck<UserRole>>({});

  const [openedRoles, { close: closeRoles, open: openRoles }] = useDisclosure(false);

  const sortedInserts = useMemo(() => {
    return Object.values(toInsertRoles).sort((a, b) => a.roleName.toLowerCase().localeCompare(b.roleName.toLowerCase()));
  }, [toInsertRoles]);

  const { data, isLoading } = useFetch(
    UserApi.findAllSelectedRolesByUserName(username, {
      ...getDefaultTableAffected(),
      page: page,
      rowsPerPage: LOAD_ITEM_MAX_LENGTH,
    }),
    { enabled: !!username },
  );

  useEffect(() => {
    if (data?.data?.content) {
      if (page === 1) {
        setRoles(data.data.content);
      } else {
        setRoles((prev) => [...prev, ...(data?.data?.content || [])]);
      }
    }
  }, [data, page]);

  const loadMore = () => {
    setPage((prev) => prev + 1);
  };

  useEffect(() => {
    const formattedGroups = Object.values(toInsertRoles);
    form.setValue('rolesToInsert', formattedGroups);
  }, [toInsertRoles, form]);

  useEffect(() => {
    const formattedGroups = Object.values(toDeleteRoles);
    form.setValue('rolesToDelete', formattedGroups);
  }, [toDeleteRoles, form]);

  useEffect(() => {
    setTempToDeleteRoles(toDeleteRoles);
  }, [toDeleteRoles]);

  const handleCancelChangeRoles = () => {
    setTempToDeleteRoles(toDeleteRoles);
    setTempToInsertRoles(toInsertRoles);
    closeRoles();
  };

  const handleSaveChangeGroups = () => {
    setToInsertRoles(tempToInsertRoles);
    setToDeleteRoles(tempToDeleteRoles);
    closeRoles();
  };

  return (
    <>
      <Box mt='md'>
        <Flex direction={'row'} align={'center'} gap={'xs'}>
          <KanbanText fw={'500'}>Role List</KanbanText>
          <KanbanIconButton size='sm' variant='filled' onClick={openRoles}>
            <IconPlus />
          </KanbanIconButton>
          <AmtModal
            title=''
            size={'80%'}
            opened={openedRoles}
            actions={
              <KanbanButton variant='filled' onClick={handleSaveChangeGroups}>
                Save
              </KanbanButton>
            }
            onClose={handleCancelChangeRoles}>
            <UserRoleTableComponent
              username={username}
              selecteds={selectedRoles}
              toDeletes={tempToDeleteRoles}
              toInserts={tempToInsertRoles}
              setToInserts={setTempToInsertRoles}
              setToDeletes={setTempToDeleteRoles}
              setSelecteds={setSelectedRoles}
            />
          </AmtModal>
        </Flex>
        <Flex wrap={'wrap'} gap={'xs'} mt='xs'>
          {sortedInserts.map((item) => (
            <Pill
              onRemove={() =>
                setToInsertRoles((prev) => {
                  const newRoles = { ...prev };
                  delete newRoles[item.roleId];
                  return newRoles;
                })
              }
              withRemoveButton
              size='md'
              bg='var(--mantine-color-green-1)'
              bd='1px solid var(--mantine-color-green-1)'
              key={item.id}>
              {item.roleName}
            </Pill>
          ))}

          {roles.map(
            (group) =>
              !toDeleteRoles[group.roleId] && (
                <Pill
                  onRemove={() =>
                    setToDeleteRoles((prev) => {
                      const newRoles = { ...prev };
                      newRoles[group.roleId] = group;
                      return newRoles;
                    })
                  }
                  withRemoveButton
                  size='md'
                  bg='white'
                  bd='1px solid var(--mantine-color-primary-2)'
                  key={group.roleId}>
                  {group.roleName}
                </Pill>
              ),
          )}

          {data?.data && !data.data.last && (
            <KanbanButton size='xs' variant='light' onClick={loadMore} disabled={isLoading}>
              <IconReload size={16} style={{ marginRight: 5 }} />
            </KanbanButton>
          )}
        </Flex>
      </Box>
    </>
  );
};
